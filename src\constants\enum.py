from enum import Enum
from dataclasses import dataclass
from typing import Tuple

class QueueProcessingStatus(str, Enum):
    QUEUED = "queued"
    PROCESSING = "processing"
    PROCESSED = "processed"
    PARTIALLY_PROCESSED = "partial"
    ERROR = "error"
    ABORTED = "aborted"
    NO_FORMS = "no-forms"

class DrakeProcessingStatus(str, Enum):
    WAITING = "waiting"
    QUEUED = "queued"
    PROCESSING = "in-progress"
    PROCESSED = "complete"
    PARTIALLY_PROCESSED = "partial"
    ERROR = "error"
    ABORTED = "aborted"
    FALLBACK = "fallback"

class Tag(Enum):
    USERS = "USER"
    CLIENTS = "CLIENT"
    FILES = "FILE"
    PAGES = "PAGE"
    FORMS = "FORM"
    API_KEYS = "API_KEY"
    STATS = "STATS"

class UserRole(str, Enum):
    ADMIN = "admin"
    USER = "user"

class InviteStatus(str, Enum):
    PENDING = "pending"
    ACCEPTED = "accepted"

class OTPType(str, Enum):
    FORGET_PASSWORD = "FORGET_PASSWORD"
    TWO_FACTOR_AUTH = "TWO_FACTOR_AUTH"

class TwoFactorAuthTypes(str, Enum):
    EMAIL = "EMAIL"
    SMS = "SMS"

class UpdateClientJsonType(str, Enum):
    ADD_FORM_TYPE = "add_form_type"
    MODIFY = "modify"
    APPEND = "append"
    DELETE = "delete"
    
class LLM_PROVIDER(Enum):
    OPENAI_GPT_4O = "gpt-4o"
    GEMINI_1_5_PRO = "gemini-2.5-flash"
    HF_QWEN_2_5_VL= "qwen/qwen2.5-vl-72b-instruct"
    OR_QWEN_2_5_VL= "qwen/qwen2.5-vl-72b-instruct"

    # GEMINI_1_5_PRO = "gemini-2.0-pro-exp-02-05"
    

class FormTypeWithDesc(Enum):
    FORM_1098_E = {
        "form_name": "Form 1098-E",
        "description": "This form, officially titled the Student Loan Interest Statement, is used to report interest paid on student loans. It helps individuals claim deductions for student loan interest on their tax returns."
    }
    FORM_1098_T = {
        "form_name": "Form 1098-T",
        "description": "The Tuition Statement form is provided by educational institutions to students. It reports qualified tuition and related expenses paid, which can be used for education-related tax credits or deductions."
    }
    FORM_1098 = {
        "form_name": "Form 1098",
        "description": "Known as the Mortgage Interest Statement, this form is issued by lenders to homeowners. It shows the amount of mortgage interest paid during the year, which may be deductible on the tax return."
    }
    FORM_1099_DIV = {
        "form_name": "Form 1099-DIV",
        "description": "The Dividends and Distributions form reports income from dividends and distributions received from investments, including qualified dividends and capital gain distributions, for tax reporting purposes."
    }
    FORM_1099_INT = {
        "form_name": "Form 1099-INT",
        "description": "This form is used to report interest income earned from savings accounts, certificates of deposit, or other financial investments. It provides the total taxable and tax-exempt interest earned during the year."
    }
    FORM_1099_MISC = {
        "form_name": "Form 1099-MISC",
        "description": "The Miscellaneous Income form is issued for various types of income not classified under other forms, such as freelance work, rents, royalties, or awards received, and is used for tax reporting."
    }
    FORM_1099_NEC = {
        "form_name": "Form 1099-NEC",
        "description": "The Nonemployee Compensation form is specifically for reporting payments made to independent contractors or self-employed individuals. It includes income paid for services rendered."
    }
    FORM_1099_SSA = {
        "form_name": "Form 1099-SSA",
        "description": "The Social Security Benefit Statement reports the amount of Social Security benefits received during the year. It helps recipients determine how much of the benefits are taxable."
    }
    FORM_1099_B = {
        "form_name": "Form 1099-B",
        "description": "This form reports proceeds from broker and barter exchange transactions, including stock sales or other investments, for capital gains and losses calculation on the tax return."
    }
    FORM_1099_G = {
        "form_name": "Form 1099-G",
        "description": "The Statement for Certain Government Payments form reports taxable government payments such as unemployment compensation, state tax refunds, or agricultural payments received."
    }
    FORM_1099_R = {
        "form_name": "Form 1099-R",
        "description": "The form is used to report distributions from pensions, annuities, retirement accounts (like IRAs), and similar plans. It helps recipients account for taxable and non-taxable portions of distributions."
    }
    FORM_W_2 = {
        "form_name": "Form W-2",
        "description": "The Wage and Tax Statement is issued by employers to employees. It details total wages earned, taxes withheld, and other payroll information needed for individual income tax returns."
    }
    FORM_W_2G = {
        "form_name": "Form W-2G",
        "description": "This form reports gambling winnings, including prizes from lotteries, casinos, and sweepstakes, along with any federal income tax withheld on those winnings."
    }
    FORM_1095_A = {
        "form_name": "Form 1095-A",
        "description": "The Health Insurance Marketplace Statement is issued to individuals who obtained health insurance coverage through the marketplace. It provides information needed for the Premium Tax Credit."
    }
    FORM_K_1_1120_S = {
        "form_name": "Form K-1 1120-S",
        "description": "This form reports a shareholder's share of income, deductions, and credits from an S corporation. It is required for shareholders to file their tax returns accurately."
    }
    FORM_K_1_1065 = {
        "form_name": "Form K-1 1065",
        "description": "This form details a partner's share of income, deductions, and credits from a partnership. It is necessary for partners to accurately report their taxable share."
    }
    FORM_K_1_1041 = {
        "form_name": "Form K-1 1041",
        "description": "This form reports a beneficiary's share of income, deductions, and credits from a trust or estate, helping the beneficiary meet their tax reporting obligations."
    }
    FORM_1099_PATR = {
        "form_name": "Form 1099-PATR",
        "description": "This form reports taxable distributions received from cooperatives, which may be used to claim tax deductions or credits associated with cooperative income."
    }
    FORM_1099_OID = {
        "form_name": "Form 1099-OID",
        "description": "The Original Issue Discount form reports interest income resulting from the issuance of bonds, notes, or other debt instruments that were issued at a discount."
    }
    FORM_1099_LTC = {
        "form_name": "Form 1099-LTC",
        "description": "The Long-Term Care and Accelerated Death Benefits form reports payments made under long-term care insurance contracts or accelerated death benefits for terminally ill individuals."
    }
    FORM_1099_S = {
        "form_name": "Form 1099-S",
        "description": "The Proceeds From Real Estate Transactions form is issued for reporting gross proceeds from the sale or exchange of real estate, required for capital gains tax calculations."
    }
    FORM_1099_SA = {
        "form_name": "Form 1099-SA",
        "description": "The Distributions From an HSA, Archer MSA, or Medicare Advantage MSA form provides details on distributions made from these tax-advantaged medical accounts."
    }
    # FORM_5498 = {
    #     "form_name": "Form 5498",
    #     "description": "The IRA Contribution Information form details contributions made to an IRA during the year, helping individuals track their retirement savings and determine eligibility for deductions."
    # }
    FORM_5498_SA = {
        "form_name": "Form 5498-SA",
        "description": "This form provides information about contributions to Health Savings Accounts (HSAs), Archer MSAs, or Medicare Advantage MSAs, assisting with tax reporting."
    }
    FORM_1099_C = {
        "form_name": "Form 1099-C",
        "description": "The Cancellation of Debt form is used to report forgiven or canceled debt, which may be considered taxable income unless exclusions apply."
    }
    FORM_RRB_1099 = {
        "form_name": "Form RRB-1099",
        "description": "This form reports payments made by the Railroad Retirement Board, including Social Security equivalent benefits for tax reporting purposes."
    }
    FORM_RRB_1099_R = {
        "form_name": "Form RRB-1099-R",
        "description": "This form reports annuities or pensions paid by the Railroad Retirement Board, which may include taxable and non-taxable portions."
    }
    FORM_PROPERTY_TAXES = {
        "form_name": "Property Tax",
        "description": "This category represents property tax statements, used for tracking real estate tax obligations for deductions and financial planning."
    }
    FORM_CRYPTO_TAXES = {
        "form_name": "Crypto Tax",
        "description": "This category represents cryptocurrency tax reports, detailing capital gains, losses, or income derived from digital asset transactions for accurate tax filing."
    }

    FORM_CRYPTO_TAX_SIMPLE = {
        "form_name": "Crypto Tax Simple",
        "description": "This category represents cryptocurrency tax reports, detailing capital gains, losses, or income derived from digital asset transactions for accurate tax filing."
    }

    FORM_CRYPTO_TAX_COINBASE = {
        "form_name": "Crypto Tax Coinbase",
        "description": "This category represents cryptocurrency tax reports, detailing capital gains, losses, or income derived from digital asset transactions for accurate tax filing."
    }
    
    UNKNOWN_BUT_IMPORTANT = {
        "form_name": "Unknown But Important",
        "description": "This category represents a form that is currently unidentified but might still hold significant importance for tax or financial purposes."
    }
    UNKNOWN = {
        "form_name": "Unknown",
        "description": "An unidentifiable form type, often requiring further clarification or identification for appropriate handling."
    }
    INSTRUCTIONS = {
        "form_name": "Instructions",
        "description": "This represents instructional documents or explanatory materials that accompany various forms to guide proper completion and submission."
    }

class FormType(Enum):
    FORM_1098_E = "Form 1098-E" # Stundent Loan Interest Statement
    FORM_1098_T = "Form 1098-T" # Tuition Statement
    FORM_1098 = "Form 1098" # Mortgage Interest Statement
    FORM_1099_DIV = "Form 1099-DIV" # Dividends and Distributions
    FORM_1099_INT = "Form 1099-INT" # Interest Income
    FORM_1099_MISC = "Form 1099-MISC" # Miscellaneous Income
    FORM_1099_NEC = "Form 1099-NEC" # Nonemployee Compensation
    FORM_1099_SSA = "Form 1099-SSA" # Social Security Benefit Statement
    FORM_1099_B = "Form 1099-B" # Proceeds from Barter Exchange Transactions
    FORM_1099_G = "Form 1099-G" # Statements for recipients of certain government payments
    FORM_1099_R = "Form 1099-R" # Distr. from pensions, annuities, retirements etc.
    FORM_W_2 = "Form W-2" # Wage and Tax Statement
    FORM_W_2G = "Form W-2G" # Certain Gambling Winnings
    FORM_1095_A = "Form 1095-A" # Health Insurance Marketplace Statement
    FORM_K_1_1120_S = "Form K-1 1120-S" # Shareholder's share of income
    FORM_K_1_1065 = "Form K-1 1065" # Partner's share of income
    FORM_K_1_1041 = "Form K-1 1041" # Beneficiary's share of income
    FORM_1099_PATR = "Form 1099-PATR" # Taxable Distributions Received From Cooperatives
    FORM_1099_OID = "Form 1099-OID" # Original Issue Discount
    FORM_1099_LTC = "Form 1099-LTC" # Long-Term Care and Accelerated Death Benefits
    FORM_1099_S = "Form 1099-S" # Proceeds From Real Estate Transactions
    FORM_1099_SA = "Form 1099-SA" # Distributions From an HSA, Archer MSA, or Medicare Advantage MSA
    # FORM_5498 = "Form 5498" # IRA Contribution Information
    FORM_5498_SA = "Form 5498-SA" # HSA, Archer MSA, or Medicare Advantage MSA Information
    FORM_1099_C  = "Form 1099-C" # Cancellation of Debt
    FORM_RRB_1099 = "Form RRB-1099" # Payments by the Railroad Retirement Board
    FORM_RRB_1099_R = "Form RRB-1099-R" # Annuities or Pensions by the Railroad Retirement Board
    FORM_PROPERTY_TAX = "Form Property Tax" # Property Tax
    FORM_CRYPTO_TAX = "Form Crypto Tax" # Property Tax
    FORM_CRYPTO_TAX_SIMPLE = "Form Crypto Tax Simple" # Property Tax
    FORM_CRYPTO_TAX_COINBASE = "Form Crypto Tax Coinbase" # Property Tax
    UNKNOWN_BUT_IMPORTANT = "Unknown But Important"
    UNKNOWN = "Unknown"
    INSTRUCTIONS = "Instructions"

class ErrorMessage(Enum):
    UNKNOWN_FORMTYPE_PREPROCESSING = "Unknown FormType at PreProcessing stage"
    UNKNOWN_FORMTYPE_LLM_DETECTION = "Unknown FormType at LLM Form Detection"
    UNSUPPORTED_FORMAT = "UnSupported Format"
    INTERNAL_SERVER_ERROR = "Internal Server Error"
    NO_CELLS_DETECTED = "No Cells Detected"
    FAILED_TO_SAVE_MASKED_IMAGE = "Failed to save masked image"
    USER_SOME_FILES_PARTIALLY_PROCESSED = "Some files were partial/error processed."
    USER_ALL_FILES_PARTIALLY_PROCESSED = "All files were partially processed."
    USER_ERROR_PROCESSED = "All files encountered errors."
    FILES_PARTIALLY_PROCESSED = "Some pages were partially processed."
    FILES_ERROR_PROCESSED = "All pages encountered errors."


from src.config.sms import sms_configuration
from src.config.logger import logging

def get_sms_provider(provider: str):
    """
    Get the SMS provider object based on the provided provider name.

    Args:
        provider (str): Name of the SMS provider to retrieve.

    Returns:
        provider_object: The SMS provider object.

    Raises:
        ValueError: If the provided provider name is invalid.
    """
    try:
        provider_object = sms_configuration[provider]['provider']
    except Exception as e:
        logging.error(f"Invalid SMS provider: {provider}. Configuration: {sms_configuration} — {e}")
        raise ValueError(f"Invalid SMS provider: {provider}")
    return provider_object

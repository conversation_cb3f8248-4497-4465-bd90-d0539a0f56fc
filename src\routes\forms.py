import cv2  # For image processing
import os
import cv2
import base64
import json
import logging
import numpy as np
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, File, Request, status, Depends, Query, UploadFile, Body, Form as FastAPIForm
from fastapi.responses import JSONResponse

from src.models.forms import Form
from src.dto.input.FormDTOs import FormCreateRequest, FormUpdateRequest
from src.dto.output.CommonResponseModel import CommonResponseModel
from src.constants.enum import Tag, LLM_PROVIDER
from src.constants.env_constant import env_var
from src.provider.llm.vertex_ai_provider import GoogleVertexAIProvider
from src.provider.llm.llm_provider import get_llm_provider
from src.constants.constants import DEFAULT_OCR_LLM, DEFAULT_DRAKE_FORM_OCR_LLM
from src.langgraph_agents.agent import FORM_TEMPLATES, get_field_info_from_schema, validate_and_format_field
from src.utils.utility import send_images_to_openai

# Dependency to get current user (adjust path as needed)
from src.middleware.rbac_middleware import check_permission
from src.auth.auth_handler import user_jwt_required

# API key dependency for endpoints accessed by third‑party servers
from src.middleware.verify_api_key import verify_api_key

router = APIRouter(prefix="/v1/form", tags=[Tag.FORMS.value])

# -----------------------------------------------------------------------------
# CRUD Endpoints for Form Model
# -----------------------------------------------------------------------------


@router.post("/create", summary="Create new forms")
@check_permission("admin::create")
async def create_form(request: Request, payloads: List[FormCreateRequest], current_user: dict = Depends(user_jwt_required)):
    """
    Create new forms record.

    Roles with access:
    - Admin: Can create forms.
    Create a new form record.
    """
    try:
        form_obj_arr = []
        for payload in payloads:
            if Form.get(form_name=payload.form_name):
                continue
            form_data = {
                "form_name": payload.form_name,
                "description": payload.description,
            }
            form_obj = Form.create(**form_data)
            form_obj_arr.append(form_obj)
        response = CommonResponseModel(
            success=True, message="Forms created successfully.")
        return JSONResponse(status_code=status.HTTP_201_CREATED, content=response.dict())
    except Exception as e:
        logging.error(f"Error creating form: {e}")
        response = CommonResponseModel(
            success=False, message="Failed to create forms.")
        return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


@router.get("/get-form/{form_id}", summary="Retrieve a form by its ID")
async def get_form(form_id: str, current_user: dict = Depends(user_jwt_required)):
    """
    Retrieve a single form by its ID.

    Roles with access:
    - User: Can view forms associated with their account.
    Retrieve a single form by its ID.
    """
    try:
        form_obj = Form.get(id=form_id)
        if not form_obj:
            response = CommonResponseModel(
                success=False, message="Form not found.")
            return JSONResponse(status_code=status.HTTP_404_NOT_FOUND, content=response.dict())
        response = CommonResponseModel(
            success=True, message="Form retrieved successfully.", data=form_obj.to_dict())
        return JSONResponse(status_code=status.HTTP_200_OK, content=response.dict())
    except Exception as e:
        logging.error(f"Error retrieving form: {e}")
        response = CommonResponseModel(
            success=False, message="Failed to retrieve form.")
        return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


@router.get("/get-forms", summary="Retrieve forms with optional filters")
async def get_forms(
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1),
    form_name: Optional[str] = Query(None, description="Filter forms by name"),
    current_user: dict = Depends(user_jwt_required)
):
    """
    Retrieve forms with optional filtering by form name.
    """
    try:
        query = {}
        if form_name:
            query["form_name__icontains"] = form_name

        total_records = Form.objects(**query).count()
        total_pages = (total_records + page_size - 1) // page_size
        skip = (page - 1) * page_size
        
        order = "asc"
        sort_by = "preference"
        sort_order = "+" if order == "asc" else "-"
        sort_criteria = f"{sort_order}{sort_by}"
        forms = Form.objects(**query).order_by(sort_criteria).skip(skip).limit(page_size)
        forms_data = [f.to_dict() for f in forms]
        response_data = {
            "forms": forms_data,
            "total_records": total_records,
            "current_page": page,
            "total_pages": total_pages
        }
        response = CommonResponseModel(
            success=True, message="Forms retrieved successfully.", data=response_data)
        return JSONResponse(status_code=status.HTTP_200_OK, content=response.dict())
    except Exception as e:
        logging.error(f"Error retrieving forms: {e}")
        response = CommonResponseModel(
            success=False, message="Failed to retrieve forms.")
        return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


@router.patch("/update", summary="Update a form")
@check_permission("admin::update")
async def update_form(request: Request, payload: FormUpdateRequest, current_user: dict = Depends(user_jwt_required)):
    """
    Update an existing form.

    Roles with access:
    - User: Can update forms associated with their account.
    Update an existing form.
    """
    try:
        form_obj = Form.get(id=payload.form_id)
        if not form_obj:
            response = CommonResponseModel(
                success=False, message="Form not found.")
            return JSONResponse(status_code=status.HTTP_404_NOT_FOUND, content=response.dict())

        if payload.form_name is not None:
            form_obj.form_name = payload.form_name
        if payload.description is not None:
            form_obj.description = payload.description

        form_obj.save()
        response = CommonResponseModel(
            success=True, message="Form updated successfully.", data=form_obj.to_dict())
        return JSONResponse(status_code=status.HTTP_200_OK, content=response.dict())
    except Exception as e:
        logging.error(f"Error updating form: {e}")
        response = CommonResponseModel(
            success=False, message="Failed to update form.")
        return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


@router.delete("/delete/{form_id}", summary="Delete a form")
@check_permission("admin::delete")
async def delete_form(request: Request, form_id: str, current_user: dict = Depends(user_jwt_required)):
    """
    Delete a form by its ID.

    Roles with access:
    - Admin: Can delete any form.
    Delete a form by its ID.
    """
    try:
        form_obj = Form.get(id=form_id)
        if not form_obj:
            response = CommonResponseModel(
                success=False, message="Form not found.")
            return JSONResponse(status_code=status.HTTP_404_NOT_FOUND, content=response.dict())
        form_obj.delete()
        response = CommonResponseModel(
            success=True, message="Form deleted successfully.")
        return JSONResponse(status_code=status.HTTP_200_OK, content=response.dict())
    except Exception as e:
        logging.error(f"Error deleting form: {e}")
        response = CommonResponseModel(
            success=False, message="Failed to delete form.")
        return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


# -----------------------------------------------------------------------------
# Endpoints for Processing and Validating Forms (Protected by API Key)
# -----------------------------------------------------------------------------

def crop_inner_box(img):
    """
    Crop the inner box of an image by detecting a quadrilateral region.
    """
    if img is None:
        raise ValueError("Error loading image.")
    try:
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        thresh = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                       cv2.THRESH_BINARY_INV, 11, 2)
        contours, _ = cv2.findContours(
            thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        approx_quads = []
        for cnt in contours:
            epsilon = 0.02 * cv2.arcLength(cnt, True)
            approx = cv2.approxPolyDP(cnt, epsilon, True)
            if len(approx) == 4:
                approx_quads.append(approx)
        approx_quads = sorted(approx_quads, key=cv2.contourArea, reverse=True)
        if approx_quads:
            best_quad = approx_quads[0]
            x_min = min(point[0][0] for point in best_quad)
            y_min = min(point[0][1] for point in best_quad)
            x_max = max(point[0][0] for point in best_quad)
            y_max = max(point[0][1] for point in best_quad)
            margin_px = 27
            x_min = max(x_min - margin_px, 0)
            y_min = max(y_min - margin_px, 0)
            x_max = min(x_max + margin_px, img.shape[1])
            y_max = min(y_max + margin_px, img.shape[0])
            cropped_img = img[y_min:y_max, x_min:x_max]
            return cropped_img
        else:
            raise ValueError("No suitable quadrilateral was found.")
    except Exception as e:
        import traceback
        traceback.print_exc()
        return img


@router.post("/process-drake-table", summary="Process Drake Table for OCR", dependencies=[Depends(verify_api_key)])
async def process_drake_table(
    base64_images: Optional[List[str]] = FastAPIForm(
        None, description="List of base64-encoded images"),
    crop_flag: bool = FastAPIForm(...),
    prompt: Optional[str] = FastAPIForm(None)
):
    """
    Process one or more form images (base64) for OCR extraction. Crops images if requested,
    then sends them to the LLM provider for processing.
    This endpoint is protected by an API key.
    """
    try:
        processed_images = []
        total_images_count = len(base64_images) if base64_images else 0
        if total_images_count == 0:
            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={"error": "At least one image must be provided."}
            )
        for b64_img in base64_images:
            if crop_flag:
                decoded = base64.b64decode(b64_img)
                np_arr = np.frombuffer(decoded, np.uint8)
                image_cv2 = cv2.imdecode(np_arr, cv2.IMREAD_COLOR)
                cropped = crop_inner_box(image_cv2)
                b64_img = base64.b64encode(cv2.imencode(
                    '.png', cropped)[1]).decode("utf-8")
            processed_images.append(b64_img)

        # Send processed images and prompt to LLM provider (e.g., OpenAI)
        response = send_images_to_openai(
            prompt=prompt,
            image_paths=None,
            base64_images=processed_images
        )
        try:
            cleaned_response_string = response.replace(
                "```json", "").replace("```", "").strip()
            processed_data = json.loads(cleaned_response_string)
        except json.JSONDecodeError:
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={"error": "Invalid JSON response from LLM provider."}
            )
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={"processed_data": processed_data}
        )
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"error": f"An error occurred: {str(e)}"}
        )


@router.post("/process-drake-form", summary="Process Drake Form for OCR", dependencies=[Depends(verify_api_key)])
async def process_drake_form(
    image: UploadFile = File(None),
    base64_image: str = Body(None),
    form_name: str = Body(None),
    user_id: str = Body(None),
    output_schema: str = Body(None)
):
    """
    Process and validate a form image for OCR extraction. Accepts an image file or a base64 string,
    then validates the extracted data against a provided template.
    This endpoint is protected by an API key.
    """
    try:
        if image:
            image_data = await image.read()
            base64_image = base64.b64encode(image_data).decode("utf-8")
        if not base64_image:
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={
                    "error": "Either an image file or a base64 image string must be provided."}
            )
        # Load the template schema from output_schema if provided; otherwise, fallback to a pre-defined template
        if output_schema:
            template = json.loads(output_schema)
        else:
            template = FORM_TEMPLATES.get(form_name)
            if not template:
                return JSONResponse(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    content={"error": "Schema not found for the given form name."}
                )
        model = get_llm_provider(
            DEFAULT_DRAKE_FORM_OCR_LLM, {"user_id": user_id})
        response = None
        if DEFAULT_DRAKE_FORM_OCR_LLM == LLM_PROVIDER.OPENAI_GPT_4O:
            image_url = f"data:image/png;base64,{base64_image}"
            image_prompt = {
                "type": "text",
                "text": f"""
                    You are an OCR assistant tasked with extracting values from a tax form image.
                    Match the extracted values to the fields in the following template:
                    {json.dumps(template, indent=2)}
                    Return the data as JSON.
                """
            }
            # Here, we assume your model.invoke method takes a list of prompts and returns a response
            response = model.invoke(
                [{"type": "image_url", "image_url": {"url": image_url}}, image_prompt])
            response = response.content
        elif DEFAULT_DRAKE_FORM_OCR_LLM == LLM_PROVIDER.GEMINI_1_5_PRO:
            image_path = f"temp_image_{user_id}.png"
            with open(image_path, "wb") as image_file:
                image_file.write(base64.b64decode(base64_image))
            prompt_text = f"""
                You are an OCR assistant tasked with extracting values from a tax form image.
                Template: {json.dumps(template, indent=2)}
                Return the data as JSON.
            """
            response = model.generate_content(
                image_path=image_path, prompt=prompt_text)
            os.remove(image_path)
            try:
                if not response.get("success", False):
                    vertex_model = GoogleVertexAIProvider(
                        project_id=env_var["PROJECT_ID"], model_id=DEFAULT_OCR_LLM.value)
                    response = vertex_model.generate_content(
                        image_path=image_path, prompt=prompt_text)
                if not response.get("success", False):
                    raise Exception(response.get(
                        "error", "Unknown error during OCR"))
                response = response["response"]["candidates"][0]["content"]["parts"][0]['text']
            except Exception as e:
                return JSONResponse(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    content={
                        "messages": {"status": "error", "message": str(e)},
                        "processed_data": template,
                        "isSuccess": False,
                        "error_message": str(e)
                    }
                )
        try:
            cleaned_response_string = response.replace(
                "```json", "").replace("```", "").strip()
            processed_data = json.loads(cleaned_response_string)
        except json.JSONDecodeError:
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={"error": "Invalid JSON response from LLM provider."}
            )

        # Validate and format the extracted fields based on the template
        def process_nested_dict(data: Dict[str, Any], parent_path: str = "") -> Dict[str, Any]:
            formatted = {}
            for key, value in data.items():
                current_path = f"{parent_path}.{key}" if parent_path else key
                if isinstance(value, dict) and not ('value' in value or 'type' in value):
                    formatted[key] = process_nested_dict(value, current_path)
                else:
                    if isinstance(value, dict) and value.get('type') == 'array':
                        array_data = value.get('value', [])
                        processed_array = [
                            process_nested_dict(item, f"{current_path}[{idx}]") if isinstance(
                                item, dict) else item
                            for idx, item in enumerate(array_data)
                        ]
                        formatted[key] = {"type": value["type"], "sequence": value.get(
                            "sequence"), "value": processed_array}
                    elif isinstance(value, list):
                        processed_array = [
                            process_nested_dict(item, f"{current_path}[{idx}]") if isinstance(
                                item, dict) else item
                            for idx, item in enumerate(value)
                        ]
                        formatted[key] = {
                            "type": "array", "sequence": None, "value": processed_array}
                    else:
                        field_type, sequence = get_field_info_from_schema(
                            current_path, template)
                        formatted[key] = validate_and_format_field(
                            value, field_type, sequence)
            return formatted

        validated_data = process_nested_dict(processed_data)
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={"processed_data": validated_data}
        )
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"error": f"An error occurred: {str(e)}"}
        )

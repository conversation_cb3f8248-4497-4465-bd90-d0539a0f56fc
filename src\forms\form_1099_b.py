from typing import List, Optional
from enum import Enum
from pydantic import BaseModel, Field
from src.forms.base_form import (
    EINField,
    TextField,
    YearField,
    CurrencyField,
    DateField,
    CheckboxField,
    FormDetails,
    FloatField,
    ZipCodeField
)


class TermType(str, Enum):
    SHORT_TERM = "short term"
    LONG_TERM = "long term"
    UNDETERMINED_TERM = "undetermined term"


class TaxSlotType(str, Enum):
    COVERED_TAX_SLOTS = "covered tax slots"
    NON_COVERED_TAX_SLOTS = "non-covered tax slots"


class PartType(str, Enum):
    PART_1 = "part 1"
    PART_2 = "part 2"
    PART_3 = "part 3"


class BoxType(str, Enum):
    BOX_A = "box A"
    BOX_B = "box B"
    BOX_C = "box C"
    BOX_D = "box D"
    BOX_E = "box E"
    BOX_F = "box F"

class SummaryRow(BaseModel):
    proceeds: FloatField = Field(default_factory=lambda: FloatField(type="float"), description="Total Proceeds")
    cost_basis: CurrencyField = Field(default_factory=lambda: CurrencyField(type="currency"), description="Total Cost Basis")
    market_discount: CurrencyField = Field(default_factory=lambda: CurrencyField(type="currency"), description="Total Market Discount")
    wash_sale_loss_disallowed: FloatField = Field(default_factory=lambda: FloatField(type="float"), description="Total Wash Sale Loss Disallowed. It can either be positive or negative so capture the -ve sign if present")
    net_gain_or_loss: FloatField = Field(default_factory=lambda: FloatField(type="float"), description="Total Net Gain or Loss. It can either be positive or negative so capture the -ve sign if present")

class Summary(BaseModel):
    short_A_or_short_term_transaction_for_covered_tax_lots: SummaryRow = Field(
        default_factory=SummaryRow, 
        # description="Short Term Covered (Box A) - Short-term transactions for covered tax lots"
    )
    short_B_or_short_term_transaction_for_noncovered_tax_lots: SummaryRow = Field(
        default_factory=SummaryRow, 
        # description="Short Term Non-Covered (Box B) - Short-term transactions for noncovered tax lots"
    )
    short_C: SummaryRow = Field(
        default_factory=SummaryRow, 
        # description="Short Term (Box C) - Short-term transactions where Form 1099-B is not received"
    )
    total_short_term: SummaryRow = Field(
        default_factory=SummaryRow, 
        # description="Total Short-Term - Aggregated total of all short-term transactions"
    )

    long_D_or_long_term_transaction_for_covered_tax_lots: SummaryRow = Field(
        default_factory=SummaryRow, 
        # description="Long Term Covered (Box D) - Long-term transactions for covered tax lots"
    )
    long_E_or_long_term_transaction_for_noncovered_tax_lots: SummaryRow = Field(
        default_factory=SummaryRow, 
        # description="Long Term Non-Covered (Box E) - Long-term transactions for noncovered tax lots"
    )
    long_F: SummaryRow = Field(
        default_factory=SummaryRow, 
        # description="Long Term (Box F) - Long-term transactions where Form 1099-B is not received"
    )
    total_long_term: SummaryRow = Field(
        default_factory=SummaryRow, 
        # description="Total Long-Term - Aggregated total of all long-term transactions"
    )

    undetermined_B_or_E: SummaryRow = Field(
        default_factory=SummaryRow, 
        # description="Undetermined (Box B or E) - Basis not reported to the IRS"
    )
    undetermined_C_or_F_undetermined_transaction_for_noncovered_tax_lots: SummaryRow = Field(
        default_factory=SummaryRow, 
        # description="Undetermined (Box C or F) - Transactions where Form 1099-B is not received"
    )
    total_undetermined_term: SummaryRow = Field(
        default_factory=SummaryRow, 
        # description="Total Undetermined Term - Aggregated total of all undetermined transactions"
    )

    grand_total: SummaryRow = Field(
        default_factory=SummaryRow, 
        # description="Grand Total - Overall total of all short-term, long-term, and undetermined transactions"
    )



class PayerInformation(BaseModel):
    payer_name: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_street_address: TextField = Field(
        default_factory=lambda: TextField(type="text"), description="The payer's street address as listed on the form without city, state, zipcode."
    )
    payer_state: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_city: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_zip_code: ZipCodeField = Field(
        default_factory=lambda: ZipCodeField(type="zipcode"))
    # telephone_number: ContactField = Field(
    #     default_factory=lambda: ContactField(type="contact"))
    tin: EINField = Field(default_factory=lambda: EINField(type="einfield"))
    rtn_optional: Optional[TextField] = Field(
        default_factory=lambda: TextField(type="text")
    )


class TransactionDetail(BaseModel):
    date_sold_or_disposed: DateField = Field(default_factory=lambda: DateField(
        type="date"), description="Date Sold or Disposed (Box 1c)")
    quantity: FloatField = Field(default_factory=lambda: FloatField(
        type="float"), description="Quantity of securities sold")
    proceeds: FloatField = Field(default_factory=lambda: FloatField(
        type="float"), description="Proceeds (Box 1d)")
    date_acquired: DateField = Field(default_factory=lambda: DateField(
        type="date"), description="Date Acquired (Box 1b)")
    cost_or_other_basis: CurrencyField = Field(default_factory=lambda: CurrencyField(
        type="currency"), description="Cost or Other Basis (Box 1e)")
    accrued_market_discount: CurrencyField = Field(default_factory=lambda: CurrencyField(
        type="currency"), description="Accrued Market Discount (W) (Box 1g)")
    wash_sale_loss_disallowed: CurrencyField = Field(default_factory=lambda: CurrencyField(
        type="currency"), description="Wash Sale Loss Disallowed (D) (Box 1g)")
    gain_or_loss: FloatField = Field(default_factory=lambda: FloatField(
        type="float"), description="Gain or Loss, along with - sign in case of loss")
    additional_notes: TextField = Field(default_factory=lambda: TextField(
        type="text"), description="Additional Notes")


class TotalRow(BaseModel):
    # total_quantity: FloatField = Field(default_factory=lambda: FloatField(type="float"), description="Total Quantity of securities sold")
    total_proceeds_reported_gross_or_net: FloatField = Field(
        default_factory=lambda: FloatField(type="float"), description="Total Proceeds (Box 1d)")
    total_cost_or_other_basis: CurrencyField = Field(default_factory=lambda: CurrencyField(
        type="currency"), description="Total Cost or Other Basis (Box 1e)")
    total_accrued_market_discount: CurrencyField = Field(default_factory=lambda: CurrencyField(
        type="currency"), description="Total Accrued Market Discount (W) (Box 1g)")
    total_wash_sale_loss_disallowed: CurrencyField = Field(default_factory=lambda: CurrencyField(
        type="currency"), description="Total Wash Sale Loss Disallowed (D) (Box 1g)")
    total_gain_or_loss: FloatField = Field(default_factory=lambda: FloatField(
        type="float"), description="Total Gain or Loss, along with - sign in case of loss")


class Transaction(BaseModel):
    type_of_term: TermType = Field(...)
    type_of_tax_slots: TaxSlotType = Field(...)
    type_of_part: PartType = Field(...)
    type_of_box: BoxType = Field(...)
    description_of_property: TextField = Field(
        default_factory=lambda: TextField(type="text")
    )
    cusip_number: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    symbol: TextField = Field(default_factory=lambda: TextField(type="text"))
    transactions: List[TransactionDetail] = Field(default_factory=list)
    totals: TotalRow = Field(default_factory=TotalRow)


class Form1099B(BaseModel):
    form_details: FormDetails = Field(default_factory=FormDetails)
    payer_information: PayerInformation = Field(
        default_factory=PayerInformation)
    transaction_details: List[Transaction] = Field(default_factory=list)
    summary: Summary = Field(default_factory=Summary)

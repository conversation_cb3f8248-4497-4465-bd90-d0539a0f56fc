from fastapi import APIRouter, status, Depends, Query
from fastapi.responses import JSONResponse
from typing import Optional
from bson import ObjectId
from math import ceil
import logging

from src.provider.pubsub import get_pubsub_provider
from src.models.pages import Page
from src.dto.input.FileDTOs import FileCreateRequest, FileFallbackRequest, FileUpdateRequest, PresignedUrlRequest
from src.dto.output.CommonResponseModel import CommonResponseModel
from src.constants.enum import DrakeProcessingStatus, Tag, QueueProcessingStatus, UserRole
from src.constants.env_constant import env_var
from src.models.files import File
from src.models.clients import Client

# Import the user_jwt_required dependency function (assuming you already have this implemented)
from src.auth.auth_handler import user_jwt_required

# Import the presigned URL utility functions
from src.utils.presigned_url import generate_upload_presigned_url, generate_preview_presigned_url
from src.utils.encrypt_decrypt_data import deterministic_encrypt

router = APIRouter(prefix="/v1/file", tags=[Tag.FILES.value])

# -----------------------------------------------------------------------------
# CRUD Endpoints for File
# -----------------------------------------------------------------------------


@router.post("/create", summary="Create a new file")
async def create_file(payload: FileCreateRequest, current_user: dict = Depends(user_jwt_required)):
    """
    Create a new file record.

    Roles with access:
    - User: Can create files associated with their clients.
    Create a new file record.
    """
    try:
        # Ensure the client exists and is associated with the user
        if UserRole.ADMIN.value not in current_user["roles"]:
            client = Client.get(
                id=payload.client_id, user=ObjectId(current_user["id"]))
        else:
            client = Client.get(id=payload.client_id)

        if not client:
            response = CommonResponseModel(
                success=False, message="Associated client not found.")
            return JSONResponse(status_code=status.HTTP_404_NOT_FOUND, content=response.dict())

        file_data = {
            "file_name": payload.file_name,
            "input_file_path": payload.input_file_path,
            "masked_file_path": payload.masked_file_path,
            "message": payload.message,
            "status": QueueProcessingStatus.QUEUED.value,
            "client": ObjectId(client["id"]),
            "form_types_per_page": payload.form_types_per_page
        }
        file_obj = File.create(**file_data)
        response = CommonResponseModel(
            success=True, message="File created successfully.", data=file_obj.to_dict())
        return JSONResponse(status_code=status.HTTP_201_CREATED, content=response.dict())
    except Exception as e:
        logging.error(f"Error creating file: {e}")
        response = CommonResponseModel(
            success=False, message="Error creating file.", data=str(e))
        return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


@router.get("/get-file/{file_id}", summary="Retrieve a file by its ID")
async def get_file(file_id: str, current_user: dict = Depends(user_jwt_required)):
    """
    Retrieve a single file by its ID.

    Roles with access:
    - User: Can view files associated with their clients.
    Retrieve a single file by its ID.
    """
    try:
        file_obj = File.get(id=file_id)
        if not file_obj:
            response = CommonResponseModel(
                success=False, message="File not found.")
            return JSONResponse(status_code=status.HTTP_404_NOT_FOUND, content=response.dict())

        if UserRole.ADMIN.value not in current_user["roles"]:
            client = Client.get(
                id=file_obj.client["id"], user=ObjectId(current_user["id"]))
            if not client:
                response = CommonResponseModel(
                    success=False, message="Associated client not found.")
                return JSONResponse(status_code=status.HTTP_404_NOT_FOUND, content=response.dict())

        response = CommonResponseModel(
            success=True, message="File retrieved successfully.", data=file_obj.to_dict())
        return JSONResponse(status_code=status.HTTP_200_OK, content=response.dict())
    except Exception as e:
        logging.error(f"Error retrieving file: {e}")
        response = CommonResponseModel(
            success=False, message="Error retrieving file.", data=str(e))
        return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


@router.get("/get-files", summary="Retrieve files with filters")
async def get_files(
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1),
    client_id: Optional[str] = Query(None, description="Filter by client ID"),
    status_filter: Optional[str] = Query(
        None, description="Filter by file status"),
    file_name: Optional[str] = Query(None, description="Filter by file name"),
    sort_by: Optional[str] = Query(
        "created_at", description="Field to sort by (e.g., 'created_at', 'name')"),
    order: Optional[str] = Query(
        "asc", description="Sort order ('asc' for ascending, 'desc' for descending)"),
    current_user: dict = Depends(user_jwt_required)
):
    """
    Retrieve files with optional filters. The files are those associated with clients created by the user.
    """
    try:
        query = {}
        if client_id:
            query["client"] = ObjectId(client_id)
        if status_filter:
            query["status"] = status_filter
        if file_name:
            query["file_name__icontains"] = file_name

        if UserRole.ADMIN.value not in current_user["roles"]:
            client = Client.get(
                id=client_id, user=ObjectId(current_user["id"]))
            if not client:
                response = CommonResponseModel(
                    success=False, message="Associated client not found.")
                return JSONResponse(status_code=status.HTTP_404_NOT_FOUND, content=response.dict())

        total_files = File.objects(**query).count()
        total_pages = ceil(total_files / page_size)
        skip = (page - 1) * page_size

        # Determine sorting order
        sort_order = "+" if order == "asc" else "-"
        sort_criteria = f"{sort_order}{sort_by}"

        files = File.objects(
            **query).order_by(sort_criteria).skip(skip).limit(page_size)
        files_data = [file_obj.to_dict() for file_obj in files]
        response_data = {
            "files": files_data,
            "total_files": total_files,
            "current_page": page,
            "total_pages": total_pages
        }
        response = CommonResponseModel(
            success=True, message="Files retrieved successfully.", data=response_data)
        return JSONResponse(status_code=status.HTTP_200_OK, content=response.dict())
    except Exception as e:
        logging.error(f"Error retrieving files: {e}")
        response = CommonResponseModel(
            success=False, message="Error retrieving files.", data=str(e))
        return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


@router.patch("/update", summary="Update a file")
async def update_file(payload: FileUpdateRequest, current_user: dict = Depends(user_jwt_required)):
    """
    Update an existing file's details.

    Roles with access:
    - User: Can update files associated with their clients.
    Update an existing file's details.
    """
    try:
        file_obj = File.get(id=payload.file_id)
        if not file_obj:
            response = CommonResponseModel(
                success=False, message="File not found.")
            return JSONResponse(status_code=status.HTTP_404_NOT_FOUND, content=response.dict())

        if UserRole.ADMIN.value not in current_user["roles"]:
            client = Client.get(
                id=file_obj.client["id"], user=ObjectId(current_user["id"]))
            if not client:
                response = CommonResponseModel(
                    success=False, message="Associated client not found.")
                return JSONResponse(status_code=status.HTTP_404_NOT_FOUND, content=response.dict())
        else:
            client = Client.get(id=file_obj.client["id"])

        # Update fields if provided
        if payload.file_name is not None:
            file_obj.file_name = payload.file_name
        if payload.input_file_path is not None:
            file_obj.input_file_path = payload.input_file_path
        if payload.masked_file_path is not None:
            file_obj.masked_file_path = payload.masked_file_path
        if payload.message is not None:
            file_obj.message = payload.message
        if payload.form_types_per_page is not None:
            file_obj.form_types_per_page = payload.form_types_per_page

        file_obj.last_updated_by = ObjectId(current_user["id"])
        file_obj.save()

        client.last_updated_by = ObjectId(current_user["id"])
        client.save()

        response = CommonResponseModel(
            success=True, message="File updated successfully.", data=file_obj.to_dict())
        return JSONResponse(status_code=status.HTTP_200_OK, content=response.dict())
    except Exception as e:
        logging.error(f"Error updating file: {e}")
        response = CommonResponseModel(
            success=False, message="Error updating file.", data=str(e))
        return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


@router.delete("/delete-file/{file_id}", summary="Delete a file")
async def delete_file(file_id: str, current_user: dict = Depends(user_jwt_required)):
    """
    Delete a file by its ID.

    Roles with access:
    - Admin: Can delete any file.
    Delete a file by its ID.
    """
    try:
        file_obj = File.get(id=file_id)
        if not file_obj:
            response = CommonResponseModel(
                success=False, message="File not found.")
            return JSONResponse(status_code=status.HTTP_404_NOT_FOUND, content=response.dict())

        if UserRole.ADMIN.value not in current_user["roles"]:
            client = Client.get(
                id=file_obj.client["id"], user=ObjectId(current_user["id"]))
            if not client:
                response = CommonResponseModel(
                    success=False, message="Associated client not found.")
                return JSONResponse(status_code=status.HTTP_404_NOT_FOUND, content=response.dict())

        file_obj.delete()
        response = CommonResponseModel(
            success=True, message="File deleted successfully.")
        return JSONResponse(status_code=status.HTTP_200_OK, content=response.dict())
    except Exception as e:
        logging.error(f"Error deleting file: {e}")
        response = CommonResponseModel(
            success=False, message="Error deleting file.", data=str(e))
        return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


@router.post("/fallback", summary="Trigger fallback processing for files")
async def fallback_files(payload: FileFallbackRequest, current_user: dict = Depends(user_jwt_required)):
    """
    Trigger fallback processing for files where OCR has failed or needs reprocessing.

    Roles with access:
    - User: Can trigger fallback processing for files associated with their clients.

    """
    try:
        if UserRole.ADMIN.value not in current_user["roles"]:
            client = Client.get(
                id=payload.client_id, user=ObjectId(current_user["id"]))
            if not client:
                response = CommonResponseModel(
                    success=False, message="Associated client not found.")
                return JSONResponse(status_code=status.HTTP_404_NOT_FOUND, content=response.dict())
        else:
            client = Client.get(id=payload.client_id)

        previous_drake_status = DrakeProcessingStatus.PROCESSED.value

        if client:
            if client.queue_status in [QueueProcessingStatus.PROCESSED.value, QueueProcessingStatus.PARTIALLY_PROCESSED.value, QueueProcessingStatus.ERROR.value] \
                    and client.drake_status in [DrakeProcessingStatus.WAITING.value, DrakeProcessingStatus.PROCESSED.value, DrakeProcessingStatus.PARTIALLY_PROCESSED.value, DrakeProcessingStatus.ERROR.value]:
                previous_drake_status = client.drake_status
                client.queue_status = QueueProcessingStatus.QUEUED.value
                client.drake_status = DrakeProcessingStatus.WAITING.value
            else:
                response = CommonResponseModel(
                    success=False, message="Client is not in a valid state for fallback processing.")
                return JSONResponse(status_code=status.HTTP_400_BAD_REQUEST, content=response.dict())

            # Update is_review_required if not already true
            if not client.is_review_required:
                client.is_review_required = payload.is_review_required if payload.is_review_required else False
                if client.is_review_required:
                    client.is_reviewed = False

            if client.is_aborted:
                client.is_aborted = False

            client.last_updated_by = ObjectId(current_user["id"])
        else:
            response = CommonResponseModel(
                success=False, message="Client not found.")
            return JSONResponse(status_code=status.HTTP_404_NOT_FOUND, content=response.dict())

        file_responses = []

        for file_id in payload.file_ids:
            try:
                file_obj = File.get(id=file_id)
                if not file_obj:
                    file_response = CommonResponseModel(
                        success=False,
                        message=f"File with id {file_id} not found.",
                        data=None
                    )
                    file_responses.append(file_response.dict())
                    continue

                # Process only if the file status indicates an error
                if file_obj.status != QueueProcessingStatus.ERROR.value:
                    file_response = CommonResponseModel(
                        success=False,
                        message=f"File with id {file_id} is not eligible for fallback processing.",
                        data=None
                    )
                    file_responses.append(file_response.dict())
                    continue

                # Update file status to queued
                file_obj.status = QueueProcessingStatus.QUEUED.value
                file_obj.message = ""

                # Update statuses for each page linked to the file
                pages = Page.objects(file=file_obj.id)
                for page in pages:
                    page.status = QueueProcessingStatus.QUEUED.value
                    page.last_updated_by = ObjectId(current_user["id"])
                    page.message = ""   # Clear any previous error message
                    page.save()

                # Clear file-level error message and update last_updated_by
                file_obj.error_message = ""
                file_obj.last_updated_by = ObjectId(current_user["id"])
                file_obj.save()

                file_response = CommonResponseModel(
                    success=True,
                    message=f"Fallback processing triggered successfully for file with id {file_id}.",
                    data=file_obj.to_dict()
                )
                file_responses.append(file_response.dict())

            except Exception as ex:
                logging.error(f"Error processing file {file_id}: {ex}")
                file_response = CommonResponseModel(
                    success=False,
                    message=f"Error processing fallback for file {file_id}: {str(ex)}",
                    data=None
                )
                file_responses.append(file_response.dict())

        client.save()

        # Use the Redis utility class for enqueuing the job
        pubsub_provider = get_pubsub_provider(
            env_var["DEFAULT_PUBSUB_PROVIDER"])
        job_message = {
            "name": client.name,
            "social_security_number": client.social_security_number,
            "financial_year": client.financial_year,
            "if_fallback": True,
            "previous_drake_status": previous_drake_status
        }
        pubsub_provider.enqueue_task(
            env_var['REDIS_WORKER_PATH'],
            job_message,
            env_var['REDIS_WORKER_JOB_TIMEOUT_IN_SECONDS']
        )

        overall_response = CommonResponseModel(
            success=True,
            message="Fallback processing completed for all files.",
            data=file_responses
        )
        return JSONResponse(status_code=status.HTTP_200_OK, content=overall_response.dict())
    except Exception as e:
        logging.error(f"Error triggering fallback processing: {e}")
        response = CommonResponseModel(
            success=False, message="Error triggering fallback processing.", data=str(e))
        return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


# -----------------------------------------------------------------------------
# Endpoints for Presigned URL Generation
# -----------------------------------------------------------------------------

@router.post("/generate-upload-url", summary="Generate presigned URL for file upload")
async def generate_upload_url(
    payload: PresignedUrlRequest,
    current_user: dict = Depends(user_jwt_required)
):
    """
    Generate a presigned URL for uploading a file.
    """
    try:

        if payload.client_id and len(payload.client_id) > 0:
            client = Client.get(
                id=payload.client_id,
            )
            payload.social_security_number = client.social_security_number
            payload.financial_year = client.financial_year
        else:
            payload.social_security_number = deterministic_encrypt(
                payload.social_security_number)

        urls = {
            filename: generate_upload_presigned_url(
                payload.social_security_number, payload.financial_year, filename)
            for filename in payload.filenames
        }
        return JSONResponse(status_code=status.HTTP_200_OK, content={"upload_urls": urls})
    except Exception as e:
        logging.error(f"Error generating upload URL: {e}")
        response = CommonResponseModel(
            success=False, message="Error generating upload URL.", data=str(e))
        return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


@router.get("/preview", summary="Generate presigned URL for file preview")
async def preview_file(file_path: str, current_user: dict = Depends(user_jwt_required)):
    """
    Generate a presigned URL for previewing a file.

    Roles with access:
    - User: Can generate preview URLs for files associated with their clients.
    Generate a presigned URL for previewing a file.
    """
    try:
        preview_url = generate_preview_presigned_url(file_path)
        return JSONResponse(status_code=status.HTTP_200_OK, content={"preview_url": preview_url})
    except Exception as e:
        logging.error(
            f"Error generating preview URL for file {file_path}: {e}")
        response = CommonResponseModel(
            success=False, message="Error generating preview URL.", data=str(e))
        return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())

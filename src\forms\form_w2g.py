from pydantic import BaseModel, Field
from src.forms.base_form import TextField, CurrencyField, CheckboxField, DateField, FormDetails, ZipCodeField, EINField


# Payer Information Section
class PayerInformation(BaseModel):
    payer_name: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_address: TextField = Field(
        default_factory=lambda: TextField(type="text"), description="The payer's street address as listed on the form without city, state, zipcode.")
    payer_city: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_state: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_zip_code: ZipCodeField = Field(
        default_factory=lambda: ZipCodeField(type="zipcode"))
    payer_federal_identification_number: EINField = Field(default_factory=lambda: EINField(type="einfield"))


# Financial Information Section
class FinancialInformation(BaseModel):
    gross_or_reportable_winnings: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    federal_income_tax_withheld: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    state_income_tax_withheld: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    local_income_tax_withheld: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    state_winnings: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    local_winnings: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))


# Additional Information Section
class AdditionalInformation(BaseModel):
    type_of_wager: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    date_won: DateField = Field(default_factory=lambda: DateField(type="date"))
    transaction: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    race: TextField = Field(default_factory=lambda: TextField(type="text"))
    winnings_from_identical_wagers: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    cashier: TextField = Field(default_factory=lambda: TextField(type="text"))
    window: TextField = Field(default_factory=lambda: TextField(type="text"))
    payers_state_identification_number: TextField = Field(default_factory=lambda: TextField(type="text"))
    # winner_taxpayers_identification_number: EINField = Field(default_factory=lambda: EINField(type="einfield"))
    first_id: TextField = Field(default_factory=lambda: TextField(type="text"))
    second_id: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    state_initials: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    name_of_locality: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    corrected: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    void: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))


# Main Pydantic model for Form W-2G
class FormW2G(BaseModel):
    payer_information: PayerInformation = Field(
        default_factory=PayerInformation)
    form_details: FormDetails = Field(default_factory=FormDetails)
    financial_information: FinancialInformation = Field(
        default_factory=FinancialInformation)
    additional_information: AdditionalInformation = Field(
        default_factory=AdditionalInformation)

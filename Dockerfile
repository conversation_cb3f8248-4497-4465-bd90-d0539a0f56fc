# Use an official Python runtime as a base image
FROM python:3.11-slim

# Set the working directory
WORKDIR /app

# Install system dependencies required for OpenCV, Tesseract, and additional utilities
RUN apt-get update && apt-get install -y --no-install-recommends \
    tesseract-ocr \
    tesseract-ocr-eng \
    tesseract-ocr-osd \
    libgl1-mesa-glx \
    libglib2.0-0 \
    poppler-utils \
    wget \
    && rm -rf /var/lib/apt/lists/*

# Create the tessdata directory if it doesn't exist
RUN mkdir -p /usr/share/tesseract-ocr/4.00/tessdata/

# Ensure `eng.traineddata` exists in the tessdata directory
RUN wget -qO /usr/share/tesseract-ocr/4.00/tessdata/eng.traineddata \
    https://github.com/tesseract-ocr/tessdata/raw/main/eng.traineddata

# Ensure `osd.traineddata` exists in the tessdata directory
RUN wget -qO /usr/share/tesseract-ocr/4.00/tessdata/osd.traineddata \
    https://github.com/tesseract-ocr/tessdata/raw/main/osd.traineddata

# Set TESSDATA_PREFIX environment variable
ENV TESSDATA_PREFIX=/usr/share/tesseract-ocr/4.00/tessdata/

# Copy the requirements file into the container
COPY requirements.txt .

# Install Python dependencies
RUN pip3 install -r requirements.txt --use-deprecated=legacy-resolver

# Copy the application code
COPY . .

# Expose the port on which the server runs
EXPOSE 8000

# Set environment variables for virtual environment
ENV PYTHONUNBUFFERED=1

# Entry point to start the worker
CMD ["/bin/sh", "-c", "\
    export OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES && \
    export $(grep -v '^#' .env | xargs) && \
    echo 'Starting server...' && \
    python server.py & \
    sleep 5 && \
    echo 'Starting worker...' && \
    rq worker-pool -u redis://:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}/0 ${PRIORITY_QUEUE_NAME} ${QUEUE_NAME} -n ${NO_OF_WORKERS_REDIS}"]
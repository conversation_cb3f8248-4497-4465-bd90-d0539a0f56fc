#DEFAULTS
DEFAULT_MAIL_PROVIDER=
DEFAULT_QUEUE_PROVIDER=
DEFAULT_BUCKET_PROVIDER=
DEFAULT_PUBSUB_PROVIDER=
DEFAULT_SMS_PROVIDER=
DEFAULT_PASSWORD=
DEFAULT_MAX_CLIENTS_UPLOAD_AT_A_TIME=
DEFAULT_MAX_CLIENTS_UPLOAD_AT_A_TIME_ADMIN=
DEFAULT_MAX_PRIORITY_CLIENTS_TO_PROCESS_AT_A_TIME=
DEFAULT_MAX_PRIORITY_CLIENTS_TO_PROCESS_AT_A_TIME_ADMIN=
DEFAULT_ADMIN_EMAIL=
DEFAULT_ADMIN_PASSWORD=


# openai 
OPENAI_API_KEY=

# RABBIT MQ
RABBITMQ_HOST=
RABBITMQ_PORT=
QUEUE_NAME=
RABBITMQ_USERNAME=
RABBITMQ_PASSWORD=

#GCP BUCKET
GOOGLE_SERVICE_ACCOUNT_JSON_ENCODED=
GCS_BUCKET_NAME=
PUBLIC_BUCKET_NAME=
BUCKET_BASE_URL=

#DB
MONGO_URI=
MONGO_DB=

#
PORT=

LANGSMITH_TRACING=
LANGSMITH_ENDPOINT=
LANGSMITH_API_KEY=
LANGSMITH_PROJECT=

# Directories
BASE_DOWNLOAD_DIR=
BASE_STORE_DIR=

#JWT
JWT_TOKEN_ALGORITHM=
JWT_TOKEN_SECRET_KEY=

# REDIS
REDIS_HOST=
REDIS_PORT=
REDIS_PASSWORD=
REDIS_WORKER_PATH=
REDIS_WORKER_JOB_TIMEOUT_IN_SECONDS=
NO_OF_WORKERS_REDIS=
# export OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES

# gemini 
GEMINI_API_KEY=

# vertex
GOOGLE_VERTEX_JSON=
PROJECT_ID=

# EXPIRATION CONFIGS
JWT_TOKEN_EXPIRATION_TIME_IN_HOURS=
OTP_EXPIRATION_TIME_IN_MINUTES=

# FASTAPI MAIL CONFIG
FASTAPI_MAIL_USERNAME=
FASTAPI_MAIL_PASSWORD=
FASTAPI_MAIL_FROM=

# POSTMARK MAIL CONFIG
POSTMARK_SERVER_TOKEN=
POSTMARK_MAIL_FROM=

# TWILIO SMS CONFIG
TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=
TWILIO_PHONE_NUMBER=

# Frontend URL
FRONTEND_URL=

#DRAKE QUEUE
RDP_QUEUE_NAME=

OR_TOKEN=

#CANOPY
CANOPY_CLIENT_ID=
CANOPY_CLIENT_SECRET=
CANOPY_REGISTERED_REDIRECT_URL=
CANOPY_TOKEN_URL=
CANOPY_API_BASE_URL=

#Encryption/Decryption
AES_KEY=
AES_IV=
from pydantic import BaseModel, Field
from src.forms.base_form import TextField, DateField, CurrencyField, CheckboxField, FormDetails, ZipCodeField, EINField


class FilerInformation(BaseModel):
    filer_name: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    filer_street_address: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    filer_city: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    filer_state: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    filer_zip_code: ZipCodeField = Field(
        default_factory=lambda: ZipCodeField(type="zipcode"))
    filer_tin_or_identification_number: EINField = Field(default_factory=lambda: EINField(type="einfield"))


class FinancialFields(BaseModel):
    date_of_closing: DateField = Field(
        default_factory=lambda: DateField(type="date"))
    gross_proceeds: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    address_or_legal_description:TextField = Field(
        default_factory= lambda:TextField(type="text")
    )
    city_or_legal_description:TextField = Field(
        default_factory= lambda:TextField(type="text")
    )
    state_or_legal_description:TextField = Field(
        default_factory= lambda:TextField(type="text")
    )
    zip_code_or_legal_description: ZipCodeField = Field(
        default_factory=lambda: ZipCodeField(type="zipcode"))
    buyers_part_of_real_estate_tax: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    
class AdditionalFields(BaseModel):
    corrected: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    transferorr_received_or_will_receive_property_or_services_as_part_of_the_consideration_if_checked: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    checked_if_transferor_is_a_foreign_person_or_non_resident_alien_foreign_partnership_or_foreign_state_or_foreign_trust: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    omb_number: TextField = Field(
        default_factory=lambda: TextField(type="text"))


# Main Pydantic model for Form 1099-INT
class Form1099S(BaseModel):
    filer_information: FilerInformation = Field(
        default_factory=FilerInformation)
    form_details: FormDetails = Field(default_factory=FormDetails)
    financial_fields: FinancialFields = Field(default_factory=FinancialFields)
    additional_fields: AdditionalFields = Field(
        default_factory=AdditionalFields)

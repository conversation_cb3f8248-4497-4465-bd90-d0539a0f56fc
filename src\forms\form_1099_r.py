from typing import List
from pydantic import BaseModel, Field
from src.forms.base_form import ContactField, EINField, TextField, YearField, CurrencyField, CheckboxField, FormDetails, DateField, ZipCodeField


class PayerInformation(BaseModel):
    payer_name: TextField = Field(
        default_factory=lambda: TextField(type="text"),
        description="The full name of the payer institution responsible for the distribution."
    )
    payer_street_address: TextField = Field(
        default_factory=lambda: TextField(type="text"),
        description="The payer's street address as listed on the form without city, state, zipcode."
    )
    payer_city: TextField = Field(
        default_factory=lambda: TextField(type="text"),
        description="City where the payer organization is located."
    )
    payer_state: TextField = Field(
        default_factory=lambda: TextField(type="text"),
        description="State where the payer organization is located."
    )
    payer_zip_code: ZipCodeField = Field(
        default_factory=lambda: ZipCodeField(type="zipcode"),
        description="The ZIP code of the payer’s address."
    )
    payer_telephone_number: ContactField = Field(
        default_factory=lambda: ContactField(type="contact"),
        description="Contact phone number for the payer organization."
    )
    payer_tin: EINField = Field(
        default_factory=lambda: EINField(type="einfield"),
        description="The payer’s Tax Identification Number (TIN)."
    )


class FinancialFields(BaseModel):
    gross_distribution: CurrencyField = Field(
        default_factory=lambda: CurrencyField( type="currency"),
        description="Total distribution amount before deductions, taxes, or withholding."
    )
    taxable_amount: CurrencyField = Field(
        default_factory=lambda: CurrencyField( type="currency"),
        description="The portion of the gross distribution that is subject to tax."
    )
    taxable_amount_not_determined: CheckboxField = Field(
        default_factory=lambda: CheckboxField( type="checkbox"),
        description="Checked if the taxable amount is not yet determined by the payer."
    )
    total_distribution: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"),
        description="Indicates whether the full amount is a total distribution."
    )
    capital_gain_included_in_taxable_amount: CurrencyField = Field(
        default_factory=lambda: CurrencyField( type="currency"),
        description="Capital gain amount included in the taxable distribution."
    )
    federal_income_tax_withheld: CurrencyField = Field(
        default_factory=lambda: CurrencyField( type="currency"),
        description="Federal tax withheld from the distribution amount."
    )
    employee_contributions_or_designated_roth_contributions_or_insurance_premiums: CurrencyField = Field(
        default_factory=lambda: CurrencyField( type="currency"),
        description="Contributions by employees or designated Roth contributions or insurance premiums."
    )
    net_unrealized_appreciation_in_employers_securities: CurrencyField = Field(
        default_factory=lambda: CurrencyField( type="currency"),
        description="Unrealized appreciation in employer-provided securities."
    )
    distribution_codes: TextField = Field(
        default_factory=lambda: TextField( type="text"),
        description="Distribution codes specifying the type of distribution made."
    )
    ira_sep_simple: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"),
        description="Checked if the distribution was from an IRA, SEP, or SIMPLE plan."
    )
    other: CurrencyField = Field(
        default_factory=lambda: CurrencyField( type="currency"),
        description="Any other distributions or amounts not classified above."
    )
    percentage_of_total_distribution: TextField = Field(
        default_factory=lambda: TextField( type="text"),
        description="The percentage of the total distribution attributed to this payment."
    )
    total_employee_contribution: CurrencyField = Field(
        default_factory=lambda: CurrencyField( type="currency"),
        description="Total contributions made by the employee over time."
    )
    amount_allocable_to_IRR_within_5_years: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"),
        description="The portion of the distribution allocable to an IRR within five years."
    )
    first_year_of_designated_roth_contributions: TextField = Field(
        default_factory=lambda: TextField( type="text"),
        description="The year the recipient started designated Roth contributions."
    )
    date_of_payment: DateField = Field(
        default_factory=lambda: DateField(type="date"),
        description="The official date the payment was made."
    )


class StateTaxInformation(BaseModel):
    state_tax_withheld: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"),
        description="State income tax withheld from the distribution amount."
    )
    state: TextField = Field(
        default_factory=lambda: TextField( type="text"),
        description="The state where the tax was withheld."
    )
    state_or_payers_state_number: TextField = Field(
        default_factory=lambda: TextField( type="text"),
        description="State number or identifier used by the payer for tax reporting purposes."
    )
    state_distribution: CurrencyField = Field(
        default_factory=lambda: CurrencyField( type="currency"),
        description="Amount distributed that is subject to state taxation."
    )


class LocalTaxInformation(BaseModel):
    local_tax_withheld: CurrencyField = Field(
        default_factory=lambda: CurrencyField( type="currency"),
        description="Local tax withheld from the distribution amount."
    )
    name_of_locality: TextField = Field(
        default_factory=lambda: TextField( type="text"),
        description="The locality where local taxes were withheld."
    )
    local_distribution: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"),
        description="The amount of distribution subject to local taxes."
    )


class AdditionalFields(BaseModel):
    corrected: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"),
        description="Indicates if this form is a corrected version of a previously filed form."
    )
    omb_number: TextField = Field(
        default_factory=lambda: TextField(type="text"),
        description="The Office of Management and Budget (OMB) control number for the form."
    )
    fatca_filing_requirement: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"),
        description="Indicates whether there is a FATCA filing requirement for foreign financial assets."
    )


class Form1099R(BaseModel):
    payer_information: PayerInformation = Field(
        default_factory=PayerInformation,
        description="Details of the payer responsible for issuing the 1099-R."
    )
    form_details: FormDetails = Field(
        default_factory=FormDetails,
        description="General information related to the form, including account number and date."
    )
    financial_fields: FinancialFields = Field(
        default_factory=FinancialFields,
        description="Financial-related fields including gross distribution, tax amounts, and contributions."
    )
    state_tax_information: List[StateTaxInformation] = Field(
        default_factory=list,
        description="Information regarding state-level tax withholding."
    )
    local_tax_information: List[LocalTaxInformation] = Field(
        default_factory=list,
        description="Information regarding local-level tax withholding."
    )
    additional_fields: AdditionalFields = Field(
        default_factory=AdditionalFields,
        description="Additional fields for corrections, FATCA filing, and form identification."
    )

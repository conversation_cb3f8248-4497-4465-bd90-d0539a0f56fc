import base64
import os
import json
import time
from typing import List, Optional
from celery import Celery
from fastapi import APIRouter, Header, Path, Request, status as HTTPSSTATUS, Depends, Query
from fastapi.responses import JSONResponse
from bson import ObjectId
import logging
from math import ceil

from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
import requests

# Importing models, constants, and dependencies from the project
from src.models.pages import Page
from src.middleware.verify_api_key import verify_api_key
from src.models.clients import Client
from src.models.files import File
from src.dto.output.CommonResponseModel import CommonResponseModel
from src.dto.input.ClientDTOs import ClientJsonUpdateRequest, ClientUpdateDrakeStatusRequest, ClientUpdateIsReviewedRequest, ClientUpdateRequest, DocumentUploadRequest, ExchangeTokenRequest, RefreshTokenRequest
from src.constants.enum import DrakeProcessingStatus, Tag, QueueProcessingStatus, UpdateClientJsonType, UserRole
from src.constants.env_constant import env_var
from mongoengine.queryset.visitor import Q

# Import the user_jwt_required dependency function
from src.auth.auth_handler import user_jwt_required

# Import the Pubsub Provider
from src.provider.pubsub import get_pubsub_provider
from src.utils.upload_and_download_file_gcp import download_blob_as_string, upload_file_to_bucket

# Initialize bucket proider
from src.provider.bucket import get_bucket_provider
from src.utils.encrypt_decrypt_data import deterministic_decrypt
bucket_provider = get_bucket_provider(env_var["DEFAULT_BUCKET_PROVIDER"])
security = HTTPBearer()

# Initialize FastAPI router with prefix '/v1/client'
router = APIRouter(prefix="/v1/client")

JWT_SECRET = env_var.get("JWT_SECRET")
JWT_ALGORITHM = env_var.get("JWT_ALGORITHM", "HS256")

redis_host = env_var['REDIS_HOST']
redis_port = env_var['REDIS_PORT']
redis_password = env_var['REDIS_PASSWORD']

broker_url = f"redis://:{redis_password}@{redis_host}:{redis_port}/0"
backend_url = broker_url  # often Celery uses the same Redis for the backend

celery_app = Celery(
    'celery_worker',  # name of this Celery app
    broker=broker_url,
    backend=backend_url
)

# --- Canopy Constants from env_var ---
# Make sure these are added to your env_constant.py or wherever env_var is populated
CANOPY_CLIENT_ID = env_var.get("CANOPY_CLIENT_ID", "")
CANOPY_CLIENT_SECRET = env_var.get("CANOPY_CLIENT_SECRET", "")
CANOPY_REGISTERED_REDIRECT_URL = env_var.get(
    "CANOPY_REGISTERED_REDIRECT_URL", "")
CANOPY_TOKEN_URL = env_var.get("CANOPY_TOKEN_URL", "")
CANOPY_API_BASE_URL = env_var.get("CANOPY_API_BASE_URL", "")

# Basic validation for required Canopy env vars - useful for debugging startup issues
if not all([CANOPY_CLIENT_ID, CANOPY_CLIENT_SECRET, CANOPY_REGISTERED_REDIRECT_URL, CANOPY_TOKEN_URL, CANOPY_API_BASE_URL]):
    logging.error(
        "Missing one or more required Canopy environment variables! Canopy proxy endpoints may fail.")
    # Consider raising an exception here if these are critical for server startup.


@router.post("/process-clients", tags=[Tag.CLIENTS.value])
async def process_client(payloads: List[DocumentUploadRequest], current_user: dict = Depends(user_jwt_required)):
    """
    Process client documents.
    Creates MongoDB entries for each file and enqueues jobs for processing.
    """
    try:
        # Check maximum clients can be upload at a time on basis of requests and client which have status as QUEUED

        if not UserRole.ADMIN.value in current_user["roles"]:
            max_clients = int(current_user.get(
                "max_clients_upload_at_a_time", env_var["DEFAULT_MAX_CLIENTS_UPLOAD_AT_A_TIME"]))
        else:
            max_clients = int(current_user.get(
                "max_clients_upload_at_a_time", env_var["DEFAULT_MAX_CLIENTS_UPLOAD_AT_A_TIME_ADMIN"]))

        clients_in_queue_or_processing = Client.objects(
            user=ObjectId(current_user["id"]),
            queue_status__in=[QueueProcessingStatus.QUEUED.value,
                              QueueProcessingStatus.PROCESSING.value]
        ).count()

        total_clients = clients_in_queue_or_processing + len(payloads)
        if total_clients > max_clients:
            number_of_clients_user_can_upload = max_clients - clients_in_queue_or_processing
            response = CommonResponseModel(
                success=False,
                message=f"You can process a maximum of {max_clients} clients at a time. You currently have space to process {number_of_clients_user_can_upload} more."
            )
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

        # Check maximum clients can be upload as a priority at a time on basis of requests and client which have status as QUEUED and is processing on priority
        current_number_of_priority_clients = Client.objects(
            user=ObjectId(current_user["id"]),
            is_priority=True,
            queue_status__in=[QueueProcessingStatus.QUEUED.value,
                              QueueProcessingStatus.PROCESSING.value]
        ).count()

        if not UserRole.ADMIN.value in current_user["roles"]:
            max_priority_clients_at_a_time = int(current_user.get(
                "max_priority_clients_to_process_at_a_time", env_var["DEFAULT_MAX_PRIORITY_CLIENTS_TO_PROCESS_AT_A_TIME"]))
        else:
            max_priority_clients_at_a_time = int(current_user.get(
                "max_priority_clients_to_process_at_a_time", env_var["DEFAULT_MAX_PRIORITY_CLIENTS_TO_PROCESS_AT_A_TIME_ADMIN"]))

        number_of_priority_clients_user_can_upload = max_priority_clients_at_a_time - \
            current_number_of_priority_clients

        # get count of client user want to process as priority using payload.is_priority
        number_of_priority_clients_user_want_to_upload = sum(
            1 for payload in payloads if payload.is_priority)

        if number_of_priority_clients_user_want_to_upload > number_of_priority_clients_user_can_upload:
            response = CommonResponseModel(
                success=False,
                message=f"You can process a maximum of {max_priority_clients_at_a_time} clients as priority at a time. \
                    You currently have space to process {number_of_priority_clients_user_can_upload} more clients as priority."
            )
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

        # create a response array for each client payload
        response_array = []

        for payload in payloads:

            if payload.client_id and len(payload.client_id) > 0:

                if not UserRole.ADMIN.value in current_user["roles"]:
                    client = Client.get(
                        id=payload.client_id, user=ObjectId(current_user["id"]))
                else:
                    client = Client.get(
                        id=payload.client_id)

                if client:
                    payload.financial_year = client.financial_year
                else:
                    response_array.append(CommonResponseModel(
                        success=False, message=f"Client {payload.client_id} not found").dict())
                    continue
            else:
                client = None

            previous_drake_status = DrakeProcessingStatus.PROCESSED.value

            is_priority_client = True if payload.is_priority else False

            if not client:
                # get last 4 digit of ssn
                client_data = {
                    "social_security_number": payload.social_security_number,
                    "name": payload.name,  # Will be encrypted in the save method
                    "financial_year": payload.financial_year,
                    "output_path": "",
                    "queue_status": QueueProcessingStatus.QUEUED.value,
                    "drake_status": DrakeProcessingStatus.WAITING.value,
                    "user": ObjectId(current_user["id"]),
                    "is_review_required": payload.is_review_req,
                    "is_reviewed": False if payload.is_review_req else True,
                    "is_priority": is_priority_client,
                    "last_updated_by": ObjectId(current_user["id"])
                }
                client = Client.create(**client_data)
            else:
                if client.queue_status == QueueProcessingStatus.QUEUED.value:
                    response_array.append(CommonResponseModel(
                        success=False, message=f"Client {client['id']} already in queue").dict())
                    continue
                elif client.queue_status == QueueProcessingStatus.PROCESSING.value:
                    response_array.append(CommonResponseModel(
                        success=False, message="Client already in processing").dict())
                    continue
                elif client.drake_status == DrakeProcessingStatus.QUEUED.value:
                    response_array.append(CommonResponseModel(
                        success=False, message=f"Client {client['id']} already in drake queue").dict())
                    continue
                elif client.drake_status == DrakeProcessingStatus.PROCESSING.value:
                    response_array.append(CommonResponseModel(
                        success=False, message="Client already processing in drake").dict())
                    continue
                else:
                    previous_drake_status = client.drake_status
                    Client.update_one(
                        query={
                            "id": ObjectId(client["id"])
                        },
                        update_data={
                            "queue_status": QueueProcessingStatus.QUEUED.value,
                            "drake_status": DrakeProcessingStatus.WAITING.value,
                            "is_review_required": payload.is_review_req,
                            "is_reviewed": False if payload.is_review_req else True,
                            "is_priority": is_priority_client,
                            "is_aborted": False if client.is_aborted else client.is_aborted,
                            "last_updated_by": ObjectId(current_user["id"])
                        },
                    )
            encrypted_ssn = client.social_security_number
            client = client.to_dict()

            for filedata in payload.filedatas:
                filename = filedata.filename
                original_filename = filedata.original_filename if filedata.original_filename else filename
                form_types_per_page_in_file = {
                    str(k): v for k, v in filedata.form_types_per_page.items()}
                full_path = f"{encrypted_ssn}/{payload.financial_year}/input/{filename}"
                file_doc = {
                    "file_name": original_filename,
                    "input_file_path": full_path,
                    "masked_file_path": "",
                    "client": ObjectId(client["id"]),
                    "status": QueueProcessingStatus.QUEUED.value,
                    "form_types_per_page": form_types_per_page_in_file,
                    "message": ""
                }
                File.create(**file_doc)

            # Use the Redis utility class for enqueuing the job
            pubsub_provider = get_pubsub_provider(
                env_var["DEFAULT_PUBSUB_PROVIDER"])
            
            # Use the original unencrypted name from the payload for the job message
            # since client.name is now encrypted in the database
            job_message = {
                "name": payload.name,  # Use unencrypted name from payload
                "social_security_number": encrypted_ssn,
                "financial_year": payload.financial_year,
                "if_fallback": False,
                "previous_drake_status": previous_drake_status
            }
            if is_priority_client:
                pubsub_provider.enqueue_priority_task(
                    env_var['REDIS_WORKER_PATH'],
                    job_message,
                    env_var['REDIS_WORKER_JOB_TIMEOUT_IN_SECONDS']
                )
            else:
                pubsub_provider.enqueue_task(
                    env_var['REDIS_WORKER_PATH'],
                    job_message,
                    env_var['REDIS_WORKER_JOB_TIMEOUT_IN_SECONDS']
                )
            response_array.append(CommonResponseModel(
                success=True, message="Client queued for processing").dict())

        if all(response['success'] for response in response_array):
            response = CommonResponseModel(
                success=True, message="Clients queued for processing", data=response_array)
        elif any(response['success'] for response in response_array):
            response = CommonResponseModel(
                success=True, message="Clients queued for processing with some errors", data=response_array)
        else:
            response = CommonResponseModel(
                success=True, message="No clients queued for processing", data=response_array)

        return JSONResponse(status_code=HTTPSSTATUS.HTTP_201_CREATED, content=response.to_dict())
    except Exception as e:
        logging.error(f"Error processing client: {e}")
        import traceback
        traceback.print_exc()
        response = CommonResponseModel(
            success=False, message="Error processing client", data=str(e))
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.to_dict())

# API to update is_reviewed status for a client


@router.post("/update-client-review/{client_id}", tags=[Tag.CLIENTS.value])
async def update_client_review(
    client_id: str,
    payload: ClientUpdateIsReviewedRequest,
    current_user: dict = Depends(user_jwt_required)
):
    try:
        if UserRole.ADMIN.value in current_user["roles"] and client_id:
            client = Client.get(id=client_id)
        else:
            client = Client.get(
                id=client_id, user=ObjectId(current_user["id"]))

        if not client:
            response = CommonResponseModel(
                success=False, message="Client not found")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

        if client.is_aborted:
            response = CommonResponseModel(
                success=False, message="Client is aborted. Cannot update review status")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

        if client.is_review_required:
            if not payload.is_reviewed:
                response = CommonResponseModel(
                    success=False, message="Client is marked as review required. Please mark it as reviewed")
                return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

            # Check if all files are processed
            if client.queue_status in [QueueProcessingStatus.PROCESSED.value, QueueProcessingStatus.PARTIALLY_PROCESSED.value] \
                    and client.drake_status in [DrakeProcessingStatus.WAITING.value, DrakeProcessingStatus.PARTIALLY_PROCESSED.value, DrakeProcessingStatus.ERROR.value]:

                # Update client is_review status
                client.is_reviewed = payload.is_reviewed
                if payload.is_reviewed:
                    client.is_review_required = False

                # Fetch output JSON from GCS
                output_path = client.output_path
                if not output_path:
                    response = CommonResponseModel(
                        success=False, message="Client output path is not set")
                    return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

                # Custom function to get file content from GCS
                json_str = download_blob_as_string(
                    output_path, env_var['GCS_BUCKET_NAME'])
                full_json = json.loads(json_str)

                file_ids = list(File.objects(
                    client=client_id).only("id").scalar("id"))

                if not file_ids:
                    return JSONResponse(
                        status_code=HTTPSSTATUS.HTTP_200_OK,
                        content=CommonResponseModel(
                            success=True, message="No files found", data={"stats": {}}).dict()
                    )

                # Step 2: Query pages with those file IDs
                page_query = {"file": {"$in": file_ids}}
                pages_cursor = Page.objects(__raw__=page_query).only(
                    "id", "forms_status").as_pymongo()

                form_status_stats = {}

                for page in pages_cursor:
                    forms_status = page.get("forms_status", {})
                    page_id = str(page.get("_id", ""))
                    if page_id not in form_status_stats:
                        form_status_stats[page_id] = {}
                    if forms_status:
                        for form_id, form_status in forms_status.items():
                            form_status_stats[page_id][form_id] = form_status

                total_form_ids = []
                for form_type, form_list in full_json.items():
                    for form in form_list:
                        if "form_details" in form and "form_id" in form["form_details"] and "value" in form["form_details"]["form_id"]:
                            form_id = form["form_details"]["form_id"]["value"]
                            page_ids = form["form_details"]["page_id"]["value"]
                            # check if form_status_stats for all page_ids and form_id is_filled status is true or false
                            filled = False
                            for page_id in page_ids:
                                if page_id in form_status_stats and form_id in form_status_stats[page_id]:
                                    if "is_filled" in form_status_stats[page_id][form_id] and form_status_stats[page_id][form_id]["is_filled"]:
                                        filled = True
                                    else:
                                        filled = False
                                        break
                            if not filled:
                                total_form_ids.append(form_id)

                # Get list of all file_ids for this client
                file_ids = [str(fid) for fid in File.objects(
                    client=client.id).only("id").scalar("id")]
                page_ids = [[] for _ in file_ids]

                try:

                    # Prepare RDP message
                    rdp_message = {
                        "gcp_json_path": client.output_path,
                        "social_security_number": client.social_security_number,
                        "social_security_number_decrypted": deterministic_decrypt(client.social_security_number),
                        "client_id": str(client.id),
                        "financial_year": client.financial_year,
                        "doc_ids": file_ids,
                        "page_ids": page_ids,
                        "processed_form_ids": total_form_ids
                    }

                    # Send task to celery
                    celery_app.send_task(
                        name="process_user_task",  # the EXACT name from the remote decorator
                        # or pass named params if needed
                        args=[rdp_message],
                        queue=env_var["RDP_QUEUE_NAME"] if env_var.get(
                            "RDP_QUEUE_NAME") else "drake-local"  # the queue to send the task to
                    )
                except Exception as e:
                    logging.error(f"Error sending task to celery: {e}")
                    response = CommonResponseModel(
                        success=False, message="Error sending task to celery", data=str(e))
                    return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())

                # Update client drake status
                client.drake_status = DrakeProcessingStatus.QUEUED.value

                client.last_updated_by = ObjectId(current_user["id"])

                # Save updated client
                client.save()

                response = CommonResponseModel(
                    success=True, message="Client review status updated successfully"
                )
                return JSONResponse(status_code=HTTPSSTATUS.HTTP_200_OK, content=response.dict())

            else:
                response = CommonResponseModel(
                    success=False, message="Client's files is not yet processed. Please wait for processing to complete.")
                return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())
        else:
            if payload.is_reviewed:
                response = CommonResponseModel(
                    success=False, message="Client is not marked as review required.")
                return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

    except Exception as e:
        logging.error(f"Error updating client: {e}")
        response = CommonResponseModel(
            success=False, message="Error updating client", data=str(e))
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


# API to fill the pages in drake for which is_filled is true in form_status key
@router.post("/fill-client-in-drake/{client_id}", tags=[Tag.CLIENTS.value])
async def fill_client_in_drake(
    client_id: str,
    current_user: dict = Depends(user_jwt_required)
):
    try:
        if UserRole.ADMIN.value in current_user["roles"] and client_id:
            client = Client.get(id=client_id)
        else:
            client = Client.get(
                id=client_id, user=ObjectId(current_user["id"]))

        if not client:
            response = CommonResponseModel(
                success=False, message="Client not found")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

        if client.is_review_required:
            response = CommonResponseModel(
                success=False, message="Client is marked as review required. Please mark as reviewed")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

        if client.is_aborted:
            response = CommonResponseModel(
                success=False, message="Client is aborted. Cannot initiate drake form-filling.")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

        # Check if all files are processed
        if client.queue_status in [QueueProcessingStatus.PROCESSED.value, QueueProcessingStatus.PARTIALLY_PROCESSED.value] and \
                client.drake_status in [DrakeProcessingStatus.PARTIALLY_PROCESSED.value, DrakeProcessingStatus.ERROR.value]:

            # Get list of all file_ids
            all_file_ids = [str(fid) for fid in File.objects(
                client=client.id).only("id").scalar("id")]

            # Filter all the pages in which page's is_filled is true in form_status key for all_file_ids
            page_query = {"file": {"$in": all_file_ids}}
            pages_cursor = Page.objects(__raw__=page_query).only(
                "id", "forms_status", "file").as_pymongo()

            # Fetch output JSON from GCS
            output_path = client.output_path
            if not output_path:
                response = CommonResponseModel(
                    success=False, message="Client output path is not set")
                return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

            # Custom function to get file content from GCS
            json_str = download_blob_as_string(
                output_path, env_var['GCS_BUCKET_NAME'])
            full_json = json.loads(json_str)

            file_ids = list(File.objects(
                client=client_id).only("id").scalar("id"))

            if not file_ids:
                return JSONResponse(
                    status_code=HTTPSSTATUS.HTTP_200_OK,
                    content=CommonResponseModel(
                        success=True, message="No files found", data={"stats": {}}).dict()
                )

            # Step 2: Query pages with those file IDs
            page_query = {"file": {"$in": file_ids}}
            pages_cursor = Page.objects(__raw__=page_query).only(
                "id", "forms_status").as_pymongo()

            form_status_stats = {}

            for page in pages_cursor:
                forms_status = page.get("forms_status", {})
                page_id = str(page.get("_id", ""))
                if page_id not in form_status_stats:
                    form_status_stats[page_id] = {}
                if forms_status:
                    for form_id, form_status in forms_status.items():
                        form_status_stats[page_id][form_id] = form_status

            total_form_ids = []
            for form_type, form_list in full_json.items():
                for form in form_list:
                    if "form_details" in form and "form_id" in form["form_details"] and "value" in form["form_details"]["form_id"]:
                        form_id = form["form_details"]["form_id"]["value"]
                        page_ids = form["form_details"]["page_id"]["value"]
                        # check if form_status_stats for all page_ids and form_id is_filled status is true or false
                        filled = False
                        for page_id in page_ids:
                            if page_id in form_status_stats and form_id in form_status_stats[page_id]:
                                if "is_filled" in form_status_stats[page_id][form_id] and form_status_stats[page_id][form_id]["is_filled"]:
                                    filled = True
                                else:
                                    filled = False
                                    break
                        if not filled:
                            total_form_ids.append(form_id)

            file_ids = []
            page_ids = []

            for page in pages_cursor:
                forms_status = page.get("forms_status", {})
                if forms_status and ("is_filled" in forms_status):
                    if not forms_status["is_filled"]:
                        page_ids.append(str(page.get("_id", "")))
                        file_ids.append(str(page.get("file", "")))

            try:

                # Prepare RDP message
                rdp_message = {
                    "gcp_json_path": client.output_path,
                    "social_security_number": client.social_security_number,
                    "social_security_number_decrypted": deterministic_decrypt(client.social_security_number),
                    "client_id": str(client.id),
                    "financial_year": client.financial_year,
                    "doc_ids": file_ids,
                    "page_ids": page_ids,
                    "processed_form_ids": total_form_ids
                }

                # Send task to celery
                celery_app.send_task(
                    name="process_user_task",  # the EXACT name from the remote decorator
                    # or pass named params if needed
                    args=[rdp_message],
                    queue=env_var["RDP_QUEUE_NAME"] if env_var.get(
                        "RDP_QUEUE_NAME") else "drake-local"  # the queue to send the task to
                )
            except Exception as e:
                logging.error(f"Error sending task to celery: {e}")
                response = CommonResponseModel(
                    success=False, message="Error sending task to celery", data=str(e))
                return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())

            # Update client drake status
            client.drake_status = DrakeProcessingStatus.QUEUED.value

            client.last_updated_by = ObjectId(current_user["id"])

            # Save updated client
            client.save()

            response = CommonResponseModel(
                success=True, message="Filling in drake initiated successfully"
            )
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_200_OK, content=response.dict())

        else:
            response = CommonResponseModel(
                success=False, message="Client's files is not yet processed. Please wait for processing to complete.")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

    except Exception as e:
        logging.error(f"Error updating client: {e}")
        response = CommonResponseModel(
            success=False, message="Error updating client", data=str(e))
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


@router.post("/update-client-json/{client_id}", tags=[Tag.CLIENTS.value])
async def update_client_json(
    client_id: str,
    updates: List[ClientJsonUpdateRequest],
    current_user: dict = Depends(user_jwt_required)
):
    try:
        if UserRole.ADMIN.value in current_user["roles"] and client_id:
            client = Client.get(id=client_id)
        else:
            client = Client.get(
                id=client_id, user=ObjectId(current_user["id"]))
        if not client:
            response = CommonResponseModel(
                success=False, message="Client not found")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

        # Status check
        if client.drake_status in [DrakeProcessingStatus.QUEUED.value, DrakeProcessingStatus.PROCESSING.value, DrakeProcessingStatus.PROCESSED.value] or \
                client.queue_status not in [QueueProcessingStatus.PARTIALLY_PROCESSED.value, QueueProcessingStatus.PROCESSED.value]:
            response = CommonResponseModel(
                success=False, message="Client is not in a valid state for updates")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

        # Fetch output JSON from GCS
        output_path = client.output_path
        if not output_path:
            response = CommonResponseModel(
                success=False, message="Client output path is not set")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

        # Custom function to get file content from GCS
        json_str = download_blob_as_string(
            output_path, env_var['GCS_BUCKET_NAME'])
        full_json = json.loads(json_str)

        for update in updates:

            # Handle different update types
            if update.update_type == UpdateClientJsonType.ADD_FORM_TYPE.value:
                if update.form_type in full_json:
                    response = CommonResponseModel(
                        success=False, message=f"Form type '{update.form_type}' already exists")
                    return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())
                full_json[update.form_type] = [update.modified_json]

            elif update.update_type == UpdateClientJsonType.APPEND.value:
                if update.form_type not in full_json:
                    response = CommonResponseModel(
                        success=False, message=f"Form type '{update.form_type}' not found")
                    return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())
                full_json[update.form_type].append(update.modified_json)

            elif update.update_type == UpdateClientJsonType.MODIFY.value:
                if update.form_type not in full_json:
                    response = CommonResponseModel(
                        success=False, message=f"Form type '{update.form_type}' not found")
                    return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

                entries = full_json[update.form_type]

                if update.arr_index is None or update.arr_index >= len(entries):
                    response = CommonResponseModel(
                        success=False, message="Invalid array index")
                    return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

                if str(entries[update.arr_index].get("form_details").get("document_id").get("value")) != update.document_id:
                    response = CommonResponseModel(
                        success=False, message="document_id mismatch")
                    return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

                # Preserve form_details
                form_details = entries[update.arr_index].get("form_details")
                updated_entry = update.modified_json
                if form_details:
                    updated_entry["form_details"] = form_details

                full_json[update.form_type][update.arr_index] = updated_entry

            elif update.update_type == UpdateClientJsonType.DELETE.value:
                if update.form_type not in full_json:
                    response = CommonResponseModel(
                        success=False, message=f"Form type '{update.form_type}' not found")
                    return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

                entries = full_json[update.form_type]

                if update.arr_index is None or update.arr_index >= len(entries):
                    response = CommonResponseModel(
                        success=False, message="Invalid array index")
                    return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

                if str(entries[update.arr_index].get("document_id")) != update.document_id:
                    response = CommonResponseModel(
                        success=False, message="document_id mismatch")
                    return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

                del full_json[update.form_type][update.arr_index]

            else:
                response = CommonResponseModel(
                    success=False, message="Invalid update type")
                return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

            # Write updated JSON to a temp file and re-upload
            temp_file_path = f"/tmp/{ObjectId()}.json"
            with open(temp_file_path, "w") as f:
                json.dump(full_json, f)

            upload_file_to_bucket(
                temp_file_path, output_path, env_var['GCS_BUCKET_NAME'])

            os.remove(temp_file_path)

            client.last_updated_by = ObjectId(current_user["id"])
            client.save()

        response = CommonResponseModel(
            success=True, message="Client output JSON(s) updated successfully")
        return JSONResponse(status_code=200, content=response.dict())

    except Exception as e:
        logging.error(f"Error updating client JSON: {e}")
        response = CommonResponseModel(
            success=False, message="Error updating client JSON", data=str(e))
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


@router.post("/abort-client", tags=[Tag.CLIENTS.value])
async def abort_client(payload: ClientUpdateRequest, current_user: dict = Depends(user_jwt_required)):
    """
    Abort a client's processing.
    Updates the client's is_aborted status to true'.
    """
    try:
        if UserRole.ADMIN.value in current_user["roles"] and payload.client_id:
            client = Client.get(id=payload.client_id)
        else:
            client = Client.get(
                id=payload.client_id, user=ObjectId(current_user["id"]))
        if not client:
            response = CommonResponseModel(
                success=False, message="Client not found.")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_404_NOT_FOUND, content=response.dict())

        if client.is_aborted:
            response = CommonResponseModel(
                success=False, message="Client already aborted.")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

        if client.queue_status in [QueueProcessingStatus.QUEUED.value, QueueProcessingStatus.PROCESSING.value, QueueProcessingStatus.PARTIALLY_PROCESSED.value, QueueProcessingStatus.PROCESSED.value] \
                and client.drake_status in [DrakeProcessingStatus.WAITING.value, DrakeProcessingStatus.QUEUED.value]:
            client.is_aborted = True
            if client.queue_status in [QueueProcessingStatus.PROCESSED.value,
                                       QueueProcessingStatus.PARTIALLY_PROCESSED.value, QueueProcessingStatus.ERROR.value,
                                       QueueProcessingStatus.NO_FORMS.value] and client.drake_status == DrakeProcessingStatus.WAITING.value:
                client.queue_status = QueueProcessingStatus.ABORTED.value
                client.drake_status = DrakeProcessingStatus.ABORTED.value
            client.last_updated_by = ObjectId(current_user["id"])
            client.save()
            response = CommonResponseModel(
                success=True, message="Client processing aborted successfully.")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_200_OK, content=response.dict())
        else:
            response = CommonResponseModel(
                success=False, message="Client is not in a valid state for aborting.")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())
    except Exception as e:
        logging.error(f"Error aborting client: {e}")
        response = CommonResponseModel(
            success=False, message="Error aborting client", data=str(e))
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


@router.get("/get-client/{client_id}", tags=[Tag.CLIENTS.value])
async def get_client(client_id: str, current_user: dict = Depends(user_jwt_required)):
    """
    Retrieve a single client by ID.

    Roles with access:
    - Admin: Can view all clients.
    - User: Can only view clients they have created.
    """
    try:
        if UserRole.ADMIN.value not in current_user["roles"]:
            client = Client.get(
                id=client_id, user=ObjectId(current_user["id"]))
        else:
            client = Client.get(id=client_id)

        if not client:
            response = CommonResponseModel(
                success=False, message="Client not found.")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_404_NOT_FOUND, content=response.dict())

        response = CommonResponseModel(
            success=True, message="Client retrieved successfully.", data=client.to_dict())
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_200_OK, content=response.dict())
    except Exception as e:
        logging.error(f"Error retrieving client: {e}")
        response = CommonResponseModel(
            success=False, message="Error retrieving client", data=str(e))
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


@router.get("/get-clients", tags=[Tag.CLIENTS.value])
async def get_clients(
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1),
    financial_year: int = Query(None, description="Filter by financial year"),
    user_id: str = Query(None, description="Filter by user ID"),
    queue_status: str = Query(None, description="Filter by queue status"),
    drake_status: str = Query(None, description="Filter by drake status"),
    name_or_ssn: str = Query(
        None, description="Filter by client's name or client's ssn"),
    sort_by: Optional[str] = Query(
        "updated_at", description="Field to sort by (e.g., 'created_at', 'name')"),
    order: Optional[str] = Query(
        "asc", description="Sort order ('asc' for ascending, 'desc' for descending)"),
    current_user: dict = Depends(user_jwt_required)
):
    """
    Roles with access:
    Retrieve all clients created by the current user with optional filters.

    - Admin: Can view all clients.
    - User: Can only view clients associated with their account.
    """
    try:
        # Start by building a Q object for the initial filter
        query_q = Q()

        if financial_year is not None:
            query_q &= Q(financial_year=financial_year)
        if queue_status is not None:
            query_q &= Q(queue_status=queue_status)
        if drake_status is not None:
            query_q &= Q(drake_status=drake_status)

        if user_id is not None:
            query_q &= Q(user=ObjectId(user_id))

        skip = (page - 1) * page_size
        if name_or_ssn:
            all_clients = Client.get_filtered_clients(query_q, name_or_ssn)
            total_clients = len(all_clients)
            total_pages = ceil(total_clients / page_size)

            # Apply sorting manually
            reverse = order == "desc"
            all_clients = sorted(
                all_clients,
                key=lambda x: getattr(x, sort_by, None),
                reverse=reverse
            )

            # Paginate
            clients = all_clients[skip: skip + page_size]
        else:
            total_clients = Client.objects(query_q).count()
            total_pages = ceil(total_clients / page_size)
            clients = Client.objects(query_q).order_by(
                f"{'' if order == 'asc' else '-'}{sort_by}"
            ).skip(skip).limit(page_size)

        clients_data = [client.to_dict() for client in clients]
        response_data = {
            "clients": clients_data,
            "total_clients": total_clients,
            "current_page": page,
            "total_pages": total_pages,
        }
        response = CommonResponseModel(
            success=True, message="Clients retrieved successfully.", data=response_data)
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_200_OK, content=response.dict())
    except Exception as e:
        logging.error(f"Error retrieving clients: {e}")
        response = CommonResponseModel(
            success=False, message="Error retrieving clients", data=str(e))
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


@router.patch("/update", tags=[Tag.CLIENTS.value])
async def update_client(payload: ClientUpdateRequest, current_user: dict = Depends(user_jwt_required)):
    """
    Update a client's details.

    Roles with access:
    - Admin: Can only update client associated with their account.
    - User: Can only update client associated with their account.
    """
    try:

        if UserRole.ADMIN.value in current_user["roles"] and payload.client_id:
            client = Client.get(id=payload.client_id)
        else:
            client = Client.get(
                id=payload.client_id, user=ObjectId(current_user["id"]))

        if not client:
            response = CommonResponseModel(
                success=False, message="Client not found.")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_404_NOT_FOUND, content=response.dict())

        # Update allowed fields if provided
        if payload.name is not None:
            # The name will be encrypted in the save method
            # If it's already encrypted, it will be re-encrypted
            client.name = payload.name
        if payload.message is not None:
            client.message = payload.message

        client.last_updated_by = ObjectId(current_user["id"])

        client.save()
        response = CommonResponseModel(
            success=True, message="Client updated successfully.", data=client.to_dict())
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_200_OK, content=response.dict())
    except Exception as e:
        logging.error(f"Error updating client: {e}")
        response = CommonResponseModel(
            success=False, message="Error updating client", data=str(e))
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


@router.delete("/delete-client/{client_id}", tags=[Tag.CLIENTS.value])
async def delete_client(client_id: str, current_user: dict = Depends(user_jwt_required)):
    """
    Delete a client's details.

    Roles with access:
    - Admin: Can delete any client.
    - User: Can only delete client associated with their account.
    """
    try:
        if UserRole.ADMIN.value in current_user["roles"] and client_id:
            client = Client.get(id=client_id)
        else:
            client = Client.get(
                id=client_id, user=ObjectId(current_user["id"]))

        if not client:
            response = CommonResponseModel(
                success=False, message="Client not found.")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_404_NOT_FOUND, content=response.dict())

        if client.queue_status in [QueueProcessingStatus.QUEUED.value, QueueProcessingStatus.PROCESSING.value] \
                and client.drake_status in [DrakeProcessingStatus.QUEUED.value, DrakeProcessingStatus.PROCESSING.value]:
            response = CommonResponseModel(
                success=False, message="Client is currently being processed. Cannot delete.")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

        bucket_provider.delete_folder(
            env_var["GCS_BUCKET_NAME"], f"{client.social_security_number}/{client.financial_year}")
        client.delete()
        response = CommonResponseModel(
            success=True, message="Client deleted successfully.")
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_200_OK, content=response.dict())
    except Exception as e:
        logging.error(f"Error deleting client: {e}")
        response = CommonResponseModel(
            success=False, message="Error deleting client", data=str(e))
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


@router.get("/get-client-info/{client_id}", tags=[Tag.CLIENTS.value], dependencies=[Depends(verify_api_key)])
async def get_client_info(client_id: str):
    """
    Retrieve a single client by ID using api key.
    """
    try:
        client = Client.get(id=client_id)

        if not client:
            response = CommonResponseModel(
                success=False, message="Client not found.")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_404_NOT_FOUND, content=response.dict())

        response = CommonResponseModel(
            success=True, message="Client retrieved successfully.", data=client.to_dict())
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_200_OK, content=response.dict())
    except Exception as e:
        logging.error(f"Error retrieving client: {e}")
        response = CommonResponseModel(
            success=False, message="Error retrieving client", data=str(e))
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())

@router.patch("/update-drake-status-jwt", tags=[Tag.CLIENTS.value], dependencies=[Depends(user_jwt_required)])
async def update_client_drake_status_from_frontend(payload: ClientUpdateDrakeStatusRequest):
    """
    Update a client's drake_status.
    """
    try:
        client = Client.objects(id=payload.client_id).first()

        if not client:
            response = CommonResponseModel(
                success=False, message="Client not found.")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_404_NOT_FOUND, content=response.dict())

        # Update allowed fields if provided
        if payload.drake_status is not None:
            client.drake_status = payload.drake_status
            if payload.drake_status == DrakeProcessingStatus.ABORTED.value:
                client.queue_status = QueueProcessingStatus.ABORTED.value

        client.save()
        response = CommonResponseModel(
            success=True, message="Client drake status updated successfully.", data=client.to_dict())
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_200_OK, content=response.dict())
    except Exception as e:
        logging.error(f"Error updating client drake status: {e}")
        response = CommonResponseModel(
            success=False, message="Error updating client drake status", data=str(e))
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())
    
@router.patch("/update-drake-status", tags=[Tag.CLIENTS.value], dependencies=[Depends(verify_api_key)])
async def update_client_drake_status(payload: ClientUpdateDrakeStatusRequest):
    """
    Update a client's drake_status.
    """
    try:
        client = Client.get(id=payload.client_id)

        if not client:
            response = CommonResponseModel(
                success=False, message="Client not found.")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_404_NOT_FOUND, content=response.dict())

        # Update allowed fields if provided
        if payload.drake_status is not None:
            client.drake_status = payload.drake_status
            if payload.drake_status == DrakeProcessingStatus.ABORTED.value:
                client.queue_status = QueueProcessingStatus.ABORTED.value

        client.save()
        response = CommonResponseModel(
            success=True, message="Client drake status updated successfully.", data=client.to_dict())
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_200_OK, content=response.dict())
    except Exception as e:
        logging.error(f"Error updating client drake status: {e}")
        response = CommonResponseModel(
            success=False, message="Error updating client drake status", data=str(e))
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


# --- Canopy Proxy Endpoints ---

@router.post("/canopy/exchange-token", tags=[Tag.CLIENTS.value])
async def exchange_canopy_token(payload: ExchangeTokenRequest):
    """
    Exchanges an authorization code from Canopy OAuth for access and refresh tokens.
    Proxies the request to the Canopy token endpoint.
    """
    if not CANOPY_TOKEN_URL or not CANOPY_REGISTERED_REDIRECT_URL or not CANOPY_CLIENT_ID or not CANOPY_CLIENT_SECRET:
        logging.error(
            "Canopy token exchange environment variables are not set.")
        response = CommonResponseModel(
            success=False, message="Server configuration error: Canopy credentials missing.")
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())

    token_url = CANOPY_TOKEN_URL

    # Prepare the form-urlencoded body
    body_params = {
        "grant_type": "authorization_code",
        "redirect_uri": CANOPY_REGISTERED_REDIRECT_URL,
        "code": payload.authorization_code,
    }

    # Prepare Basic Authentication header
    auth_string = f"{CANOPY_CLIENT_ID}:{CANOPY_CLIENT_SECRET}"
    auth_header_value = base64.b64encode(auth_string.encode()).decode()

    headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        "Authorization": f"Basic {auth_header_value}",
    }

    logging.info("Attempting to exchange code with Canopy...")
    try:
        # Use requests library for the HTTP call
        canopy_response = requests.post(
            token_url, data=body_params, headers=headers)
        response_data = canopy_response.json()

        logging.info(
            f"Canopy token response status: {canopy_response.status_code}")

        if not canopy_response.ok:
            logging.error(
                f"Canopy API Error during token exchange: {response_data}")
            # Mimic Node.js error structure
            return JSONResponse(
                status_code=canopy_response.status_code,
                content={
                    "message": response_data.get("message") or response_data.get("error_description") or "Failed to exchange token with Canopy.",
                    "canopyError": response_data,
                },
            )

        # Calculate expiry timestamp in milliseconds since epoch
        # responseData.expires_in is typically in seconds
        access_token_expires_at = int(
            time.time() * 1000) + response_data.get("expires_in", 0) * 1000
        # Log in readable format
        logging.info(
            f"Token exchanged successfully. Access token expires at: {time.ctime(access_token_expires_at / 1000)}")

        # Add the calculated expiry time to the response data
        response_data["access_token_expires_at"] = access_token_expires_at

        return JSONResponse(status_code=HTTPSSTATUS.HTTP_200_OK, content=response_data)

    except requests.exceptions.RequestException as e:
        logging.error(
            f"Network or HTTP error during Canopy token exchange: {e}")
        return JSONResponse(
            status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"message": "Internal server error during token exchange.",
                     "error_details": str(e)},
        )
    except Exception as e:
        logging.error(f"Unexpected error during Canopy token exchange: {e}")
        return JSONResponse(
            status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"message": "An unexpected error occurred.",
                     "error_details": str(e)},
        )


@router.get("/canopy/clients", tags=[Tag.CLIENTS.value])
async def get_canopy_clients(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    limit: Optional[int] = Query(10, ge=1),
    next_cursor: Optional[str] = Query(
        None, description="Cursor for pagination")
):
    """
    Fetches clients from the Canopy API.
    Proxies the request using the provided Authorization header.
    """
    if not CANOPY_API_BASE_URL:
        logging.error("Canopy API base URL environment variable is not set.")
        response = CommonResponseModel(
            success=False, message="Server configuration error: Canopy API base URL missing.")
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())

    authorization = credentials.credentials

    if not authorization or not authorization.startswith("Bearer "):
        logging.warning(
            "Attempted to access /canopy/clients without a Bearer token.")
        response = CommonResponseModel(
            success=False, message="Authorization header with Bearer token required.")
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_401_UNAUTHORIZED, content=response.dict())

    # Extract the access token from the "Bearer <token>" string
    access_token = authorization.split(" ")[1]

    clients_url = f"{CANOPY_API_BASE_URL}/public/v2/clients"

    # Prepare query parameters
    query_params = {"limit": limit}
    if next_cursor:
        query_params["next_cursor"] = next_cursor

    headers = {
        # Forward the token to Canopy
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
    }

    logging.info(
        f"Fetching clients from Canopy: {clients_url} with params {query_params}")
    try:
        # Use requests library for the HTTP call
        canopy_response = requests.get(
            clients_url, params=query_params, headers=headers)
        response_data = canopy_response.json()

        logging.info(
            f"Canopy fetch clients response status: {canopy_response.status_code}")

        if not canopy_response.ok:
            logging.error(
                f"Canopy API Error during fetch clients: {response_data}")
            # Mimic Node.js error structure
            return JSONResponse(
                status_code=canopy_response.status_code,
                content={
                    "message": response_data.get("message") or response_data.get("error_description") or "Failed to fetch clients from Canopy.",
                    "canopyError": response_data,
                },
            )
        logging.info("Clients fetched successfully from Canopy.")
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_200_OK, content=response_data)

    except requests.exceptions.RequestException as e:
        logging.error(
            f"Network or HTTP error during Canopy fetch clients: {e}")
        return JSONResponse(
            status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"message": "Internal server error while fetching clients.",
                     "error_details": str(e)},
        )
    except Exception as e:
        logging.error(f"Unexpected error during Canopy fetch clients: {e}")
        return JSONResponse(
            status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"message": "An unexpected error occurred.",
                     "error_details": str(e)},
        )


@router.get("/canopy/clients/search", tags=[Tag.CLIENTS.value])
async def search_canopy_clients(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    q: str = Query(..., description="The query string to search"),
    query_field: str = Query(...,
                             description="The field to search ('name', 'email', or 'phone')"),
    # Respect the documented default of 100
    limit: Optional[int] = Query(100, ge=1, le=200),
    next_cursor: Optional[str] = Query(
        None, description="Cursor for pagination")
):
    """
    Proxies a search query to Canopy's public client search API.
    Requires Bearer token in the Authorization header.
    """
    authorization = credentials.credentials
    if not CANOPY_API_BASE_URL:
        logging.error("Canopy API base URL is not set.")
        response = CommonResponseModel(
            success=False, message="Server configuration error: Canopy API base URL missing.")
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())

    if not authorization or not authorization.startswith("Bearer "):
        logging.warning("Access attempt without Bearer token.")
        response = CommonResponseModel(
            success=False, message="Authorization header with Bearer token required.")
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_401_UNAUTHORIZED, content=response.dict())

    access_token = authorization.split(" ")[1]

    search_url = f"{CANOPY_API_BASE_URL}/public/v2/clients/search"

    # Construct query parameters
    query_params = {
        "q": q,
        "query_field": query_field,
        "limit": limit,
    }
    if next_cursor:
        query_params["next_cursor"] = next_cursor

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
    }

    logging.info(
        f"Proxying client search to Canopy: {search_url} with params {query_params}")
    try:
        canopy_response = requests.get(
            search_url, params=query_params, headers=headers)
        response_data = canopy_response.json()

        logging.info(
            f"Search response status from Canopy: {canopy_response.status_code}")

        if not canopy_response.ok:
            logging.error(
                f"Canopy API returned error on search: {response_data}")
            return JSONResponse(
                status_code=canopy_response.status_code,
                content={
                    "message": response_data.get("message") or response_data.get("error_description") or "Failed to search clients from Canopy.",
                    "canopyError": response_data,
                },
            )

        return JSONResponse(status_code=HTTPSSTATUS.HTTP_200_OK, content=response_data)

    except requests.exceptions.RequestException as e:
        logging.error(f"Request exception during Canopy client search: {e}")
        return JSONResponse(
            status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"message": "Internal server error during search.",
                     "error_details": str(e)},
        )

    except Exception as e:
        logging.error(f"Unhandled exception during Canopy client search: {e}")
        return JSONResponse(
            status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"message": "Unexpected error occurred.",
                     "error_details": str(e)},
        )

@router.get("/canopy/clients/search", tags=[Tag.CLIENTS.value])
async def search_canopy_clients(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    q: str = Query(..., description="The query string to search"),
    query_field: str = Query(..., description="The field to search ('name', 'email', or 'phone')"),
    limit: Optional[int] = Query(100, ge=1, le=200),  # Respect the documented default of 100
    next_cursor: Optional[str] = Query(None, description="Cursor for pagination")
):
    """
    Proxies a search query to Canopy's public client search API.
    Requires Bearer token in the Authorization header.
    """
    if not CANOPY_API_BASE_URL:
        logging.error("Canopy API base URL is not set.")
        response = CommonResponseModel(success=False, message="Server configuration error: Canopy API base URL missing.")
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())
    
    authorization = credentials.credentials
    
    if not authorization or not authorization.startswith("Bearer "):
        logging.warning("Access attempt without Bearer token.")
        response = CommonResponseModel(success=False, message="Authorization header with Bearer token required.")
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_401_UNAUTHORIZED, content=response.dict())

    access_token = authorization.split(" ")[1]

    search_url = f"{CANOPY_API_BASE_URL}/public/v2/clients/search"

    # Construct query parameters
    query_params = {
        "q": q,
        "query_field": query_field,
        "limit": limit,
    }
    if next_cursor:
        query_params["next_cursor"] = next_cursor

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
    }

    logging.info(f"Proxying client search to Canopy: {search_url} with params {query_params}")
    try:
        canopy_response = requests.get(search_url, params=query_params, headers=headers)
        response_data = canopy_response.json()

        logging.info(f"Search response status from Canopy: {canopy_response.status_code}")

        if not canopy_response.ok:
            logging.error(f"Canopy API returned error on search: {response_data}")
            return JSONResponse(
                status_code=canopy_response.status_code,
                content={
                    "message": response_data.get("message") or response_data.get("error_description") or "Failed to search clients from Canopy.",
                    "canopyError": response_data,
                },
            )

        return JSONResponse(status_code=HTTPSSTATUS.HTTP_200_OK, content=response_data)

    except requests.exceptions.RequestException as e:
        logging.error(f"Request exception during Canopy client search: {e}")
        return JSONResponse(
            status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"message": "Internal server error during search.", "error_details": str(e)},
        )

    except Exception as e:
        logging.error(f"Unhandled exception during Canopy client search: {e}")
        return JSONResponse(
            status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"message": "Unexpected error occurred.", "error_details": str(e)},
        )

@router.get("/canopy/clients/{client_id}", tags=[Tag.CLIENTS.value])
async def get_canopy_client_by_id(
    client_id: str = Path(..., description="Unique identifier for the client"),
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    Proxies a client lookup by ID to Canopy's public client API.
    Requires Bearer token in the Authorization header.
    """
    if not CANOPY_API_BASE_URL:
        logging.error("Canopy API base URL is not set.")
        response = CommonResponseModel(success=False, message="Server configuration error: Canopy API base URL missing.")
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())

    authorization = credentials.credentials
    if not authorization or not authorization.startswith("Bearer "):
        logging.warning("Access attempt without Bearer token.")
        response = CommonResponseModel(success=False, message="Authorization header with Bearer token required.")
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_401_UNAUTHORIZED, content=response.dict())

    access_token = authorization.split(" ")[1]
    request_url = f"{CANOPY_API_BASE_URL}/public/v2/clients/{client_id}"

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
    }

    logging.info(f"Proxying client fetch to Canopy: {request_url}")
    try:
        canopy_response = requests.get(request_url, headers=headers)
        response_data = canopy_response.json()

        logging.info(f"Client fetch response status from Canopy: {canopy_response.status_code}")

        if not canopy_response.ok:
            logging.error(f"Canopy API returned error: {response_data}")
            return JSONResponse(
                status_code=canopy_response.status_code,
                content={
                    "message": response_data.get("message") or response_data.get("error_description") or "Failed to retrieve client from Canopy.",
                    "canopyError": response_data,
                },
            )

        return JSONResponse(status_code=HTTPSSTATUS.HTTP_200_OK, content=response_data)

    except requests.exceptions.RequestException as e:
        logging.error(f"Request exception during Canopy client fetch: {e}")
        return JSONResponse(
            status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"message": "Internal server error during client retrieval.", "error_details": str(e)},
        )

    except Exception as e:
        logging.error(f"Unhandled exception during Canopy client fetch: {e}")
        return JSONResponse(
            status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"message": "Unexpected error occurred.", "error_details": str(e)},
        )

@router.post("/canopy/refresh-token", tags=[Tag.CLIENTS.value])
async def refresh_canopy_token(payload: RefreshTokenRequest):
    """
    Refreshes an expired Canopy access token using a refresh token.
    Proxies the request to the Canopy token endpoint.
    """
    if not CANOPY_TOKEN_URL or not CANOPY_CLIENT_ID or not CANOPY_CLIENT_SECRET:
        logging.error(
            "Canopy token refresh environment variables are not set.")
        response = CommonResponseModel(
            success=False, message="Server configuration error: Canopy credentials missing.")
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())

    token_url = CANOPY_TOKEN_URL  # Refresh also uses the same token URL

    # Prepare the form-urlencoded body
    body_params = {
        "grant_type": "refresh_token",
        "refresh_token": payload.refresh_token,
        "client_id": CANOPY_CLIENT_ID,       # Included in body as per Node.js code
        "client_secret": CANOPY_CLIENT_SECRET,  # Included in body as per Node.js code
    }

    headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        # Note: Node.js code did NOT use Basic auth for refresh, only form params.
        # Sticking to that implementation detail. If Canopy docs require Basic auth,
        # you'd add it here similar to exchange-token.
    }

    logging.info("Attempting to refresh token with Canopy...")
    try:
        # Use requests library for the HTTP call
        canopy_response = requests.post(
            token_url, data=body_params, headers=headers)
        response_data = canopy_response.json()

        logging.info(
            f"Canopy refresh token response status: {canopy_response.status_code}")

        if not canopy_response.ok:
            logging.error(
                f"Canopy API Error during token refresh: {response_data}")
            # Mimic Node.js error structure, including needsReAuth flag
            needs_reauth = canopy_response.status_code in [
                HTTPSSTATUS.HTTP_400_BAD_REQUEST, HTTPSSTATUS.HTTP_401_UNAUTHORIZED]
            return JSONResponse(
                status_code=canopy_response.status_code,
                content={
                    "message": response_data.get("message") or response_data.get("error_description") or "Failed to refresh token with Canopy.",
                    "canopyError": response_data,
                    "needsReAuth": needs_reauth  # Add flag for frontend
                },
            )

        # Calculate expiry timestamp in milliseconds since epoch
        access_token_expires_at = int(
            time.time() * 1000) + response_data.get("expires_in", 0) * 1000
        # Log in readable format
        logging.info(
            f"Token refreshed successfully. New access token expires at: {time.ctime(access_token_expires_at / 1000)}")

        # Add the calculated expiry time to the response data
        response_data["access_token_expires_at"] = access_token_expires_at

        return JSONResponse(status_code=HTTPSSTATUS.HTTP_200_OK, content=response_data)

    except requests.exceptions.RequestException as e:
        logging.error(
            f"Network or HTTP error during Canopy token refresh: {e}")
        return JSONResponse(
            status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"message": "Internal server error during token refresh.",
                     "error_details": str(e)},
        )
    except Exception as e:
        logging.error(f"Unexpected error during Canopy token refresh: {e}")
        return JSONResponse(
            status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"message": "An unexpected error occurred.",
                     "error_details": str(e)},
        )

# --- End of New Canopy Endpoints ---

from fastapi import APIRouter, status, Depends, Query
from fastapi.responses import JSONResponse
from math import ceil
from bson import ObjectId
from typing import List, Optional
import logging

from src.provider.pubsub import get_pubsub_provider
from src.middleware.verify_api_key import verify_api_key
from src.constants.env_constant import env_var
from src.models.clients import Client
from src.models.pages import Page
from src.models.files import File
from src.dto.input.PageDTOs import PageCreateRequest, PageFallbackRequest, PageUpdateIsFilledOrErrorRequest, PageUpdateRequest
from src.dto.output.CommonResponseModel import CommonResponseModel
from src.constants.enum import DrakePro<PERSON>ingStatus, FormType, QueueProcessingStatus, Tag, UserRole

# Dependency to get current user
from src.auth.auth_handler import user_jwt_required


router = APIRouter(prefix="/v1/page", tags=[Tag.PAGES.value])


@router.post("/create", summary="Create a new page")
async def create_page(payload: PageCreateRequest, current_user: dict = Depends(user_jwt_required)):
    """
    Create a new page record.

    Roles with access:
    - User: Can create pages associated with their files.
    Create a new page record.
    """
    try:
        # Ensure the file exists
        file_obj = File.get(id=payload.file_id)
        if not file_obj:
            response = CommonResponseModel(
                success=False, message="Associated file not found.")
            return JSONResponse(status_code=status.HTTP_404_NOT_FOUND, content=response.dict())

        # Check if file is created for a client created by the current user
        client_obj = file_obj.client
        if client_obj.user["id"] != current_user["id"]:
            if UserRole.ADMIN.value not in current_user["roles"]:
                response = CommonResponseModel(
                    success=False, message="Unauthorized to create page for this file.")
                return JSONResponse(status_code=status.HTTP_403_FORBIDDEN, content=response.dict())

        # Create a new Page
        page_data = {
            "page_index": payload.page_index,
            "form_type": payload.form_type,
            "t_form_type": payload.t_form_type,
            "l_form_type": payload.l_form_type,
            "message": payload.message,
            "status": QueueProcessingStatus.QUEUED.value,  # Default status
            "file": ObjectId(file_obj["id"]),
        }
        page_obj = Page.create(**page_data)
        response = CommonResponseModel(
            success=True, message="Page created successfully.", data=page_obj.to_dict())
        return JSONResponse(status_code=status.HTTP_201_CREATED, content=response.dict())
    except Exception as e:
        logging.error(f"Error creating page: {e}")
        response = CommonResponseModel(
            success=False, message="Error creating page.", data=str(e))
        return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


@router.get("/get-page/{page_id}", summary="Retrieve a page by its ID")
async def get_page(page_id: str, current_user: dict = Depends(user_jwt_required)):
    """
    Retrieve a single page by its ID.

    Roles with access:
    - User: Can view pages associated with their files.
    Retrieve a single page by its ID.
    """
    try:
        page_obj = Page.get(id=page_id)
        if not page_obj:
            response = CommonResponseModel(
                success=False, message="Page not found.")
            return JSONResponse(status_code=status.HTTP_404_NOT_FOUND, content=response.dict())

        file_obj = File.get(id=page_obj.file["id"])
        if not file_obj:
            response = CommonResponseModel(
                success=False, message="Associated file not found.")
            return JSONResponse(status_code=status.HTTP_404_NOT_FOUND, content=response.dict())

        if UserRole.ADMIN.value not in current_user["roles"]:
            client = Client.get(
                id=file_obj.client["id"], user=ObjectId(current_user["id"]))
            if not client:
                response = CommonResponseModel(
                    success=False, message="Associated client not found.")
                return JSONResponse(status_code=status.HTTP_404_NOT_FOUND, content=response.dict())

        response = CommonResponseModel(
            success=True, message="Page retrieved successfully.", data=page_obj.to_dict())
        return JSONResponse(status_code=status.HTTP_200_OK, content=response.dict())
    except Exception as e:
        logging.error(f"Error retrieving page: {e}")
        response = CommonResponseModel(
            success=False, message="Error retrieving page.", data=str(e))
        return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


@router.get("/get-pages", summary="Retrieve pages with optional filters")
async def get_pages(
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1),
    file_id: Optional[str] = Query(None, description="Filter by file ID"),
    status_filter: Optional[str] = Query(
        None, description="Filter by page status"),
    form_type: Optional[str] = Query(None, description="Form type filter"),
    page_index: Optional[int] = Query(
        None, description="Filter by page index"),
    sort_by: Optional[str] = Query(
        "page_index", description="Field to sort by (e.g., 'page_index', 'status')"),
    order: Optional[str] = Query(
        "asc", description="Sort order ('asc' for ascending, 'desc' for descending)"),
    current_user: dict = Depends(user_jwt_required)
):
    """
    Retrieve pages with optional filters.
    """
    try:
        query = {}
        if file_id:
            query["file"] = ObjectId(file_id)
        if status_filter:
            query["status"] = status_filter
        if page_index is not None:
            query["page_index"] = page_index
        if form_type:
            query["form_type__contains"] = form_type

        file_obj = File.get(id=file_id)
        if not file_obj:
            response = CommonResponseModel(
                success=False, message="Associated file not found.")
            return JSONResponse(status_code=status.HTTP_404_NOT_FOUND, content=response.dict())

        if UserRole.ADMIN.value not in current_user["roles"]:
            client = Client.get(
                id=file_obj.client["id"], user=ObjectId(current_user["id"]))
            if not client:
                response = CommonResponseModel(
                    success=False, message="Associated client not found.")
                return JSONResponse(status_code=status.HTTP_404_NOT_FOUND, content=response.dict())

        total_pages_count = Page.objects(**query).count()
        total_pages_val = ceil(total_pages_count / page_size)
        skip = (page - 1) * page_size

        # Determine sorting order
        sort_order = "+" if order == "asc" else "-"
        sort_criteria = f"{sort_order}{sort_by}"

        pages_objs = Page.objects(
            **query).order_by(sort_criteria).skip(skip).limit(page_size)
        pages_data = [p.to_dict() for p in pages_objs]
        response_data = {
            "pages": pages_data,
            "total_pages": total_pages_val,
            "current_page": page,
            "total_records": total_pages_count
        }
        response = CommonResponseModel(
            success=True, message="Pages retrieved successfully.", data=response_data)
        return JSONResponse(status_code=status.HTTP_200_OK, content=response.dict())
    except Exception as e:
        logging.error(f"Error retrieving pages: {e}")
        response = CommonResponseModel(
            success=False, message="Error retrieving pages.", data=str(e))
        return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


@router.patch("/update", summary="Update a page")
async def update_page(payload: PageUpdateRequest, current_user: dict = Depends(user_jwt_required)):
    """
    Update an existing page's details.

    Roles with access:
    - User: Can update pages associated with their files.
    Update an existing page's details.
    """
    try:

        update_form_status = False

        if payload.page_id:
            page_ids = [payload.page_id]
        elif payload.update_form_status:
            page_ids = payload.update_form_status.page_ids
            update_form_status_payload = payload.update_form_status
            update_form_status = True

        for page_id in page_ids:
            page_obj = Page.get(id=page_id)
            if not page_obj:
                response = CommonResponseModel(
                    success=False, message="Page not found.")
                return JSONResponse(status_code=status.HTTP_404_NOT_FOUND, content=response.dict())

            file_obj = File.get(id=page_obj.file["id"])
            if not file_obj:
                response = CommonResponseModel(
                    success=False, message="Associated file not found.")
                return JSONResponse(status_code=status.HTTP_404_NOT_FOUND, content=response.dict())

            if UserRole.ADMIN.value not in current_user["roles"]:
                client = Client.get(
                    id=file_obj.client["id"], user=ObjectId(current_user["id"]))
                if not client:
                    response = CommonResponseModel(
                        success=False, message="Associated client not found.")
                    return JSONResponse(status_code=status.HTTP_404_NOT_FOUND, content=response.dict())
            else:
                client = Client.get(id=file_obj.client["id"])

            if update_form_status:
                if update_form_status_payload.form_id not in page_obj.forms_status:
                    page_obj.forms_status[update_form_status_payload.form_id] = {
                        "is_filled": update_form_status_payload.is_filled,
                        "error_message": update_form_status_payload.error_message if update_form_status_payload.error_message else None
                    }
                else:
                    page_obj.forms_status[update_form_status_payload.form_id]["is_filled"] = update_form_status_payload.is_filled
                    if update_form_status_payload.error_message:
                        page_obj.forms_status[update_form_status_payload.form_id][
                            "error_message"] = update_form_status_payload.error_message
            else:
                # Update fields if provided
                if payload.page_index is not None:
                    page_obj.page_index = payload.page_index
                if payload.form_type is not None:
                    page_obj.form_type = payload.form_type
                if payload.t_form_type is not None:
                    page_obj.t_form_type = payload.t_form_type
                if payload.l_form_type is not None:
                    page_obj.l_form_type = payload.l_form_type
                if payload.message is not None:
                    page_obj.message = payload.message
                if payload.status is not None:
                    page_obj.status = payload.status

            page_obj.last_updated_by = ObjectId(current_user["id"])
            page_obj.save()

            file_obj.last_updated_by = ObjectId(current_user["id"])
            file_obj.save()

            # Check to update drake status
            file_ids = list(File.objects(
                client=client.id).only("id").scalar("id"))

            page_query = {"file": {"$in": file_ids}}
            pages_cursor = Page.objects(__raw__=page_query).only(
                "id", "forms_status").as_pymongo()
            is_filled_list = []
            for page in pages_cursor:
                for form_status in list(page["forms_status"].values()):
                    if "is_filled" in form_status:
                        is_filled_list.append(form_status["is_filled"])
                    else:
                        is_filled_list.append(False)

            has_true = any(is_filled_list)
            has_false = not all(is_filled_list)

            if has_true and has_false:
                # Some form filled status are True, some are False.
                client.drake_status = DrakeProcessingStatus.PARTIALLY_PROCESSED.value
            elif has_true:
                # All form filled status are True.
                client.drake_status = DrakeProcessingStatus.PROCESSED.value
            else:
                # All form filled status are False.
                client.drake_status = DrakeProcessingStatus.ERROR.value

            client.last_updated_by = ObjectId(current_user["id"])
            client.save()

        response = CommonResponseModel(
            success=True, message="Page updated successfully.", data=page_obj.to_dict())
        return JSONResponse(status_code=status.HTTP_200_OK, content=response.dict())
    except Exception as e:
        logging.error(f"Error updating page: {e}")
        import traceback
        traceback.print_exc()
        response = CommonResponseModel(
            success=False, message="Error updating page.", data=str(e))
        return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


@router.delete("/delete-page/{page_id}", summary="Delete a page")
async def delete_page(page_id: str, current_user: dict = Depends(user_jwt_required)):
    """
    Delete a page by its ID.

    Roles with access:
    - Admin: Can delete any page.
    - User: Can delete any page for file which he has created.
    Delete a page by its ID.
    """
    try:
        page_obj = Page.get(id=page_id)
        if not page_obj:
            response = CommonResponseModel(
                success=False, message="Page not found.")
            return JSONResponse(status_code=status.HTTP_404_NOT_FOUND, content=response.dict())

        file_obj = File.get(id=page_obj.file["id"])
        if not file_obj:
            response = CommonResponseModel(
                success=False, message="Associated file not found.")
            return JSONResponse(status_code=status.HTTP_404_NOT_FOUND, content=response.dict())

        if UserRole.ADMIN.value not in current_user["roles"]:
            client = Client.get(
                id=file_obj.client["id"], user=ObjectId(current_user["id"]))
            if not client:
                response = CommonResponseModel(
                    success=False, message="Associated client not found.")
                return JSONResponse(status_code=status.HTTP_404_NOT_FOUND, content=response.dict())

        page_obj.delete()
        response = CommonResponseModel(
            success=True, message="Page deleted successfully.")
        return JSONResponse(status_code=status.HTTP_200_OK, content=response.dict())
    except Exception as e:
        logging.error(f"Error deleting page: {e}")
        response = CommonResponseModel(
            success=False, message="Error deleting page.", data=str(e))
        return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


@router.post("/fallback", summary="Trigger fallback processing for pages")
async def fallback_pages(payload: PageFallbackRequest, current_user: dict = Depends(user_jwt_required)):
    """
    Trigger fallback processing for pages where OCR has failed or needs reprocessing.

    Roles with access:
    - User: Can trigger fallback processing for pages associated with their clients.

    """
    try:

        file_obj = File.get(id=payload.file_id)

        if not file_obj:
            response = CommonResponseModel(
                success=False,
                message=f"File with id {payload.file_id} not found"
            )
            return JSONResponse(status_code=status.HTTP_400_BAD_REQUEST, content=response.dict())

        # Update file status to queued
        file_obj.status = QueueProcessingStatus.QUEUED.value
        file_obj.message = ""
        file_obj.last_updated_by = ObjectId(current_user["id"])

        for page_num, form_types in payload.new_form_types.items():
            file_obj.form_types_per_page[page_num] = form_types

        if UserRole.ADMIN.value not in current_user["roles"]:
            client = Client.get(
                id=file_obj.client["id"], user=ObjectId(current_user["id"]))
            if not client:
                response = CommonResponseModel(
                    success=False, message="Associated client not found.")
                return JSONResponse(status_code=status.HTTP_404_NOT_FOUND, content=response.dict())
        else:
            client = Client.get(id=file_obj.client["id"])

        previous_drake_status = DrakeProcessingStatus.PROCESSED.value

        if client:
            if client.queue_status in [QueueProcessingStatus.PROCESSED.value, QueueProcessingStatus.PARTIALLY_PROCESSED.value, QueueProcessingStatus.ERROR.value] \
                    and client.drake_status in [DrakeProcessingStatus.WAITING.value, DrakeProcessingStatus.PARTIALLY_PROCESSED.value, DrakeProcessingStatus.PROCESSED.value, DrakeProcessingStatus.ERROR.value]:
                previous_drake_status = client.drake_status
                client.queue_status = QueueProcessingStatus.QUEUED.value
                client.drake_status = DrakeProcessingStatus.WAITING.value
            else:
                response = CommonResponseModel(
                    success=False,
                    message=f"Client with id {client.id} is not in a valid state for fallback processing."
                )

            # Update is_review_required if not already true
            if not client.is_review_required:
                client.is_review_required = payload.is_review_required if payload.is_review_required else False
                if client.is_review_required:
                    client.is_reviewed = False

            if client.is_aborted:
                client.is_aborted = False

            client.last_updated_by = ObjectId(current_user["id"])

        page_responses = []

        for page_id in payload.page_ids:
            try:
                page_obj = Page.get(id=page_id)
                if not page_obj:
                    page_response = CommonResponseModel(
                        success=False,
                        message=f"Page with id {page_id} not found.",
                        data=None
                    )
                    page_responses.append(page_response.dict())
                    continue

                # Process only if the file status indicates an error
                page_form_types = page_obj.form_type
                if page_obj.status != QueueProcessingStatus.ERROR.value:
                    if FormType.INSTRUCTIONS.value in page_form_types or FormType.UNKNOWN.value in page_form_types:
                        pass
                    else:
                        page_response = CommonResponseModel(
                            success=False,
                            message=f"Page with id {page_id} is not eligible for fallback processing.",
                            data=None
                        )
                        page_responses.append(page_response.dict())
                        continue

                page_obj.status = QueueProcessingStatus.QUEUED.value
                page_obj.last_updated_by = ObjectId(current_user["id"])
                page_obj.message = ""   # Clear any previous error message
                page_obj.save()

                page_response = CommonResponseModel(
                    success=True,
                    message=f"Fallback processing triggered successfully for page with id {page_id}.",
                    data=page_obj.to_dict()
                )
                page_responses.append(page_response.dict())

            except Exception as ex:
                logging.error(f"Error processing Page {page_id}: {ex}")
                file_response = CommonResponseModel(
                    success=False,
                    message=f"Error processing fallback for page {page_id}: {str(ex)}",
                    data=None
                )
                page_responses.append(file_response.dict())

        file_obj.save()
        client.save()

        # Use the Redis utility class for enqueuing the job
        pubsub_provider = get_pubsub_provider(
            env_var["DEFAULT_PUBSUB_PROVIDER"])
        job_message = {
            "name": client.name,
            "social_security_number": client.social_security_number,
            "financial_year": client.financial_year,
            "if_fallback": True,
            "previous_drake_status": previous_drake_status
        }
        pubsub_provider.enqueue_task(
            env_var['REDIS_WORKER_PATH'],
            job_message,
            env_var['REDIS_WORKER_JOB_TIMEOUT_IN_SECONDS']
        )

        overall_response = CommonResponseModel(
            success=True,
            message="Fallback processing completed for all pages.",
            data=page_responses
        )
        return JSONResponse(status_code=status.HTTP_200_OK, content=overall_response.dict())
    except Exception as e:
        logging.error(f"Error triggering fallback processing: {e}")
        import traceback
        traceback.print_exc()
        response = CommonResponseModel(
            success=False, message="Error triggering fallback processing.", data=str(e))
        return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


@router.patch("/update-is-filled", summary="Update pages is_filled list")
async def update_pages_is_filled(payloads: List[PageUpdateIsFilledOrErrorRequest], current_user: dict = Depends(verify_api_key)):
    """
    Update an existing page's is_filled details.

    Roles with access:
    - Drake: Only Drake can update pages is_filled list.
    Update an existing page's details.
    """
    try:
        for payload in payloads:
            for page_id in payload.page_ids:
                page_obj = Page.get(id=page_id)

                if payload.form_id not in page_obj.forms_status:
                    page_obj.forms_status[payload.form_id] = {
                        "is_filled": payload.is_filled,
                        "error_message": payload.error_message if payload.error_message else None
                    }
                else:
                    page_obj.forms_status[payload.form_id]["is_filled"] = payload.is_filled
                    if payload.error_message:
                        page_obj.forms_status[payload.form_id]["error_message"] = payload.error_message

                page_obj.save()
        response = CommonResponseModel(
            success=True, message="Pages updated successfully.")
        return JSONResponse(status_code=status.HTTP_200_OK, content=response.dict())
    except Exception as e:
        logging.error(f"Error updating page: {e}")
        response = CommonResponseModel(
            success=False, message="Error updating page.", data=str(e))
        return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())

# constants.py

import os
import logging
from src.constants.enum import LLM_PROVIDER
from src.constants.env_constant import env_var
from dotenv import load_dotenv # Load environment variables from a .env file if present

load_dotenv()


OPENAI_MODEL = LLM_PROVIDER.OPENAI_GPT_4O  # Update as per available models

# Paths
NON_MASKED_FOLDER = 'non_masked_folder'
OUTPUTS_FOLDER = 'outputs'

# OCR Configuration
PYTESSERACT_CONFIG = '--oem 3 --psm 6 -l eng'

# Image Processing Parameters
ADAPTIVE_THRESH_BLOCK_SIZE = 15
ADAPTIVE_THRESH_C = -2
VERTICAL_KERNEL_RATIO = 100
BORDER_SIZE = 20

# Regex Patterns
EIN_PATTERN = r'\b\d{2}[- ]?\d{7}\b'
SEQUENCE_NUMBER_PATTERN = r'^\s*\d*[a-zA-Z]?\d*\s*$'

# Output Paths
DEFAULT_OUTPUT_PATH_TEMPLATE = 'outputs/{folder_name}/{file_id}/{file_id}_{page_number}_{form_type}.png'
PROCESSING_FOLDER_TEMPLATE = os.path.join(
    env_var['BASE_DOWNLOAD_DIR'], "{ssn}_{year}")
MASKED_FILES_OUTPUT_FOLDER_TEMPLATE = "{social_security_number}_{financial_year}"

#LLM config
DEFAULT_FORM_TYPE_LLM = LLM_PROVIDER.OPENAI_GPT_4O
DEFAULT_OCR_LLM =LLM_PROVIDER.GEMINI_1_5_PRO

# Form Configuration Mapping

BASE_PROMPT = """
                    Extract table data and checkbox statuses from the provided image. 
                    For checkboxes, categorize them under checkboxes with their enabled
                    /disabled status in boolean. Organize table data into valid JSON 
                    format as key-value pairs, focusing solely on values without any 
                    extra text or sequence numbers. Maintain a clean, professional 
                    structure in your output.
                
                    Note:
                        1. Ensure all the checkboxes status should be there if it is in 
                        the image i can be in table or it can be without table.
                        2. Checkbox fields have boxes in it.
                        3. Keys apart from checkboxes for which values are not there assign empty values in them.
                """


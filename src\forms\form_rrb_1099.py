from pydantic import BaseModel, Field
from typing import List, Optional
from src.forms.base_form import TextField, YearField, CurrencyField, CheckboxField, FormDetails, ZipCodeField, EINField, DateField


class PayerInformation(BaseModel):
    payer_name: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_address: TextField = Field(
        default_factory=lambda: TextField(type="text"), description="The payer's street address as listed on the form without city, state, zipcode.")
    payer_city: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_state: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_zip_code: ZipCodeField = Field(
        default_factory=lambda: ZipCodeField(type="zipcode"))
    payer_federal_identification_number: EINField = Field(default_factory=lambda: EINField(type="einfield"))

class claimInformation(BaseModel):
    payee_code: TextField = Field(default_factory=lambda: TextField(type="text"))

class FinancialData(BaseModel):
    gross_social_security_equivalent_benefit_portion_of_tier_1_paid_in_2023 : CurrencyField = Field(default_factory=lambda: CurrencyField(type="currency"))
    social_security_equivalent_benefit_portion_of_tier_1_repaid_to_rrb_in_2023 : CurrencyField = Field(default_factory=lambda: CurrencyField(type="currency"))
    net_social_security_equivalent_benefit_portion_of_tier_1_paid_in_2023 : CurrencyField = Field(default_factory=lambda: CurrencyField(type="currency"))
    workers_compensation_offset_in_2023 : CurrencyField = Field(default_factory=lambda: CurrencyField(type="currency"))
    social_security_equivalent_benefit_portion_of_tier_1_paid_for_2022 : CurrencyField = Field(default_factory=lambda: CurrencyField(type="currency"))
    social_security_equivalent_benefit_portion_of_tier_1_paid_for_2021 : CurrencyField = Field(default_factory=lambda: CurrencyField(type="currency"))
    social_security_equivalent_benefit_portion_of_tier_1_paid_for_years_prior_to_2021 : CurrencyField = Field(default_factory=lambda: CurrencyField(type="currency"))
    federal_income_tax_withheld : CurrencyField = Field(default_factory=lambda: CurrencyField(type="currency"))
    medicare_premium_total : CurrencyField = Field(default_factory=lambda: CurrencyField(type="currency"))

class FormRRB1099(BaseModel):

    form_details: FormDetails = Field(default_factory=FormDetails)
    payer_information: PayerInformation = Field(default_factory=PayerInformation)
    financial_data: FinancialData = Field(default_factory=FinancialData)
    claim_information: claimInformation = Field(default_factory=claimInformation)

from pydantic import BaseModel, Field, condecimal
from typing import Optional, List
from src.forms.base_form import TextField, CurrencyField, CheckboxField, YearField, DateField, FormDetails, ZipCodeField, EINField


# Employer Information Section
class EmployerInformation(BaseModel):
    employer_identification_number: EINField = Field(default_factory=lambda: EINField(type="einfield"))
    employer_name: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    employer_address: TextField = Field(
        default_factory=lambda: TextField(type="text"), description="The employer's street address as listed on the form without city, state, zipcode.")
    employer_city: TextField = Field(
        default_factory=lambda: TextField(type="text"), description="City of the employer")
    employer_state: TextField = Field(
        default_factory=lambda: TextField(type="text"), description="State of the employer")
    employer_zip_code: ZipCodeField = Field(
        default_factory=lambda: ZipCodeField(type="zipcode"), description="Zip code of the employer")


# Financial Fields Section
class FinancialFields(BaseModel):
    wages_tips_other_compensation: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    federal_income_tax_withheld: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    social_security_wages: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    social_security_tax_withheld: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    medicare_wages_and_tips: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    medicare_tax_withheld: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    social_security_tips: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    allocated_tips: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    dependent_care_benefits: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    nonqualified_plans: TextField = Field(
        default_factory=lambda: TextField(type="text"))

    # Checkboxes for statutory, retirement, and third-party sick pay
    statutory_employee: CheckboxField = Field(default_factory=lambda: CheckboxField(
        type="checkbox"), description="Check if `statutory employee` field is marked with anything like a cross or tick then mark it as True otherwise mark it as False")
    retirement_plan: CheckboxField = Field(default_factory=lambda: CheckboxField(
        type="checkbox"), description="Check if `retirement plan` field is marked with anything like a cross or tick then mark it as True otherwise mark it as False")
    third_party_sick_pay: CheckboxField = Field(default_factory=lambda: CheckboxField(
        type="checkbox"), description="Check if `third party sick pay` field is marked with anything like a cross or tick then mark it as True otherwise mark it as False")

    control_number: TextField = Field(
        default_factory=lambda: TextField(type="text"))


class StateAndLocalInformation(BaseModel):
    state: TextField = Field(default_factory=lambda: TextField(type="text"))
    employers_state_ID_number: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    state_wages_tips: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    state_income_tax: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    local_wages_tips: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    local_income_tax: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    locality_name: TextField = Field(
        default_factory=lambda: TextField(type="text"))


# Additional Information Section
class AdditionalInformation(BaseModel):
    void: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))


class Box12Filling(BaseModel):
    box_12_code: TextField = Field(
        default_factory=lambda: TextField(type="text", description="it can just be named as box 12 OR 12a,12b,12c,12d in the form"))
    box_12_amount: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))


class OtherInformation(BaseModel):
    other_code: TextField = Field(default_factory=lambda: TextField(
        type="text"), description="collect data from box named *Other*/ box or box 14 only or box in which field are labeled as *14a,14b,14c,14d,14e,14f*/. Do not collect data from box *12a,12b,12c,12d*/ or box See Ins. for box 12")
    other_amount: CurrencyField = Field(default_factory=lambda: CurrencyField(
        type="currency"), description="collect data from box named *Other*/ box or box 14 only or box in which field are labeled as *14a,14b,14c,14d,14e,14f*/. Do not collect data from box *12a,12b,12c,12d*/ or box See Ins. for box 12")
# Main Pydantic model for Form W-2


class FormW2(BaseModel):
    employer_information: EmployerInformation = Field(
        default_factory=EmployerInformation)
    form_details: FormDetails = Field(default_factory=FormDetails)
    financial_fields: FinancialFields = Field(default_factory=FinancialFields)
    box_12_filling: List[Box12Filling] = Field(
        default_factory=list, description="collect data from box 12 or boxes *12a,12b,12c,12d*/ or box See Ins. for box 12 only. Do not collect data from box 14 or *Other*/ box")
    other_information: List[OtherInformation] = Field(
        default_factory=list, description="collect data from box named *Other*/ box or box with labeled as 14 only or box in which field are labeled as *14a,14b,14c,14d,14e,14f*/. Do not collect data from *box 12*/ or boxes *12a,12b,12c,12d*/ or box See Ins. for box 12")
    state_and_local_information: List[StateAndLocalInformation] = Field(
        default_factory=list)
    additional_information: AdditionalInformation = Field(
        default_factory=AdditionalInformation)

from src.config.bucket import bucket_configuration
import logging


def get_bucket_provider(provider):
    """
    Get the bucket provider object based on the provided provider name.

    Args:
    - provider (str): The name of the bucket provider to retrieve.

    Returns:
    - provider_object: The bucket provider object.

    Raises:
    - ValueError: If the provided provider name is invalid.
    """
    try:
        # Attempt to retrieve the provider object from the configuration
        provider_object = bucket_configuration[provider]['provider']

    except Exception as e:
        # Log the error along with the configuration for debugging purposes
        logging.error(f"Invalid provider: {provider}. Configuration: {bucket_configuration} = {e}")

        # Raise a ValueError with a meaningful error message
        raise ValueError(f"Invalid bucket provider: {provider}")

    return provider_object

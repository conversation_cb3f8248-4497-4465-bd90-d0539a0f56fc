from pydantic import BaseModel, <PERSON>
from typing import List
from src.forms.base_form import TextField, YearField, CurrencyField, DateField, CheckboxField, FormDetails, ZipCodeField, EINField


class PartnershipInformation(BaseModel):
    partnership_name: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    partnership_address: TextField = Field(
        default_factory=lambda: TextField(type="text"), description="The partnership's street address as listed on the form without city, state, zipcode.")
    partnership_city: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    partnership_state: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    partnership_zip: ZipCodeField = Field(
        default_factory=lambda: ZipCodeField(type="zipcode"))
    partnership_employer_identification_number: EINField = Field(default_factory=lambda: EINField(type="einfield"))
    check_if_this_is_a_publicly_traded_partnership: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))


class OtherIncomeLoss(BaseModel):
    other_income_code: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    other_income_loss_amount: TextField = Field(
        default_factory=lambda: TextField(type="text"),description="If value is present inside bracket then provide the value inside bracket")


class OtherDeductions(BaseModel):
    other_deductions_code: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    other_deductions_amount: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))


class SelfEmploymentEarnings(BaseModel):
    self_employment_earnings_code: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    self_employment_earnings_amount: TextField = Field(
        default_factory=lambda: TextField(type="text"),description="If value is present inside bracket then provide the value inside bracket")


class CreditInfo(BaseModel):
    credits_code: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    credits_amount: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))


class AlternativeMinimumTaxItems(BaseModel):
    alternative_minimum_tax_items_code: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    alternative_minimum_tax_items_amount: TextField = Field(
        default_factory=lambda: TextField(type="text"))


class TaxExemptedIncome(BaseModel):
    tax_exempted_income_and_nondeductible_expenses_code: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    tax_exempted_income_and_nondeductible_expenses_amount: TextField = Field(
        default_factory=lambda: TextField(type="text"))


class Distributions(BaseModel):
    distributions_code: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    distributions_amount: TextField = Field(
        default_factory=lambda: TextField(type="text"))


class OtherInformation(BaseModel):
    other_information_code: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    other_information_amount: TextField = Field(
        default_factory=lambda: TextField(type="text"))


class UnrecapturedSection1250Gain(BaseModel):
    unrecaptured_section_1250_gain_code: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    unrecaptured_section_1250_gain_amount: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))


class PartnerShareOfIncome(BaseModel):
    ordinary_business_income_loss: TextField = Field(
        default_factory=lambda: TextField(type="text"),description="If value is present inside bracket then provide the value inside bracket")
    net_rental_real_estate_income_loss: TextField = Field(
        default_factory=lambda: TextField(type="text"),description="If value is present inside bracket then provide the value inside bracket")
    other_net_rental_income_loss: TextField = Field(
        default_factory=lambda: TextField(type="text"),description="If value is present inside bracket then provide the value inside bracket")
    guaranteed_payments_services: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    guaranteed_payments_capital: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    total_guaranteed_payments: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    interest_income: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    ordinary_dividends: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    qualified_dividends: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    dividend_equivalents: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    royalties: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    net_short_term_capital_gain_loss: TextField = Field(
        default_factory=lambda: TextField(type="text"),description="If value is present inside bracket then provide the value inside bracket")
    net_long_term_capital_gain_loss: TextField = Field(
        default_factory=lambda: TextField(type="text"),description="If value is present inside bracket then provide the value inside bracket")
    collectibles_28_percent_gain_loss: TextField = Field(
        default_factory=lambda: TextField(type="text"),description="If value is present inside bracket then provide the value inside bracket")
    unrecaptured_section_1250_gain: UnrecapturedSection1250Gain = Field(
        default_factory=UnrecapturedSection1250Gain)
    net_section_1231_gain_loss: TextField = Field(
        default_factory=lambda: TextField(type="text"),description="If value is present inside bracket then provide the value inside bracket")
    other_income_loss: List[OtherIncomeLoss] = Field(default_factory=list)
    section_179_deduction: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    other_deductions: List[OtherDeductions] = Field(default_factory=list)
    self_employment_earnings: List[SelfEmploymentEarnings] = Field(
        default_factory=list)
    credits: List[CreditInfo] = Field(default_factory=list)
    alternative_minimum_tax_items: List[AlternativeMinimumTaxItems] = Field(
        default_factory=list)
    tax_exempt_income_and_nondeductible_expenses: List[TaxExemptedIncome] = Field(
        default_factory=list)
    distributions: List[Distributions] = Field(default_factory=list)
    other_information: List[OtherInformation] = Field(default_factory=list, description="list of all code and value present under other information section")
    schedule_k3_attached: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    foreign_taxes_paid_or_accrued: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    more_than_one_activity_for_at_risk_purposes: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    more_than_one_activity_for_passive_activity_purposes: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))

class AdditionalFields(BaseModel):
    period_begin: YearField = Field(
        default_factory=lambda: YearField(type="year"))
    period_end: YearField = Field(
        default_factory=lambda: YearField(type="year"))
    check_final_k1: CheckboxField = Field(
        default_factory=lambda: CheckboxField(sequence="Final K-1", type="checkbox"))
    check_amended_k1: CheckboxField = Field(
        default_factory=lambda: CheckboxField(sequence="Amended K-1", type="checkbox"))
    
class FormK1_1065(BaseModel):
    form_details: FormDetails = Field(default_factory=FormDetails)
    additional_fields: AdditionalFields = Field(default_factory=AdditionalFields)
    partnership_information: PartnershipInformation = Field(
        default_factory=PartnershipInformation)
    partner_share_of_income: PartnerShareOfIncome = Field(
        default_factory=PartnerShareOfIncome)

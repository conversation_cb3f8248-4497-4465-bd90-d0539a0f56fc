from fastapi import Depends, HTTPException, status, Request
from src.constants.enum import User<PERSON><PERSON>
from src.constants.env_constant import env_var
from datetime import datetime, timedelta
from src.config.logger import logging
from src.models.users import User
from bson import ObjectId
from typing import Dict, Optional
import jwt

# JWT Secret and Algorithm
JWT_TOKEN_SECRET_KEY = env_var["JWT_TOKEN_SECRET_KEY"]  # Replace this with a secure key
JWT_TOKEN_ALGORITHM = env_var["JWT_TOKEN_ALGORITHM"]
JWT_EXPIRATION_TIME_IN_HOURS = int(env_var["JWT_TOKEN_EXPIRATION_TIME_IN_HOURS"])  # JWT Token expiration time in hours
OTP_EXPIRATION_TIME_IN_MINUTES = int(env_var["OTP_EXPIRATION_TIME_IN_MINUTES"])  # OTP expiration time in minutes


def generate_jwt_token(data: dict, expires_delta: timedelta = None) -> Dict[str, str]:
    """
    Generate a JWT token for a user with an expiration time.

    Args:
        user_id (str): The ID of the user for whom the token is generated.

    Returns:
        Dict[str, str]: A dictionary containing the JWT token.
    """
    payload = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(hours=JWT_EXPIRATION_TIME_IN_HOURS)
    payload.update({"exp": expire})
    return jwt.encode(payload, JWT_TOKEN_SECRET_KEY, algorithm=JWT_TOKEN_ALGORITHM)


def decode_jwt(token: str) -> dict:
    """
    Decode a JWT token to extract the payload.

    Args:
        token (str): The JWT token to decode.

    Returns:
        dict: The payload of the decoded token or an empty dictionary if decoding fails.
    """
    try:
        payload = jwt.decode(token, JWT_TOKEN_SECRET_KEY, algorithms=[JWT_TOKEN_ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        logging.warning("Token has expired.")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Token has expired.")
    except jwt.InvalidTokenError:
        logging.warning("Invalid token.")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token.")
    except Exception as e:
        logging.error(f"Error decoding token: {e}")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Error decoding token.")

from src.auth.auth_bearer import JWTBearer

def user_jwt_required(request: Request, token: str = Depends(JWTBearer())) -> dict:
    """
    Dependency that ensures the request has a valid JWT token and the user exists and has the appropriate role.

    Args:
        request (Request): The request object to set the user in the state.
        token (str): The JWT token extracted from the request.

    Returns:
        dict: The user information as a dictionary if the token is valid and the user is authorized.

    Raises:
        HTTPException: If the token is invalid, expired, or the user does not exist or is unauthorized.
    """
    payload = decode_jwt(token)
    user_id = payload.get("id")

    if not user_id:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token payload.")

    user = User.get(id=ObjectId(user_id))

    if not user:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="User not found.")
    
    if not user.is_active:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="User is inactive.")

    user = user.to_dict()

    request.state.user = user

    return user

async def admin_or_first_signup(
    request: Request,
    token: Optional[str] = Depends(JWTBearer(auto_error=False))
):
    # If no admin exists yet → allow signup unconditionally
    if not User.get(roles=[UserRole.ADMIN.value]):
        return

    # Otherwise we must have a valid token
    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required"
        )

    payload = decode_jwt(token)
    user = User.get(id=ObjectId(payload.get("id")))
    if not user or UserRole.ADMIN.value not in user.roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only admins can create new accounts"
        )

    request.state.user = user.to_dict()

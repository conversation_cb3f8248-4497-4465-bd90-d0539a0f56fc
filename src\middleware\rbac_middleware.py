from src.dto.output.CommonResponseModel import CommonResponseModel
from src.models.permissions import Permissions
from functools import wraps
from fastapi.responses import JSONResponse
from fastapi import Request, status


def check_permission(permission: str):
    def decorator(func):
        @wraps(func)
        async def wrapper(request: Request, *args, **kwargs):

            try:
            
                if request is None:
                    raise RuntimeError("No Request found for RBAC check")
                
                user = getattr(request.state, 'user', None)
                if not user:
                    response = CommonResponseModel(
                        message="User not authenticated"
                    )
                    return JSONResponse(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        content=response.dict()
                    )

                user_roles = user.get('roles', [])
                permissions = Permissions()

                # Check permissions for the user's roles
                user_permissions = []
                for role in user_roles:
                    user_permissions.extend(permissions.get_permissions_by_role_name(role))

                if permission not in user_permissions:
                    response = CommonResponseModel(
                        message="Access denied"
                    )
                    return JSONResponse(
                        status_code=status.HTTP_403_FORBIDDEN,
                        content=response.dict()
                    )

                return await func(request, *args, **kwargs)
            except Exception as e:
                import traceback
                traceback.print_exc()
                response = CommonResponseModel(
                    message="Error in RBAC check"
                )

        return wrapper
    return decorator

import logging
from typing import Optional
from src.models.users import User
from src.constants.enum import QueueProcessingStatus
from src.constants.enum import DrakeProcessingStatus
from src.models.base_model import BaseDocument
from mongoengine.queryset.visitor import Q
import mongoengine as me

from src.utils.encrypt_decrypt_data import deterministic_decrypt, deterministic_encrypt

class Client(BaseDocument):
    name = me.StringField(required=True)
    social_security_number = me.StringField(required=True, unique=False)  # Remove unique=True here
    ssn_with_last_four_digits = me.StringField(required=True, unique=False)  # Remove unique=True here
    queue_status = me.StringField(
        choices=[status.value for status in QueueProcessingStatus],
        default=QueueProcessingStatus.QUEUED.value
    )
    message = me.StringField(required=False)
    financial_year = me.IntField(required=True)
    output_path = me.StringField(required=False)
    drake_status = me.StringField(
        choices=[status.value for status in DrakeProcessingStatus],
        default=DrakeProcessingStatus.WAITING.value,
        required= True
    )
    user = me.ReferenceField(User, required=True)  # Reference to the User model

    # Maintain user who updated the document last
    last_updated_by = me.ReferenceField(User, required=False)

    # Maintain a flag to indicate if review is required
    is_review_required = me.BooleanField(default=False, required=True)
    is_reviewed = me.BooleanField(default=True, required=True)

    # Maintain a flag to indicate this client is aborted
    is_aborted = me.BooleanField(default=False, required=True)

    # Maintain a flag to indicate this Client is priority
    is_priority = me.BooleanField(default=False, required=True)
    
    def save(self, *args, **kwargs):
        # Encrypt name if not already encrypted
        if self.name:
            self.name = deterministic_encrypt(self.name)
            
        if self.social_security_number:
            # Encrypt full SSN
            self.social_security_number = deterministic_encrypt(self.social_security_number)

            # Extract last 4 digits or partial and encrypt it
            try:
                ssn_plain = deterministic_decrypt(self.social_security_number)
            except Exception:
                ssn_plain = self.social_security_number  # fallback if already decrypted

            if len(ssn_plain) >= 4:
                self.ssn_with_last_four_digits = deterministic_encrypt(ssn_plain[-4:])
        return super().save(*args, **kwargs)

    
    @classmethod
    def get(cls, **kwargs) -> Optional[me.Document]:
        ssn = kwargs.pop("social_security_number", None)
        name = kwargs.pop("name", None)

        if ssn:
            encrypted_ssn = deterministic_encrypt(ssn)
            return cls.objects(social_security_number=encrypted_ssn, **kwargs).first()
        
        if name:
            encrypted_name = deterministic_encrypt(name)
            return cls.objects(name=encrypted_name, **kwargs).first()

        return super().get(**kwargs)

    @classmethod
    def get_filtered_clients(cls, query_q, name_or_ssn: Optional[str] = None):
        if not name_or_ssn:
            return cls.objects(query_q)

        clients = cls.objects(query_q)
        matched_clients = []
        seen_ids = set()

        # Case 1: Possibly partial SSN (4-8 digits)
        if name_or_ssn.isdigit() and 4 <= len(name_or_ssn) < 9:
            encrypted_partial = deterministic_encrypt(name_or_ssn[-4:])
            filtered = cls.objects(query_q & Q(ssn_with_last_four_digits=encrypted_partial))
            for client in filtered:
                matched_clients.append(client)
                seen_ids.add(str(client.id))

        # Case 2: Full SSN (9 digits)
        elif name_or_ssn.isdigit() and len(name_or_ssn) == 9:
            encrypted_ssn = deterministic_encrypt(name_or_ssn)
            filtered = cls.objects(query_q & Q(social_security_number=encrypted_ssn))
            for client in filtered:
                matched_clients.append(client)
                seen_ids.add(str(client.id))

        # Case 3: Match by name
        else:
            encrypted_name = deterministic_encrypt(name_or_ssn)
            filtered = cls.objects(query_q & Q(name=encrypted_name))
            if not filtered:
                filtered = cls.objects(query_q & Q(social_security_number=encrypted_name))
            for client in filtered:
                matched_clients.append(client)
                seen_ids.add(str(client.id))

        # # Case 3: Match by name
        # else:
        #     # For name search, we need to decrypt names for comparison
        #     for client in clients:
        #         if str(client.id) in seen_ids:
        #             continue  # already matched by SSN
                
        #         # Decrypt the name for comparison
        #         try:
        #             if client.name:
        #                 decrypted_name = deterministic_decrypt(client.name)
        #                 if name_or_ssn.lower() in decrypted_name.lower():
        #                     matched_clients.append(client)
        #                     seen_ids.add(str(client.id))
        #         except Exception:
        #             # If decryption fails, skip this client for name matching
        #             pass

        return matched_clients


    def to_dict(self, exclude_fields: Optional[list] = []) -> dict:
        document = super().to_dict(exclude_fields)
        try:
            # Decrypt name
            if self.name:
                document["name"] = deterministic_decrypt(self.name)
        except Exception:
            document["name"] = self.name
            
        try:
            ssn = deterministic_decrypt(self.social_security_number)
            document["social_security_number"] = "XXX-XX-" + ssn[-4:]
        except Exception:
            ssn = self.social_security_number
            document["social_security_number"] = "XXX-XX-" + ssn[-4:]
        return document

    meta = {
        'collection': 'clients',
        'indexes': [
            {
                'fields': ['social_security_number', 'financial_year'],
                'unique': True
            }
        ]
    }

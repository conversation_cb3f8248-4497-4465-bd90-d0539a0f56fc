
import sys
from typing import Dict
from bson import ObjectId
from celery import Celery
from src.models.pages import Page
from src.constants.env_constant import env_var
from pydantic import BaseModel, ValidationError
from src.models.clients import Client
from src.models.files import File
from src.constants.enum import QueueProcessingStatus, FormType, ErrorMessage
from google.cloud import storage
from src.models.config import init_db
from mongoengine import DoesNotExist
from src.langgraph_agents.agent import process_tax_form
from src.langgraph_agents.agentG2 import run_deduplication
from src.constants.constants import PROCESSING_FOLDER_TEMPLATE, MASKED_FILES_OUTPUT_FOLDER_TEMPLATE
from main import process_file
from src.utils.utility import get_number
import threading
import pika
import base64
import json
import os
from redis import Redis
from rq import Worker
import shutil
import logging
import src.config.logger
from src.constants.constants import NON_MASKED_FOLDER
from src.constants.enum import DrakeProcessingStatus
from src.utils.upload_and_download_file_gcp import upload_file_to_bucket
from src.utils.encrypt_decrypt_data import deterministic_decrypt


# Initialize the database once during startup
init_db()

redis_conn = Redis(host=env_var['REDIS_HOST'], port=env_var['REDIS_PORT'],
                   db=0, password=env_var['REDIS_PASSWORD'])

redis_host = env_var['REDIS_HOST']
redis_port = env_var['REDIS_PORT']
redis_password = env_var['REDIS_PASSWORD']

broker_url = f"redis://:{redis_password}@{redis_host}:{redis_port}/0"
backend_url = broker_url  # often Celery uses the same Redis for the backend

celery_app = Celery(
    'celery_worker',  # name of this Celery app
    broker=broker_url,
    backend=backend_url
)


class MessagePayload(BaseModel):
    """
    Represents the structure of the message received from RabbitMQ.
    """
    # Define the fields expected in the message
    name: str
    social_security_number: str
    financial_year: int
    if_fallback: bool
    previous_drake_status:DrakeProcessingStatus


def initialize_storage_client() -> storage.Client:
    """Initializes and returns a Google Cloud Storage Client."""
    decoded_service_account = json.loads(
        base64.b64decode(env_var['GOOGLE_SERVICE_ACCOUNT_JSON_ENCODED'])
    )
    return storage.Client.from_service_account_info(decoded_service_account)


def extract_details(file: str):
    """Extract the document ID, page number, and form type from the filename."""
    if file.lower().endswith(".png"):
        # Split the filename using underscores
        parts = file.split("_")
        if len(parts) >= 3:  # Ensure there are enough parts in the filename
            doc_id = parts[0]  # Document ID
            # Extract page number and remove 'p' prefix if present
            page_number = parts[1].lstrip("p")
            # Combine everything after the second underscore
            form_type = "_".join(parts[2:])
            # Remove the file extension
            form_type = form_type.rsplit(".", 1)[0]
            return {
                "doc_id": doc_id,
                "page_number": page_number,
                "form_type": form_type  # Remove the file extension
            }
    return {
        "doc_id": None,
        "page_number": None,
        "form_type": None
    }


def replace_with_underscore(input_string: str) -> str:
    """
    Replaces all spaces and hyphens in the input string with underscores.
    """
    return input_string.replace(" ", "_").replace("-", "_")


def load_json_file(file_path: str) -> dict:
    """Utility to load JSON from a file."""
    if os.path.exists(file_path):
        try:
            with open(file_path, 'r') as f:
                return json.load(f)
        except json.JSONDecodeError:
            logging.warning(
                f"Failed to decode {file_path}. Starting with empty data.")
    return {}


def save_json_file(file_path: str, data: dict) -> None:
    """Utility to save JSON data to a file."""
    try:
        # TODO check if json already exist
        # if yes then download
        # then update these to the json
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2)
        logging.info(f"Data saved to {file_path}.")
    except IOError as e:
        logging.error(f"Error saving data to {file_path}: {e}")


def process_agent_first(parent_folder: str, msg_obj: MessagePayload, file_id: str) -> str:
    """Process PNG files in the parent folder and update the data file."""
    ssn_yr = f"{msg_obj.social_security_number}_{msg_obj.financial_year}"
    ssn_yr_data_file = f'{ssn_yr}.json'
    new_ocr =[]
    data = load_json_file(ssn_yr_data_file)

    file_doc = File.objects(id=ObjectId(file_id)).first()

    # if files has got error during pre process then dont process that
    if (file_doc.status == QueueProcessingStatus.ERROR.value):
        return None

    page_specific_form_types = file_doc.form_types_per_page or {}

    png_files = filter(lambda f: f.lower().endswith(
        ".png"), os.listdir(parent_folder))
    sorted_files = sorted(png_files, key=get_number)

    for file_name in sorted_files:
        details = extract_details(file_name)
        if not details["form_type"]:
            logging.warning(
                f"Skipping file '{file_name}' due to missing form type.")
            continue

        page = Page.objects(file=ObjectId(details["doc_id"]), page_index=(
            int(details["page_number"])+1)).first()
        
        if page.status != QueueProcessingStatus.PROCESSING.value:
            continue

        form_type = replace_with_underscore(details["form_type"]).upper()
        enum_form_type = FormType.__members__.get(
            form_type, FormType.UNKNOWN).value

        print('enum_form_type worker :=====', enum_form_type, form_type,file_name)

        # Note: If an unknown form type is detected by Tesseract, the document is skipped and not sent to the LLM for processing.
        if enum_form_type == FormType.UNKNOWN.value:
            # if admin form type is UNKNOWN then status = processed
            if page_specific_form_types and str(int(details["page_number"]) + 1) in page_specific_form_types:
                form_list = page_specific_form_types[str(
                    int(details["page_number"]) + 1)]
                if form_list and form_list[0] == FormType.UNKNOWN.value:
                    continue

        #     page.update(
        #         status=QueueProcessingStatus.ERROR.value,
        #         form_type=enum_form_type if isinstance(enum_form_type, list) else [enum_form_type],
        #         message=ErrorMessage.UNKNOWN_FORMTYPE_PREPROCESSING.value)
        #     logging.warning(f"Skipping document with Page Number: {details['page_number']}, "
        #         f"Document ID: {details['doc_id']}, File Name: '{file_name}' due to unknown form type.")
        #     continue

        llm_form_types, results, is_success = process_tax_form(
            os.path.join(parent_folder, file_name),
            details["doc_id"],
            details["page_number"],
            str(page["id"]),
            enum_form_type,
            page_specific_form_types[str(int(details["page_number"])+1)]
        )
        logging.info(
            f"LLM DETERMINED FORM TYPE :{llm_form_types} and SYSTEM FORM TYPE: {enum_form_type} and Page Number: {details['page_number']} and DocumentID: {details['doc_id']}")

        if not is_success:
            continue

        # add the elements of results in new_ocr.
        for result in results:
            new_ocr.append(result)

        if len(llm_form_types) == 1:
            llm_form_type = llm_form_types[0]
            if llm_form_type == FormType.UNKNOWN.value:
                page.update(
                    status=QueueProcessingStatus.ERROR.value,
                    form_type=llm_form_type if isinstance(
                        llm_form_type, list) else [llm_form_type],
                    message=ErrorMessage.UNKNOWN_FORMTYPE_LLM_DETECTION.value)
                logging.warning(f"Failed to process tax form '{file_name}'.")
                continue
            elif llm_form_type == FormType.INSTRUCTIONS.value:
                page.update(
                    status=QueueProcessingStatus.PROCESSED.value,
                    form_type=llm_form_type if isinstance(llm_form_type, list) else [llm_form_type])
                logging.warning(f"Skipping document with Page Number: {details['page_number']}, "
                                f"Document ID: {details['doc_id']}, File Name: '{file_name}' due to instructions form type.")
                continue

        page.update(
            status=QueueProcessingStatus.PROCESSED.value,
            form_type=llm_form_types if isinstance(llm_form_types, list) else [llm_form_types])

        llm_form_types = []

        for result in results:
            llm_form_types.append(result["form_details"]["form_type"]["value"])

        for llm_form_type, result in zip(llm_form_types, results):
            data.setdefault(llm_form_type, []).append(
                result)  # TODO check this why this twice

            # data.setdefault(llm_form_type, []).append(result)
            save_json_file(ssn_yr_data_file, data)

    print('result length', len(new_ocr))
    return (ssn_yr_data_file, new_ocr)

# def update_user_status_to_processing(msg_obj: MessagePayload) -> bool:
#     """Update user status to processing when picked by worker."""
#     try:
#         user = User.objects(
#             social_security_number=msg_obj.social_security_number,
#             financial_year=msg_obj.financial_year).first()

#         if user:

#             if user.queue_status ==QueueProcessingStatus.QUEUED.value:
#                 user.update(queue_status=QueueProcessingStatus.PROCESSING.value)
#                 logging.info(f"Updated user status to 'PROCESSING': {user["id"]}")
#                 print(f"Updated user status to 'PROCESSING': {user["id"]}")
#                 return True
#             logging.info(f"User already in {user.queue_status}")
#         else:
#             logging.warning(f"No user found for SSN: {msg_obj.social_security_number} "
#                             f"and financial year: {msg_obj.financial_year}")
#     except Exception as e:
#         logging.exception(f"Error updating user status: {e}")
#     return False


def update_user_status(msg_obj: MessagePayload, new_status: str) -> bool:
    """
    Update the status of a user in the database.

    Args:
        msg_obj (MessagePayload): The message object containing user details (SSN and financial year).
        new_status (str): The new status to assign to the user.

    Returns:
        bool: True if the user status was successfully updated, False otherwise.
    """
    try:
        # Validate the new status
        if new_status not in [status.value for status in QueueProcessingStatus]:
            logging.error(f"Invalid status: {new_status}")
            return False

        # Fetch the user by SSN and financial year
        user = Client.objects(
            social_security_number=msg_obj.social_security_number,
            financial_year=msg_obj.financial_year
        ).first()

        if user:
            # Update the user status
            user.update(queue_status=new_status)
            logging.info(f"Updated user status to '{new_status}': {user['id']}")
            logging.info(f"Updated user status to '{new_status}': {user['id']}")
            return True
        else:
            logging.warning(f"No user found for SSN: {msg_obj.social_security_number} "
                            f"and financial year: {msg_obj.financial_year}")
    except Exception as e:
        logging.exception(f"Error updating user status: {e}")
    return False


# def update_file_status(file_id: str) -> bool:
#     """
#     Updates the status of a file to 'PROCESSING' in the database.

#     Args:
#         file_id (str): The ID of the file.

#     Returns:
#         bool: True if the file status was successfully updated, False otherwise.
#     """
#     try:
#         file=File.objects(id=file_id).first()
#         if file:
#             if file.status == QueueProcessingStatus.QUEUED.value:
#                 file.update(status=QueueProcessingStatus.PROCESSING.value)
#                 print(f"File status updated to 'PROCESSING' for file id {file_id} and file name: {file.file_name}")
#                 logging.info(f"File status updated to 'PROCESSING' for file id {file_id} and file name: {file.file_name}")
#                 return True
#             else:
#                 logging.info(f"File {file_id} is in {file.status}.")
#         else:
#             logging.info(f"File not found for Id: {file_id}")
#     except Exception as e:
#         logging.exceptions(f"Error updating file status: {e}")
#     return False


def update_file_status(file_id: str, new_status: str) -> bool:
    """
    Updates the status of a file in the database.

    Args:
        file_id (str): The ID of the file.
        new_status (str): The new status to assign to the file.

    Returns:
        bool: True if the file status was successfully updated, False otherwise.
    """
    try:
        # Validate new_status
        if new_status not in [status.value for status in QueueProcessingStatus]:
            logging.error(f"Invalid status: {new_status}")
            return False

        file = File.objects(id=ObjectId(file_id)).first()
        if file:
            # Update the file status
            file.update(status=new_status)
            logging.info(
                f"File status updated to '{new_status}' for file id {file_id} and file name: {file.file_name}")
            return True
        else:
            logging.info(f"File not found for Id: {file_id}")
    except Exception as e:
        logging.exception(f"Error updating file status: {e}")
    return False


def sync_user_data(user_doc, ssn, year):
    """
    Downloads the file for a user if it exists.

    Args:
        user_doc: The user document fetched from the database.
        ssn (str): Social Security Number of the user.
        year (str): The financial year for which the file is required.

    Returns:
        dict: A dictionary with keys 'success', 'error', and 'data'.
    """
    if not user_doc:
        return {
            "success": False,
            "error": "User document does not exist.",
            "data": None
        }

    file_path = user_doc.output_path
    if not file_path:
        return {
            "success": False,
            "error": "No file path found in user document.",
            "data": None
        }

    storage_client = initialize_storage_client()
    bucket = storage_client.get_bucket(env_var['GCS_BUCKET_NAME'])

    # Create the filename
    ssn_yr = f"{ssn}_{year}"
    ssn_yr_data_file = f"{ssn_yr}.json"

    blob = bucket.blob(file_path)

    try:
        blob.download_to_filename(ssn_yr_data_file)
        return {
            "success": True,
            "error": None,
            "data": {"file_path": ssn_yr_data_file}
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "data": None
        }


def update_user_status_based_on_files(msg_obj: MessagePayload):
    """
    Updates the status of a user based on the statuses of their associated files.

    Args:
        msg_obj (MessagePayload): An object containing user details (SSN and financial year).
    """
    try:
        # Fetch the user based on SSN and financial year
        user = Client.objects(
            social_security_number=msg_obj.social_security_number,
            financial_year=msg_obj.financial_year
        ).first()

        if not user:
            logging.warning(
                f"No user found for SSN: {msg_obj.social_security_number} and financial year: {msg_obj.financial_year}"
            )
            return {
                "success": False,
                "error": "No user found for user ID",
                "status": QueueProcessingStatus.ERROR.value
            }

        # Fetch all files associated with the user
        files = File.objects(client=user)

        if not files:
            logging.warning(f"No files found for user ID: {str(user.id)}")
            return {
                "success": False,
                "error": f"No files found for user ID: {str(user.id)}",
                "status": QueueProcessingStatus.ERROR.value
            }

        # Collect statuses of associated files
        file_statuses = {f.status for f in files}

        # ----- New optional check for NO_FORMS at the user level -----
        # If you want to mark the user as NO_FORMS when all files have no recognized forms:
        if all(status == QueueProcessingStatus.NO_FORMS.value for status in file_statuses):
            user.update(queue_status=QueueProcessingStatus.NO_FORMS.value)
            logging.info(f"User ID {user.id} status updated to 'NO_FORMS'.")
            return {
                "success": True,
                "error": None,
                "status": QueueProcessingStatus.NO_FORMS.value
            }
        
        # 2) If exactly two unique statuses and one is NO_FORMS => user status = the other status
        if len(file_statuses) == 2 and QueueProcessingStatus.NO_FORMS.value in file_statuses:
            # Remove NO_FORMS from the set to isolate the other status
            other_status_set = file_statuses - {QueueProcessingStatus.NO_FORMS.value}
            other_status = next(iter(other_status_set))

            # If this "other" status is among processed/partial/error, assign accordingly
            # Otherwise (e.g., includes queued or processing), skip final update
            if other_status == QueueProcessingStatus.PROCESSED.value:
                user.update(queue_status=QueueProcessingStatus.PROCESSED.value)
                logging.info(f"User ID {user.id} status updated to 'PROCESSED'.")
                return {
                    "success": True,
                    "error": None,
                    "status": QueueProcessingStatus.PROCESSED.value
                }
            elif other_status == QueueProcessingStatus.ERROR.value:
                user.update(queue_status=QueueProcessingStatus.ERROR.value,
                            message=ErrorMessage.USER_ERROR_PROCESSED.value)
                logging.info(f"User ID {user.id} status updated to 'ERROR'.")
                return {
                    "success": True,
                    "error": ErrorMessage.USER_ERROR_PROCESSED.value,
                    "status": QueueProcessingStatus.ERROR.value
                }
            elif other_status == QueueProcessingStatus.PARTIALLY_PROCESSED.value:
                user.update(
                    queue_status=QueueProcessingStatus.PARTIALLY_PROCESSED.value,
                    message=ErrorMessage.USER_ALL_FILES_PARTIALLY_PROCESSED.value
                )
                logging.info(f"User ID {user.id} status updated to 'PARTIALLY_PROCESSED'.")
                return {
                    "success": True,
                    "error": ErrorMessage.USER_ALL_FILES_PARTIALLY_PROCESSED.value,
                    "status": QueueProcessingStatus.PARTIALLY_PROCESSED.value
                }

        # Determine user status based on the remaining statuses
        new_status = None
        new_message = None

        # If all are PROCESSED => user is PROCESSED
        if all(status == QueueProcessingStatus.PROCESSED.value for status in file_statuses):
            new_status = QueueProcessingStatus.PROCESSED.value

        # If all are ERROR => user is ERROR
        elif all(status == QueueProcessingStatus.ERROR.value for status in file_statuses):
            new_status = QueueProcessingStatus.ERROR.value
            new_message = ErrorMessage.USER_ERROR_PROCESSED.value

        # If all are PARTIALLY_PROCESSED => user is PARTIALLY_PROCESSED
        elif all(status == QueueProcessingStatus.PARTIALLY_PROCESSED.value for status in file_statuses):
            new_status = QueueProcessingStatus.PARTIALLY_PROCESSED.value
            new_message = ErrorMessage.USER_ALL_FILES_PARTIALLY_PROCESSED.value

        # If a mix of PROCESSED, ERROR, or PARTIALLY_PROCESSED => user is PARTIALLY_PROCESSED
        elif file_statuses.issubset({
            QueueProcessingStatus.PROCESSED.value,
            QueueProcessingStatus.ERROR.value,
            QueueProcessingStatus.PARTIALLY_PROCESSED.value
        }):
            new_status = QueueProcessingStatus.PARTIALLY_PROCESSED.value
            new_message = ErrorMessage.USER_SOME_FILES_PARTIALLY_PROCESSED.value

        else:
            # Mixed statuses that likely include queued/processing => no final update
            logging.info(
                f"User ID {user.id} has mixed file statuses (including queued/processing); no status update applied."
            )
            return {
                "success": False,
                "error": "no status update applied.",
                "status": QueueProcessingStatus.ERROR.value
            }

        # Update the user's status and message
        update_fields = {"queue_status": new_status}
        if new_message:
            update_fields["message"] = new_message

        user.update(**update_fields)
        logging.info(f"User ID {user.id} status updated to '{new_status}'.")

        return {
            "success": True,
            "error": new_message if new_message else None,
            "status": new_status
        }

    except Exception as e:
        logging.exception(f"Error updating user status based on files: {e}")
        return {
            "success": False,
            "error": "internal server error",
            "status": QueueProcessingStatus.ERROR.value
        }



def download_files(folder_path: str, msg_obj: MessagePayload):
    file_mapping = {}
    try:
        storage_client = initialize_storage_client()
        bucket = storage_client.get_bucket(env_var['GCS_BUCKET_NAME'])
        # Creating the download path using the template from constants
        local_download_path = PROCESSING_FOLDER_TEMPLATE.format(
            ssn=msg_obj.social_security_number, year=msg_obj.financial_year
        )
        os.makedirs(local_download_path, exist_ok=True)

        blobs = bucket.list_blobs(prefix=folder_path)
        folder_name = MASKED_FILES_OUTPUT_FOLDER_TEMPLATE.format(
            social_security_number=msg_obj.social_security_number, financial_year=msg_obj.financial_year
        )
        user = Client.get(social_security_number=msg_obj.social_security_number,
                        financial_year=msg_obj.financial_year)
        files = File.objects(
            client=user["id"], status=QueueProcessingStatus.QUEUED.value)
        for file in files:
            blob = bucket.blob(file.input_file_path)
            local_file_path = os.path.join(
                local_download_path, os.path.basename(file.input_file_path))

            try:
                blob.download_to_filename(local_file_path)
                logging.info(
                    F"Downloaded: {file.input_file_path} to {local_file_path}")
                file_mapping[local_file_path] = file["id"]
                # update_file_status(file["id"]) #todo

                # else:
                #     logging.warning(f"Skipping file processing for {file["id"]} due to status update failure")
            except Exception as download_error:
                logging.error(
                    f"Failed to download file {file.input_file_path}: {download_error}")
    except Exception as e:
        logging.error(
            f"Error during file download process for folder {folder_path}: {e}")
    return file_mapping, folder_name


def cleanup_files_and_folders(json_files: list, masked_files_folder: str, downloaded_files_folder: str, processed_output_folder: str, non_masked_folder: str):
    """
    Cleans up files and folders created during execution.

    Args:
        json_files (list): List of paths to JSON files to be deleted.
        masked_files_folder (str): Path to the folder containing masked files.
        downloaded_files_folder (str): Path to the folder containing downloaded files.
        processed_output_folder (str): Path to the folder containing processed output files.

    Returns:
        None
    """
    try:
        # 1. Delete JSON files
        for json_file in json_files:
            if os.path.exists(json_file):
                os.remove(json_file)
                logging.info(f"Deleted JSON file: {json_file}")
            else:
                logging.warning(f"JSON file not found: {json_file}")

        # 2. Delete Masked Files Folder
        if os.path.exists(masked_files_folder):
            shutil.rmtree(masked_files_folder)
            logging.info(f"Deleted masked files folder: {masked_files_folder}")
        else:
            logging.warning(
                f"Masked files folder not found: {masked_files_folder}")

        # 3. Delete Downloaded Files Folder
        if os.path.exists(downloaded_files_folder):
            shutil.rmtree(downloaded_files_folder)
            logging.info(
                f"Deleted downloaded files folder: {downloaded_files_folder}")
        else:
            logging.warning(
                f"Downloaded files folder not found: {downloaded_files_folder}")

        # 4. Delete Processed Output Folder
        if os.path.exists(processed_output_folder):
            shutil.rmtree(processed_output_folder)
            logging.info(
                f"Deleted processed output folder: {processed_output_folder}")
        else:
            logging.warning(
                f"Processed output folder not found: {processed_output_folder}")

            # 5. Non Masked Folder
        if os.path.exists(non_masked_folder):
            shutil.rmtree(non_masked_folder)
            logging.info(
                f"Deleted processed output folder: {non_masked_folder}")
        else:
            logging.warning(
                f"Processed output folder not found: {non_masked_folder}")

    except Exception as e:
        logging.error(f"Error during cleanup: {e}")


# def setup_rabbitmq_connection():
#     """
#     Establishes connection to RabbitMQ server.

#     Returns:
#         RabbitMQ channel
#     """
#     # Get the RabbitMQ connection parameters from environment variables
#     rabbitmq_host = env_var.get("RABBITMQ_HOST")
#     rabbitmq_port = int(env_var.get("RABBITMQ_PORT"))
#     queue_name = env_var.get("QUEUE_NAME")
#     username = env_var.get("RABBITMQ_USERNAME")
#     password = env_var.get("RABBITMQ_PASSWORD")

#     # Create a connection to the RabbitMQ server with credentials
#     credentials = pika.PlainCredentials(username, password)
#     connection = pika.BlockingConnection(
#         pika.ConnectionParameters(
#             host=rabbitmq_host,
#             port=rabbitmq_port,
#             credentials=credentials,
#             heartbeat=0,
#         )
#     )
#     channel = connection.channel()

#     # Declare the queue (this is idempotent, won't create if exists)
#     channel.queue_declare(queue=queue_name, durable=False)

#     # Set up the consumer to handle one message at a time
#     channel.basic_qos(prefetch_count=1)

#     return channel


# def callback(ch, method, properties, body) -> None:
#     """
#     Callback function to process incoming messages from RabbitMQ.

#     Args:
#         ch: RabbitMQ channel
#         method: RabbitMQ method
#         properties: RabbitMQ properties
#         body: Message body

#     Returns:
#         None
#     """
#     message = {}

#     try:
#         file_mapping = {}

#         json_file_path = {}

#         logging.info(f"Received message: {body}")

#         # Parse the message body and validate against the defined model
#         message = json.loads(body)
#         msg_obj = MessagePayload(**message)
#         logging.info(f"Processing message: {message}")

#         if update_user_status(msg_obj, QueueProcessingStatus.PROCESSING.value):
#             folder_path = f"{msg_obj.social_security_number}/{msg_obj.financial_year}"
#             file_mapping,folder_name=download_files(folder_path,msg_obj)
#             for local_file_path, file_id in file_mapping.items():
#                 update_file_status(file_id,QueueProcessingStatus.PROCESSING.value)
#                 process_file(local_file_path,file_id,folder_name)

#         file_id_list=list(file_mapping.values())
#         for files in file_id_list:
#             base_folder_path = os.path.join(env_var["BASE_STORE_DIR"],
#             f"{msg_obj.social_security_number}_{msg_obj.financial_year}"
#             )
#             file_path=os.path.join(base_folder_path,str(files))
#             json_file_path = process_agent_first(file_path,msg_obj)
#         print("Agent Stage 1 completed !")

#         final_result=run_deduplication(json_file_path)
#         print("Agent stage 2 completed !")

#         #change user/file status
#         # files id = for local_file_path, file_id in file_mapping.items():
#         for local_file_path, file_id in file_mapping.items():
#             file = File.objects(id=file_id).first()
#             if not file:
#                 logging.warning(f"File not found for ID: {file_id}")
#                 continue

#             # Get all pages associated with the file
#             pages = Page.objects(file=file)

#             if not pages:
#                 logging.warning(f"No pages found for file ID: {file_id}")
#                 continue

#             # Collect statuses of associated pages
#             page_statuses = {page.status for page in pages}

#             # Determine file status based on page statuses
#             if all(status == QueueProcessingStatus.PROCESSED.value for status in page_statuses):
#                 file.update(status=QueueProcessingStatus.PROCESSED.value)
#                 logging.info(f"File ID {file_id} marked as 'PROCESSED'.")
#             elif all(status in [QueueProcessingStatus.PROCESSED.value, QueueProcessingStatus.ERROR.value] for status in page_statuses):
#                 file.update(status=QueueProcessingStatus.PARTIALLY_PROCESSED.value)
#                 logging.info(f"File ID {file_id} marked as 'PARTIALLY_PROCESSED'.")
#             else:
#                 logging.info(f"File ID {file_id} has mixed page statuses; no status update applied.")

#         # user = msg_obj
#         update_user_status_based_on_files(msg_obj)


#         # if os.path.exists(json_file_path):
#         #     upload_file_to_bucket(json_file_path)


#     except ValidationError as ve:
#         logging.error(f"Message validation failed: {ve}")
#     except Exception as e:
#         logging.error(f"Error processing message: {e} === {body}")

#     finally:
#         ch.basic_ack(delivery_tag=method.delivery_tag)
#         logging.info(f"Removing message from the queue {body}")

#         # Stop consuming messages after processing the first one
#         # ch.stop_consuming()


# def consume_messages(channel):
#     """
#     Start consuming messages from RabbitMQ.

#     Args:
#         channel: RabbitMQ channel

#     Returns:
#         None
#     """

#     queue_name = env_var.get("QUEUE_NAME")

#     # Start consuming messages
#     logging.info("Waiting for a single message. To exit, press CTRL+C")
#     channel.basic_consume(queue=queue_name, on_message_callback=callback)
#     channel.start_consuming()


# if __name__ == "__main__":

#     # Establish connection to RabbitMQ
#     rabbitmq_channel = setup_rabbitmq_connection()

#     # Consume messages
#     consume_messages(rabbitmq_channel)


def process_user_task(message: Dict):
    """
    This function is invoked by RQ workers when a new job is enqueued.
    It's the equivalent of the RabbitMQ callback.
    """

    msg_obj = MessagePayload(**message)
    user_doc = Client.objects(
        social_security_number=msg_obj.social_security_number,
        financial_year=msg_obj.financial_year
    ).first()

    # if_fallback = msg_obj.get("if_fallback", False)
    if_fallback = msg_obj.if_fallback 

    if(user_doc.is_aborted == True):
        print('user aborted before processing')
        user_doc.update(queue_status=QueueProcessingStatus.ABORTED.value, drake_status = DrakeProcessingStatus.ABORTED.value)
        return

    try:
        logging.info(f"Processing message: {msg_obj}")

        if update_user_status(msg_obj, QueueProcessingStatus.PROCESSING.value):
            folder_path = f"{msg_obj.social_security_number}/{msg_obj.financial_year}"
            file_mapping, folder_name = download_files(folder_path, msg_obj)
            # file_mapping contains file id TODO
            for local_file_path, file_id in file_mapping.items():
                update_file_status(
                    file_id, QueueProcessingStatus.PROCESSING.value)
                process_file(local_file_path, file_id, folder_name,if_fallback)

        file_id_list = list(file_mapping.values())
        json_files = []
        processed_form_ids =[]
        sync_user_data(user_doc, msg_obj.social_security_number,
                       msg_obj.financial_year)
        for file_id in file_id_list:
            base_folder_path = os.path.join(
                env_var["BASE_STORE_DIR"],
                f"{msg_obj.social_security_number}_{msg_obj.financial_year}"
            )
            file_path = os.path.join(base_folder_path, str(file_id))
            (json_file_path,new_ocr) = process_agent_first(file_path, msg_obj, file_id)

            for result in new_ocr:
                if "form_details" in result and "form_id" in result["form_details"] and "value" in result["form_details"]["form_id"]:
                    processed_form_ids.append(result["form_details"]["form_id"]["value"])

            if (json_file_path != None):
                json_files.append(json_file_path)

        logging.info("Agent Stage 1 completed !")

        final_result = run_deduplication(json_file_path)

        total_form_ids = []
        
        for form_type, form_list in final_result.items():
            for form in form_list:
                if "form_details" in form and "form_id" in form["form_details"] and "value" in form["form_details"]["form_id"]:
                    total_form_ids.append(form["form_details"]["form_id"]["value"])

        
        final_processed_form_ids = []

        for form_id in processed_form_ids:
            if form_id in total_form_ids:
                final_processed_form_ids.append(form_id)

        print('processed_form_ids',processed_form_ids)
        print('total_form_ids',total_form_ids)
        print('final_processed_form_ids',final_processed_form_ids)

        logging.info("Agent stage 2 completed !")

        for local_file_path, file_id in file_mapping.items():
            file_obj = File.objects(id=file_id).first()
            if not file_obj:
                logging.warning(f"File not found for ID: {file_id}")
                continue

            pages = Page.objects(file=file_obj)
            if not pages:
                logging.warning(f"No pages found for file ID: {file_id}")
                # If absolutely no pages exist at all, you might treat this as NO_FORMS or leave it as is:
                file_obj.update(status=QueueProcessingStatus.NO_FORMS.value)
                logging.info(f"File ID {file_id} marked as 'NO_FORMS' (no pages).")
                continue

            # 1) Identify recognized pages (i.e., at least one form_type is neither UNKNOWN nor INSTRUCTIONS).
            recognized_pages = []
            for page in pages:
                # If any of the page's form types is a recognized form (not UNKNOWN or INSTRUCTIONS), this page is "recognized"
                if any(
                    ft not in [FormType.UNKNOWN.value, FormType.INSTRUCTIONS.value]
                    for ft in page.form_type
                ):
                    recognized_pages.append(page)

            # 2) If there are no recognized pages at all, set status to NO_FORMS.
            if not recognized_pages:
                file_obj.update(status=QueueProcessingStatus.NO_FORMS.value)
                logging.info(f"File ID {file_id} marked as 'NO_FORMS' (only unknown/instruction pages).")
                continue

            # 3) Extract statuses of recognized pages
            recognized_page_statuses = {p.status for p in recognized_pages}

            # 4) Apply the new rules, ignoring unknown/instruction pages:
            #    - If all recognized pages are processed => file processed
            #    - If all recognized pages are error => file error
            #    - If recognized pages are a mix of processed and error => partial
            #    - Else => do not update (e.g., pages still queued or processing)
            if all(st == QueueProcessingStatus.PROCESSED.value for st in recognized_page_statuses):
                file_obj.update(status=QueueProcessingStatus.PROCESSED.value)
                logging.info(f"File ID {file_id} marked as 'PROCESSED'.")
            elif all(st == QueueProcessingStatus.ERROR.value for st in recognized_page_statuses):
                # Edge Case 2: All recognized forms failed
                file_obj.update(status=QueueProcessingStatus.ERROR.value)
                logging.info(f"File ID {file_id} marked as 'ERROR' (all recognized forms errored).")
            elif recognized_page_statuses.issubset({QueueProcessingStatus.PROCESSED.value,
                                                QueueProcessingStatus.ERROR.value}):
                # We have a mix of PROCESSED and ERROR => partial
                file_obj.update(status=QueueProcessingStatus.PARTIALLY_PROCESSED.value)
                logging.info(f"File ID {file_id} marked as 'PARTIALLY_PROCESSED'.")
            else:
                # Recognized pages have statuses that include queued/processing
                # => No final update is applied in this scenario
                logging.info(f"File ID {file_id} has unready recognized pages (queued/processing); no status update.")

        response = update_user_status_based_on_files(msg_obj)

        logging.info(f"User status updated: {response}")
        print(f"User status updated: {response}")
        if response and (
            (not response["success"]) or
                (response["success"] and response["status"] and response["status"] == QueueProcessingStatus.ERROR.value)):
            cleanup_files_and_folders(
                json_files=json_files,
                masked_files_folder=MASKED_FILES_OUTPUT_FOLDER_TEMPLATE.format(
                    social_security_number=msg_obj.social_security_number,
                    financial_year=msg_obj.financial_year
                ),
                downloaded_files_folder=PROCESSING_FOLDER_TEMPLATE.format(
                    ssn=msg_obj.social_security_number,
                    year=msg_obj.financial_year
                ),
                processed_output_folder=os.path.join(
                    env_var["BASE_STORE_DIR"],
                    f"{msg_obj.social_security_number}_{msg_obj.financial_year}"
                ),
                non_masked_folder=os.path.join(
                    NON_MASKED_FOLDER,
                    f"{msg_obj.social_security_number}_{msg_obj.financial_year}"
                ),

            )

            logging.warning("error with user status")
            print("error with user status")
            return

        # upload only all files in basedir folder of given__ssn_finanacialyear folder
        folder_path = os.path.join(
            env_var["BASE_STORE_DIR"],
            f"{msg_obj.social_security_number}_{msg_obj.financial_year}"
        )

        # Check if the folder exists
        if os.path.exists(folder_path) and os.path.isdir(folder_path):
            # List all file names in the folder
            file_names = [file for file in os.listdir(
                folder_path) if os.path.isfile(os.path.join(folder_path, file))]
            logging.info("Files in the folder:")
            # logging.info('file_names', file_names)
            for file in file_names:
                masked_file_path = f"{msg_obj.social_security_number}/{msg_obj.financial_year}/masked/{file}"
                masked_file_id = file.split("_")[0]

                file_doc = File.objects(id=masked_file_id).first()
                file_doc.update(masked_file_path=masked_file_path)
                print("File record updated for ", masked_file_path)

                # upload_file_to_bucket(
                #     f"{folder_path}/{file}", masked_file_path, env_var['GCS_BUCKET_NAME'])
                # print(file)
        else:
            # print(f"The folder '{folder_path}' does not exist or is not a directory.")
            logging.info(
                f"The folder '{folder_path}' does not exist or is not a directory.")

        output_file_path = f"{msg_obj.social_security_number}/{msg_obj.financial_year}/output/{json_file_path}"

        if if_fallback:
            docs = [str(file_id) for file_id in file_id_list]
            page_ids = []
            # get all pages of file_id_list
            for file_id in file_id_list:
                file_doc = File.objects(id=file_id).first()
                pages = Page.objects(file=file_doc)
                page_ids.append([str(page.id) for page in pages])

        else:
            docs = [str(file_id) for file_id in file_id_list]
            page_ids = [[] for _ in docs]

        rdp_message = {
            "gcp_json_path": output_file_path,
            "social_security_number": msg_obj.social_security_number,
            "social_security_number_decrypted": deterministic_decrypt(msg_obj.social_security_number),
            "client_id": str(user_doc.id),
            "financial_year": msg_obj.financial_year,
            "doc_ids": docs,
            "page_ids": page_ids,
            "processed_form_ids":final_processed_form_ids
        }

        user_doc = Client.objects(social_security_number=msg_obj.social_security_number,
                                financial_year=msg_obj.financial_year).first()
        user_doc.update(output_path=output_file_path)

        upload_file_to_bucket(json_file_path, output_file_path, env_var['GCS_BUCKET_NAME'])

        user_doc = Client.objects(social_security_number=msg_obj.social_security_number,
        financial_year=msg_obj.financial_year).first()

        if(user_doc.is_aborted == True):
            print("user aborted")
            user_doc.update(queue_status=QueueProcessingStatus.ABORTED.value, drake_status = DrakeProcessingStatus.ABORTED.value)

        if(user_doc.is_review_required):
            print("user review required")
            # user_doc.update(queue_status=QueueProcessingStatus.PARTIALLY_PROCESSED.value)
        elif(user_doc.is_aborted == False):
            
            if len(final_processed_form_ids) == 0:
                user_doc.update(drake_status=msg_obj.previous_drake_status)
            else:
                result = celery_app.send_task(
                    name="process_user_task",  # the EXACT name from the remote decorator
                    args=[rdp_message],               # or pass named params if needed
                    queue=env_var["RDP_QUEUE_NAME"] if env_var.get(
                        "RDP_QUEUE_NAME") else "drake-local"  # the queue to send the task to
                )
                user_doc.update(drake_status=DrakeProcessingStatus.QUEUED.value)
            # logging.info("Task dispatched. Task ID:", {
            #             "user_id": result["id"], "doc_ids": file_id_list})
        # TODO send the doc array

        # Cleanup after processing
        cleanup_files_and_folders(
            json_files=json_files,
            masked_files_folder=MASKED_FILES_OUTPUT_FOLDER_TEMPLATE.format(
                social_security_number=msg_obj.social_security_number,
                financial_year=msg_obj.financial_year
            ),
            downloaded_files_folder=PROCESSING_FOLDER_TEMPLATE.format(
                ssn=msg_obj.social_security_number,
                year=msg_obj.financial_year
            ),
            processed_output_folder=os.path.join(
                env_var["BASE_STORE_DIR"],
                f"{msg_obj.social_security_number}_{msg_obj.financial_year}"
            ),
            non_masked_folder=os.path.join(
                NON_MASKED_FOLDER,
                f"{msg_obj.social_security_number}_{msg_obj.financial_year}"
            ),

        )

        # If needed, upload result to bucket
        # if os.path.exists(json_file_path):
        #     upload_file_to_bucket(json_file_path)

    except ValidationError as ve:
        logging.error(f"Message validation failed: {ve}")
    except Exception as e:
        import traceback
        traceback.print_exc()
        user_doc.update(queue_status=QueueProcessingStatus.ERROR.value)
        logging.error(f"Error processing message: {e} === {message}")


# Add the project root directory to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)


# def main():
#     try:
#         # Get Redis credentials from environment variables
#         redis_host = os.getenv('REDIS_HOST')
#         redis_port = os.getenv('REDIS_PORT')
#         redis_password = os.getenv('REDIS_PASSWORD')

#         # Validate that Redis credentials are present
#         if not all([redis_host, redis_port, redis_password]):
#             raise ValueError(
#                 "Missing Redis configuration in environment variables.")

#         # Construct Redis URL
#         redis_url = f"redis://:{redis_password}@{redis_host}:{redis_port}/0"

#         logging.info(f"Connecting to Redis at {redis_url}")

#         # Create Redis connection
#         redis_conn = Redis.from_url(redis_url)

#         # Specify the queue name (default to 'default' if not set)
#         queue_name = os.getenv('QUEUE_NAME', 'default')

#         # Create and start the worker
#         worker = Worker(["high", queue_name], connection=redis_conn)
#         logging.info(f"RQ Worker started. Listening to queue: {queue_name}")
#         worker.work(with_scheduler=True)
#     except Exception as e:
#         import traceback
#         traceback.print_exc()
#         logging.error(f"Error in worker: {e}")
#         raise


# if __name__ == "__main__":
#     try:
#         main()
#     except KeyboardInterrupt:
#         logging.info("\nWorker stopped.")
#     except Exception as e:
#         logging.error(f"An error occurred: {e}")
#         # import traceback
#         # traceback.print_exc()


# process_user_task({
#     "name": "Composite",
#     "social_security_number":"***********",
#     "financial_year":2024
# })

# msg_obj = MessagePayload(
#     name="Composite",
#     social_security_number="***********",
#     financial_year=2025
# )

# folder_path = f"{msg_obj.social_security_number}/{msg_obj.financial_year}"
# print(download_files(folder_path, msg_obj))

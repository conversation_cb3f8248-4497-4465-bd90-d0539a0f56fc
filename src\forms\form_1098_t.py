from pydantic import BaseModel, Field
from src.forms.base_form import TextField, YearField, CurrencyField, CheckboxField, FormDetails, ZipCodeField, EINField


class FilerInformation(BaseModel):
    filer_or_educational_institution_name: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    filer_or_educational_institution_street_address: TextField = Field(
        default_factory=lambda: TextField(type="text"), description="The filer's street address as listed on the form without city, state, zipcode.")
    filer_or_educational_institution_city: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    filer_or_educational_institution_state: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    filer_or_educational_institution_zip_code: ZipCodeField = Field(
        default_factory=lambda: ZipCodeField(type="zipcode"))
    # filer_or_educational_institution_telephone_number: ContactField = Field(
    #     default_factory=lambda: ContactField(type="contact"))
    filer_or_educational_institution_employer_identification_no: EINField = Field(default_factory=lambda: EINField(type="einfield"))


class FinancialFields(BaseModel):
    payments_received_for_qualified_tuition_and_related_expenses: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="1", type="currency"))
    adjustments_made_for_a_prior_year: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="4", type="currency"))
    scholarships_or_grants: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="5", type="currency"))
    adjustments_to_scholarships_or_grants_for_a_prior_year: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="6", type="currency"))
    ins_contract_reimb_or_refund: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="10", type="currency"))


class AdditionalFields(BaseModel):
    corrected: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    checked_if_the_amount_in_box_1_includes_amounts_for_an_academic_period: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox", sequence="7"))
    checked_if_at_least_half_time_student: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox", sequence="8"))
    checked_if_a_graduate_student: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox", sequence="9"))
    omb_number: TextField = Field(
        default_factory=lambda: TextField(type="text"))


# Main Pydantic model for Form 1099-INT
class Form1098T(BaseModel):
    filer_or_educational_institution_information: FilerInformation = Field(
        default_factory=FilerInformation)
    form_details: FormDetails = Field(default_factory=FormDetails)
    financial_fields: FinancialFields = Field(default_factory=FinancialFields)
    additional_fields: AdditionalFields = Field(
        default_factory=AdditionalFields)

from src.config.pubsub import pubsub_configuration
from src.config.logger import logging


def get_pubsub_provider(provider):
    """
    Get the pubsub provider object based on the provided provider name.

    Args:
    - provider (str): The name of the pubsub provider to retrieve.

    Returns:
    - provider_object: The pubsub provider object.

    Raises:
    - ValueError: If the provided provider name is invalid.
    """
    try:
        # Attempt to retrieve the provider object from the configuration
        provider_object = pubsub_configuration[provider]['provider']

    except Exception as e:
        # Log the error along with the configuration for debugging purposes
        logging.error(f"Invalid provider: {provider}. Configuration: {pubsub_configuration} = {e}")

        # Raise a ValueError with a meaningful error message
        raise ValueError(f"Invalid PubSub provider: {provider}")

    return provider_object

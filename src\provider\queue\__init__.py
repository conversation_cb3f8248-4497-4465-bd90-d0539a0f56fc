from src.config.queue import queue_configuration
import logging


def get_queue_provider(provider):
    """
    Get the queue provider object based on the provided provider name.

    Args:
    - provider (str): The name of the queue provider to retrieve.

    Returns:
    - provider_object: The queue provider object.

    Raises:
    - ValueError: If the provided provider name is invalid.
    """
    try:
        # Attempt to retrieve the provider object from the configuration
        provider_object = queue_configuration[provider]['provider']

    except Exception as e:
        # Log the error along with the configuration for debugging purposes
        logging.error(f"Invalid provider: {provider}. Configuration: {queue_configuration} = {e}")

        # Raise a ValueError with a meaningful error message
        raise ValueError(f"Invalid Queue provider: {provider}")

    return provider_object

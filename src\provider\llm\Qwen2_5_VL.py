import base64
import os
import requests
import json
from huggingface_hub import InferenceClient

class QwenVLProvider:
    def __init__(self, api_key: str, model: str = "Qwen/Qwen2.5-VL-72B-Instruct"):
        """
        Initialize the Qwen VL provider with your Hugging Face token.
        
        Args:
            api_key (str): Your Hugging Face API token
            model (str): Model identifier, defaults to "Qwen/Qwen2.5-VL-72B-Instruct"
        """
        self.api_key = api_key
        self.model = model
        self.client = InferenceClient(
            provider="hyperbolic",
            api_key=api_key,
        )

    def generate_content(self, image_path: str, prompt: str, retry_count: int = 3, max_tokens: int = 32000):
        """
        Process an image with a prompt using Qwen2.5-VL-72B-Instruct model.
        
        Args:
            image_path (str): Path to the local image file
            prompt (str): Text prompt for the model
            retry_count (int): Number of times to retry if the API call fails
            max_tokens (int): Maximum number of tokens to generate
            
        Returns:
            dict: Response with content, success flag, and error message if any
        """
        # Validate image format
        extension = os.path.splitext(image_path)[1].lower()
        if extension not in [".jpg", ".jpeg", ".png"]:
            return {
                "response": None,
                "error": f"Unsupported image format: {extension}",
                "success": False
            }
        
        # Prepare base64 image
        try:
            with open(image_path, "rb") as image_file:
                encoded_image = base64.b64encode(image_file.read()).decode("utf-8")
                # Create a data URL
                mime_type = "image/jpeg" if extension in [".jpg", ".jpeg"] else "image/png"
                image_data_url = f"data:{mime_type};base64,{encoded_image}"
        except Exception as e:
            return {
                "response": None,
                "error": f"Error reading or encoding the image: {str(e)}",
                "success": False
            }
        
        # Try to make API calls with retry logic
        for attempt in range(retry_count):
            try:
                # Prepare the message with local image
                messages = [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": image_data_url
                                }
                            }
                        ]
                    }
                ]
                
                # Make the API call
                completion = self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    max_tokens=max_tokens,
                )
                
                # Extract and return response
                return {
                    "response": completion.choices[0].message.content,
                    "success": True,
                    "error": ""
                }
                
            except Exception as e:
                print(f"Attempt {attempt + 1} encountered an error: {str(e)}")
                
                if attempt < retry_count - 1:
                    print("Retrying...")
                
        # If all retries failed
        return {
            "response": None,
            "error": "All retry attempts failed when calling Qwen VL API",
            "success": False
        }
    
# if __name__ == "__main__":
#     # Initialize the provider with your Hugging Face token
#     qwen_provider = QwenVLProvider(api_key="bfYEtLgIwCYuI")

#     # Call the model with a local image file
#     result = qwen_provider.generate_content(
#         image_path="1099B2.png",
#         prompt= """
#                 You are an OCR assistant tasked with extracting values from a Form 1099-B tax form image.Document id is 6778defd5957a9db5bb13984 and page number is 0
#                         Match all extracted values to the fields provided in the template below.

#                         Template:
#                         {
#   "$defs": {
#     "BoxType": {
#       "enum": [
#         "box A",
#         "box B",
#         "box C",
#         "box D",
#         "box E",
#         "box F"
#       ],
#       "title": "BoxType",
#       "type": "string"
#     },
#     "CurrencyField": {
#       "properties": {
#         "value": {
#           "anyOf": [
#             {
#               "type": "string"
#             },
#             {
#               "type": "null"
#             }
#           ],
#           "default": null,
#           "title": "Value"
#         },
#         "sequence": {
#           "anyOf": [
#             {
#               "type": "string"
#             },
#             {
#               "type": "null"
#             }
#           ],
#           "default": null,
#           "title": "Sequence"
#         },
#         "type": {
#           "default": "currency",
#           "title": "Type",
#           "type": "string"
#         }
#       },
#       "title": "CurrencyField",
#       "type": "object"
#     },
#     "DateField": {
#       "properties": {
#         "value": {
#           "anyOf": [
#             {
#               "type": "string"
#             },
#             {
#               "type": "null"
#             }
#           ],
#           "default": null,
#           "title": "Value"
#         },
#         "sequence": {
#           "anyOf": [
#             {
#               "type": "string"
#             },
#             {
#               "type": "null"
#             }
#           ],
#           "default": null,
#           "title": "Sequence"
#         },
#         "type": {
#           "default": "date",
#           "title": "Type",
#           "type": "string"
#         }
#       },
#       "title": "DateField",
#       "type": "object"
#     },
#     "EINField": {
#       "properties": {
#         "value": {
#           "anyOf": [
#             {
#               "type": "string"
#             },
#             {
#               "type": "null"
#             }
#           ],
#           "default": null,
#           "title": "Value"
#         },
#         "sequence": {
#           "anyOf": [
#             {
#               "type": "string"
#             },
#             {
#               "type": "null"
#             }
#           ],
#           "default": null,
#           "title": "Sequence"
#         },
#         "type": {
#           "default": "einfield",
#           "title": "Type",
#           "type": "string"
#         }
#       },
#       "title": "EINField",
#       "type": "object"
#     },
#     "FloatField": {
#       "properties": {
#         "value": {
#           "anyOf": [
#             {
#               "type": "number"
#             },
#             {
#               "type": "null"
#             }
#           ],
#           "default": null,
#           "title": "Value"
#         },
#         "sequence": {
#           "anyOf": [
#             {
#               "type": "string"
#             },
#             {
#               "type": "null"
#             }
#           ],
#           "default": null,
#           "title": "Sequence"
#         },
#         "type": {
#           "default": "float",
#           "title": "Type",
#           "type": "string"
#         }
#       },
#       "title": "FloatField",
#       "type": "object"
#     },
#     "FormDetails": {
#       "properties": {
#         "form_type": {
#           "$ref": "#/$defs/TextField"
#         },
#         "calendar_year": {
#           "$ref": "#/$defs/YearField"
#         },
#         "document_id": {
#           "$ref": "#/$defs/TextField"
#         },
#         "page_number": {
#           "$ref": "#/$defs/TextField"
#         }
#       },
#       "title": "FormDetails",
#       "type": "object"
#     },
#     "PartType": {
#       "enum": [
#         "part 1",
#         "part 2",
#         "part 3"
#       ],
#       "title": "PartType",
#       "type": "string"
#     },
#     "PayerInformation": {
#       "properties": {
#         "payer_name": {
#           "$ref": "#/$defs/TextField"
#         },
#         "payer_street_address": {
#           "$ref": "#/$defs/TextField",
#           "description": "The payer's street address as listed on the form without city, state, zipcode."
#         },
#         "payer_state": {
#           "$ref": "#/$defs/TextField"
#         },
#         "payer_city": {
#           "$ref": "#/$defs/TextField"
#         },
#         "payer_zip_code": {
#           "$ref": "#/$defs/ZipCodeField"
#         },
#         "tin": {
#           "$ref": "#/$defs/EINField"
#         },
#         "rtn_optional": {
#           "anyOf": [
#             {
#               "$ref": "#/$defs/TextField"
#             },
#             {
#               "type": "null"
#             }
#           ]
#         }
#       },
#       "title": "PayerInformation",
#       "type": "object"
#     },
#     "Summary": {
#       "properties": {
#         "short_A_or_short_term_transaction_for_covered_tax_lots": {
#           "$ref": "#/$defs/SummaryRow"
#         },
#         "short_B_or_short_term_transaction_for_noncovered_tax_lots": {
#           "$ref": "#/$defs/SummaryRow"
#         },
#         "short_C": {
#           "$ref": "#/$defs/SummaryRow"
#         },
#         "total_short_term": {
#           "$ref": "#/$defs/SummaryRow"
#         },
#         "long_D_or_long_term_transaction_for_covered_tax_lots": {
#           "$ref": "#/$defs/SummaryRow"
#         },
#         "long_E_or_long_term_transaction_for_noncovered_tax_lots": {
#           "$ref": "#/$defs/SummaryRow"
#         },
#         "long_F": {
#           "$ref": "#/$defs/SummaryRow"
#         },
#         "total_long_term": {
#           "$ref": "#/$defs/SummaryRow"
#         },
#         "undetermined_B_or_E": {
#           "$ref": "#/$defs/SummaryRow"
#         },
#         "undetermined_C_or_F_undetermined_transaction_for_noncovered_tax_lots": {
#           "$ref": "#/$defs/SummaryRow"
#         },
#         "total_undetermined_term": {
#           "$ref": "#/$defs/SummaryRow"
#         },
#         "grand_total": {
#           "$ref": "#/$defs/SummaryRow"
#         }
#       },
#       "title": "Summary",
#       "type": "object"
#     },
#     "SummaryRow": {
#       "properties": {
#         "proceeds": {
#           "$ref": "#/$defs/FloatField",
#           "description": "Total Proceeds"
#         },
#         "cost_basis": {
#           "$ref": "#/$defs/CurrencyField",
#           "description": "Total Cost Basis"
#         },
#         "market_discount": {
#           "$ref": "#/$defs/CurrencyField",
#           "description": "Total Market Discount"
#         },
#         "wash_sale_loss_disallowed": {
#           "$ref": "#/$defs/FloatField",
#           "description": "Total Wash Sale Loss Disallowed. It can either be positive or negative so capture the -ve sign if present"
#         },
#         "net_gain_or_loss": {
#           "$ref": "#/$defs/FloatField",
#           "description": "Total Net Gain or Loss. It can either be positive or negative so capture the -ve sign if present"
#         }
#       },
#       "title": "SummaryRow",
#       "type": "object"
#     },
#     "TaxSlotType": {
#       "enum": [
#         "covered tax slots",
#         "non-covered tax slots"
#       ],
#       "title": "TaxSlotType",
#       "type": "string"
#     },
#     "TermType": {
#       "enum": [
#         "short term",
#         "long term",
#         "undetermined term"
#       ],
#       "title": "TermType",
#       "type": "string"
#     },
#     "TextField": {
#       "properties": {
#         "value": {
#           "anyOf": [
#             {
#               "type": "string"
#             },
#             {
#               "type": "null"
#             }
#           ],
#           "title": "Value"
#         },
#         "sequence": {
#           "anyOf": [
#             {
#               "type": "string"
#             },
#             {
#               "type": "null"
#             }
#           ],
#           "default": null,
#           "title": "Sequence"
#         },
#         "type": {
#           "default": "text",
#           "title": "Type",
#           "type": "string"
#         }
#       },
#       "required": [
#         "value"
#       ],
#       "title": "TextField",
#       "type": "object"
#     },
#     "TotalRow": {
#       "properties": {
#         "total_proceeds_reported_gross_or_net": {
#           "$ref": "#/$defs/FloatField",
#           "description": "Total Proceeds (Box 1d)"
#         },
#         "total_cost_or_other_basis": {
#           "$ref": "#/$defs/CurrencyField",
#           "description": "Total Cost or Other Basis (Box 1e)"
#         },
#         "total_accrued_market_discount": {
#           "$ref": "#/$defs/CurrencyField",
#           "description": "Total Accrued Market Discount (W) (Box 1g)"
#         },
#         "total_wash_sale_loss_disallowed": {
#           "$ref": "#/$defs/CurrencyField",
#           "description": "Total Wash Sale Loss Disallowed (D) (Box 1g)"
#         },
#         "total_gain_or_loss": {
#           "$ref": "#/$defs/FloatField",
#           "description": "Total Gain or Loss, along with - sign in case of loss"
#         }
#       },
#       "title": "TotalRow",
#       "type": "object"
#     },
#     "Transaction": {
#       "properties": {
#         "type_of_term": {
#           "$ref": "#/$defs/TermType"
#         },
#         "type_of_tax_slots": {
#           "$ref": "#/$defs/TaxSlotType"
#         },
#         "type_of_part": {
#           "$ref": "#/$defs/PartType"
#         },
#         "type_of_box": {
#           "$ref": "#/$defs/BoxType"
#         },
#         "description_of_property": {
#           "$ref": "#/$defs/TextField"
#         },
#         "cusip_number": {
#           "$ref": "#/$defs/TextField"
#         },
#         "symbol": {
#           "$ref": "#/$defs/TextField"
#         },
#         "transactions": {
#           "items": {
#             "$ref": "#/$defs/TransactionDetail"
#           },
#           "title": "Transactions",
#           "type": "array"
#         },
#         "totals": {
#           "$ref": "#/$defs/TotalRow"
#         }
#       },
#       "required": [
#         "type_of_term",
#         "type_of_tax_slots",
#         "type_of_part",
#         "type_of_box"
#       ],
#       "title": "Transaction",
#       "type": "object"
#     },
#     "TransactionDetail": {
#       "properties": {
#         "date_sold_or_disposed": {
#           "$ref": "#/$defs/DateField",
#           "description": "Date Sold or Disposed (Box 1c)"
#         },
#         "quantity": {
#           "$ref": "#/$defs/FloatField",
#           "description": "Quantity of securities sold"
#         },
#         "proceeds": {
#           "$ref": "#/$defs/FloatField",
#           "description": "Proceeds (Box 1d)"
#         },
#         "date_acquired": {
#           "$ref": "#/$defs/DateField",
#           "description": "Date Acquired (Box 1b)"
#         },
#         "cost_or_other_basis": {
#           "$ref": "#/$defs/CurrencyField",
#           "description": "Cost or Other Basis (Box 1e)"
#         },
#         "accrued_market_discount": {
#           "$ref": "#/$defs/CurrencyField",
#           "description": "Accrued Market Discount (W) (Box 1g)"
#         },
#         "wash_sale_loss_disallowed": {
#           "$ref": "#/$defs/CurrencyField",
#           "description": "Wash Sale Loss Disallowed (D) (Box 1g)"
#         },
#         "gain_or_loss": {
#           "$ref": "#/$defs/FloatField",
#           "description": "Gain or Loss, along with - sign in case of loss"
#         },
#         "additional_notes": {
#           "$ref": "#/$defs/TextField",
#           "description": "Additional Notes"
#         }
#       },
#       "title": "TransactionDetail",
#       "type": "object"
#     },
#     "YearField": {
#       "properties": {
#         "value": {
#           "anyOf": [
#             {
#               "type": "integer"
#             },
#             {
#               "type": "null"
#             }
#           ],
#           "default": null,
#           "title": "Value"
#         },
#         "sequence": {
#           "anyOf": [
#             {
#               "type": "string"
#             },
#             {
#               "type": "null"
#             }
#           ],
#           "default": null,
#           "title": "Sequence"
#         },
#         "type": {
#           "default": "year",
#           "title": "Type",
#           "type": "string"
#         }
#       },
#       "title": "YearField",
#       "type": "object"
#     },
#     "ZipCodeField": {
#       "properties": {
#         "value": {
#           "anyOf": [
#             {
#               "type": "string"
#             },
#             {
#               "type": "null"
#             }
#           ],
#           "default": null,
#           "title": "Value"
#         },
#         "sequence": {
#           "anyOf": [
#             {
#               "type": "string"
#             },
#             {
#               "type": "null"
#             }
#           ],
#           "default": null,
#           "title": "Sequence"
#         },
#         "type": {
#           "default": "zipcode",
#           "title": "Type",
#           "type": "string"
#         }
#       },
#       "title": "ZipCodeField",
#       "type": "object"
#     }
#   },
#   "properties": {
#     "form_details": {
#       "$ref": "#/$defs/FormDetails"
#     },
#     "payer_information": {
#       "$ref": "#/$defs/PayerInformation"
#     },
#     "transaction_details": {
#       "items": {
#         "$ref": "#/$defs/Transaction"
#       },
#       "title": "Transaction Details",
#       "type": "array"
#     },
#     "summary": {
#       "$ref": "#/$defs/Summary"
#     }
#   },
#   "title": "Form1099B",
#   "type": "object"
# }

#                         Rules:
#                         - The corresponding data is used for tax filing so do accurate OCR.
#                         - If a checkbox is marked as ticked or crossed, it means the checkbox is marked, and its value will be marked as True. If a checkbox is empty, its value will be False.
#                         - Focus exclusively on table data and ensure no data from outside the table is considered unless explicitly labeled for extraction.
#                         - Match the field names on the form (as they appear on the page) directly to the keys provided in the template. Extract only the data for these specific fields.
#                         - **Do not map any data to a field unless its name on the form matches the key in the provided template.** 
#                         - **The template schema may include field descriptions; ensure to follow the instructions provided in those descriptions when capturing the corresponding field.**
#                         - Ensure extracted values are mapped to the correct field without overlap or misplacement.
#                         - Convert amounts to floats (remove currency symbols and commas).
#                         - Use empty strings for missing text fields and 0.0 for missing numeric fields.
#                         - Format addresses properly.
#                         - Format TIN/EIN/SSN correctly (XX-XXXXXXX for EIN, XXX-XX-XXXX for SSN).
#                         - Use MM-DD-YYYY for dates.
#                         - Extract the sequence number or index of fields as in the form template for each field wherever applicable.
#                         - If a page contains same Form 1099-B form type multiple times then only consider one of the form which has maximum data.
#                         - **Critical Rule**: **Do not mistake sequence numbers or index fields as the field value. Sequence numbers are not field values and must be excluded when determining the actual field content.**
#                         - If a numeric sequence number or index appears near a field, verify that the number aligns with the context of a sequence label and not the field's value. For example:
#                         - For fields like "amounts_allocated_or_apportioned_to_sc", a number like "1" that is clearly a sequence identifier must not be included in the "value" field. 
#                         - This means:
#                             - Extract only explicit monetary amounts (e.g., 1234.56).
#                             - Exclude numbers that are part of form numbering conventions.
#                         - Make sure all the data extracted from the image is present in the template.
#                         - Do not make any additions to the extracted data such as '
# ' in the address field.
#                         - Do not take sequence numbers as currency.
#                         - Add the document_id and page_number in the FormDetails."Document id" and "page number" are provided in the prompt
#                         - This form data may be spread across various pages, and you have been given a single page to OCR. If some sections of the form are not present in the current page, use the default values for those sections' fields.
#                         Default Values:
#                             "text": "",
#                             "currency": 0.00
#                             "checkbox": False,
#                             "year": None,
#                             "date": None,
#                             "float": 0.0,
#                             "percentage": 0.0,
#                             "list":[]
#                         Ensure to apply these rules every time while processing.
#                         Return the extracted data as a JSON object matching the template structure.
#                 """
#     )

#     # Check the result
#     if result["success"]:
#         print("Response:", result["response"])
#     else:
#         print("Error:", result["error"])
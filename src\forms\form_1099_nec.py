from pydantic import BaseModel, Field
from src.forms.base_form import EINField, TextField, YearField, CurrencyField, CheckboxField, FormDetails, DateField, ZipCodeField


class PayerInformation(BaseModel):
    payer_name: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_street_address: TextField = Field(
        default_factory=lambda: TextField(type="text"), description="The payer's street address as listed on the form without city, state, zipcode.")
    payer_city: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_state: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_zip_code: ZipCodeField = Field(
        default_factory=lambda: ZipCodeField(type="zipcode"))
    # payer_telephone_number: ContactField = Field(
    #     default_factory=lambda: ContactField(type="contact"))
    payer_tin: EINField = Field(
        default_factory=lambda: EINField(type="einfield"))


class FinancialFields(BaseModel):
    non_employee_compensation: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="1", type="currency"))
    payers_made_direct_sales_of_5000_or_more_of_consumer_products_to_a_recipient_for_resale: CheckboxField = Field(
        default_factory=lambda: CheckboxField(sequence="2", type="checkbox"))
    federal_income_tax_withheld: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="4", type="currency"))
    state_tax_withheld: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="5", type="currency"))
    state: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payers_state_number: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    state_income: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="7", type="currency"))


class AdditionalFields(BaseModel):
    corrected: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    omb_number: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    form_revision_date: DateField = Field(
        default_factory=lambda: DateField(type="date"))
    fatca_filing_requirement: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))


class Form1099NEC(BaseModel):
    payer_information: PayerInformation = Field(
        default_factory=PayerInformation)
    form_details: FormDetails = Field(default_factory=FormDetails)
    financial_fields: FinancialFields = Field(default_factory=FinancialFields)
    additional_fields: AdditionalFields = Field(
        default_factory=AdditionalFields)

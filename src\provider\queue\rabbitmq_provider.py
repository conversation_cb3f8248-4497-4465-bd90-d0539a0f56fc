from src.constants.env_constant import env_var
from typing import Dict
import pika


class RabbitMQProvider:

    def __init__(self):
        self.connection = None
        self.channel = None

    def connect(self):
        # Establish connection and channel
        credentials = pika.PlainCredentials(env_var["RABBITMQ_USERNAME"], env_var["RABBITMQ_PASSWORD"])
        self.connection = pika.BlockingConnection(
            pika.ConnectionParameters(
                host=env_var["RABBITMQ_HOST"],
                port=env_var["RABBITMQ_PORT"],
                credentials=credentials
            )
        )
        self.channel = self.connection.channel()

    def publish(self, queue: str, message: str) -> Dict:

        # Declare the queue
        self.channel.queue_declare(queue=queue)

        # Publish the message to the queue
        self.channel.basic_publish(
            exchange="",  # Empty string for default exchange
            routing_key=queue,
            body=message,
            properties=pika.BasicProperties(content_type="text/plain")
        )

        return {"success": True, "message": "Message published successfully.", "data": None}

    def disconnect(self) -> None:
        # Close the connection (can be done outside the publish method for better resource management)
        if self.connection and not self.connection.is_closed:
            self.connection.close()

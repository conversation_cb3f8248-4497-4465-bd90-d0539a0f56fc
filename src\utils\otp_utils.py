from src.constants.env_constant import env_var
from datetime import datetime, timedelta
import secrets

OTP_EXPIRATION_TIME_IN_MINUTES = int(env_var["OTP_EXPIRATION_TIME_IN_MINUTES"])  # OTP expiration time in minutes

def generate_otp(digits: int = 6) -> str:
    """
    Generates a secure OTP with the specified number of digits.
    """
    lower_bound = 10**(digits - 1)
    upper_bound = (10**digits) - 1
    return str(secrets.randbelow(upper_bound - lower_bound + 1) + lower_bound)

def get_otp_expiry(minutes: int = OTP_EXPIRATION_TIME_IN_MINUTES) -> datetime:
    """
    Returns a datetime object representing the OTP expiry time.
    """
    return datetime.utcnow() + timedelta(minutes=minutes)
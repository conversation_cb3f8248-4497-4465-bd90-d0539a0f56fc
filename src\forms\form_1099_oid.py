from pydantic import BaseModel, Field
from typing import List, Optional
from src.forms.base_form import TextField, YearField, CurrencyField, CheckboxField, FormDetails, ZipCodeField, EINField


class PayerInformation(BaseModel):
    payer_name: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_street_address: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_city: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_state: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_zip_code: ZipCodeField = Field(
        default_factory=lambda: ZipCodeField(type="zipcode"))
    payer_tin: EINField = Field(default_factory=lambda: EINField(type="einfield"))


class FinancialFields(BaseModel):
    original_issue_discount_for_the_year: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    other_periodic_interest: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    early_withdrawal_penalty: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    federal_income_tax_withheld: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    market_discount: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    acquisition_premium: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    description: TextField = Field(
        default_factory= lambda:TextField(type="text")
    )
    original_issue_discount_on_us_treasury_obligations: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    investment_expenses: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    bond_premium: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    tax_exempt_oid: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    
    
class AdditionalFields(BaseModel):
    corrected: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    void: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    fatca_filling_requirement: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    omb_number: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    calendar_year: YearField = Field(
        default_factory=lambda: YearField(type="year"))

class StateInformation(BaseModel):
    state: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    state_identification_number: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    state_tax_withheld: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))

# Main Pydantic model for Form 1099-PATR
class Form1099OID(BaseModel):
    payer_information: PayerInformation = Field(
        default_factory=PayerInformation)
    form_details: FormDetails = Field(default_factory=FormDetails)
    financial_fields: FinancialFields = Field(default_factory=FinancialFields)
    state_information: List[StateInformation] = Field(default_factory=list)
    additional_fields: AdditionalFields = Field(
        default_factory=AdditionalFields)

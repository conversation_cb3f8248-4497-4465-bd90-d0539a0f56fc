from pydantic import BaseModel, Field
from typing import List, Optional
from src.forms.base_form import TextField, CurrencyField, CheckboxField, DateField, FormDetails, YearField, ZipCodeField, EINField

# Payer Information Section


class PayerInformation(BaseModel):
    payer_name: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_address: TextField = Field(
        default_factory=lambda: TextField(type="text"), description="The payer's street address as listed on the form without city, state, zipcode.")
    payer_city: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_state: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_zip_code: ZipCodeField = Field(
        default_factory=lambda: ZipCodeField(type="zipcode"))
    payer_tin_or_federal_identification_number_or_ides_fein: EINField = Field(
        default_factory=lambda: EINField(type="einfield"))


# Financial Information Section
class FinancialInformation(BaseModel):
    unemployment_compensation: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    state_or_local_income_tax_refunds_credits_offsets: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    box_2_amount_is_for_tax_year: YearField = Field(
        default_factory=lambda: YearField(type="year"))
    federal_income_tax_withheld: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    rtaa_payments: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    taxable_grants: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    agriculture_payments: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    market_gain: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    state_income_tax_withheld: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))

# Computation of State Tax Refund Section


class StateTaxRefundComputation(BaseModel):
    refund_is_for_tax_year: YearField = Field(
        default_factory=lambda: YearField(type="year"))
    refund_requested: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    amount_paid: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    interest: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    state_income_tax_refund: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    

class Type_Of_Payment_And_Reportable_Income(BaseModel):
    type_of_payment: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    reportable_income: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))


# Additional Information Section
class AdditionalInformation(BaseModel):
    trade_or_business_income: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    amount_for_tax_year: YearField = Field(
        default_factory=lambda: YearField(type="year"))
    state: TextField = Field(default_factory=lambda: TextField(type="text"))
    state_identification_number: TextField = Field(
        default_factory=lambda: TextField(type="text"))

    type_of_payment_and_reportable_income: List[Type_Of_Payment_And_Reportable_Income] = Field(
        default_factory=list)  # List of payment types and reportable income
    total_agriculture_payments: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    market_gain: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    corrected: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))


# Main Pydantic model for Form 1099-G
class Form1099G(BaseModel):
    payer_information: PayerInformation = Field(
        default_factory=PayerInformation)
    form_details: FormDetails = Field(default_factory=FormDetails)
    financial_information: FinancialInformation = Field(
        default_factory=FinancialInformation)
    state_tax_refund_computation: StateTaxRefundComputation = Field(
        default_factory=StateTaxRefundComputation)
    additional_information: AdditionalInformation = Field(
        default_factory=AdditionalInformation)

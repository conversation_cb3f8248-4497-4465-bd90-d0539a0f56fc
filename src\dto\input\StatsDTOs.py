from pydantic import BaseModel, Field
from typing import Optional

class ClientStatsRequest(BaseModel):
    from_date: Optional[str] = Field(None, description="Filter stats from this date")
    to_date: Optional[str] = Field(None, description="Filter stats up to this date")
    financial_year: Optional[int] = Field(None, description="Filter by financial year")
    queue_status: Optional[str] = Field(None, description="Filter by client queue status")

class FileStatsRequest(BaseModel):
    client_id: str = Field(None, description="Filter file stats by client ID")

class PageStatsRequest(BaseModel):
    file_id: str = Field(None, description="Filter page stats by file ID")

class FormStatsRequest(BaseModel):
    client_id: str = Field(None, description="Filter form stats by client ID")
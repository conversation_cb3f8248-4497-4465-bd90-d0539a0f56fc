from pydantic import BaseModel, Field
from typing import Dict, Optional, List

class PageCreateRequest(BaseModel):
    page_index: int = Field(..., description="Index of the page")
    form_type: List[str] = Field(..., description="List of form types")
    t_form_type: Optional[List[str]] = Field(None, description="Optional list of t-form types")
    l_form_type: Optional[List[str]] = Field(None, description="Optional list of l-form types")
    message: str = Field(..., description="Message for the page")
    file_id: str = Field(..., description="ID of the associated file")

class PageUpdateIsFilledOrErrorRequest(BaseModel):
    page_ids: List[str] = Field(..., description="IsD of the pages to update is filled")
    form_id: str = Field(..., description="Form Id for which we have to update is_filled")
    is_filled: bool = Field(..., description="Flag to indicate if form has been filled")
    error_message: Optional[str] = Field(None, description="Error message if any")

class PageUpdateRequest(BaseModel):
    page_id: Optional[str] = Field(None, description="ID of the page to update")
    update_form_status: Optional[PageUpdateIsFilledOrErrorRequest] = Field(None, description="Updated form status")
    page_index: Optional[int] = Field(None, description="Updated page index")
    form_type: Optional[List[str]] = Field(None, description="Updated list of form types")
    t_form_type: Optional[List[str]] = Field(None, description="Updated list of t-form types")
    l_form_type: Optional[List[str]] = Field(None, description="Updated list of l-form types")
    message: Optional[str] = Field(None, description="Updated message")
    status: Optional[str] = Field(None, description="Updated status")

class PageFallbackRequest(BaseModel):
    file_id: str = Field(..., description="ID of the associated file")
    page_ids: List[str] = Field(..., description="List of page IDs to fallback")
    new_form_types: Dict[str, List[str]] = Field(..., description="Mapping of page numbers to form types")  # Maps page numbers to a list of form types
    is_review_required: bool = Field(..., description="Flag to indicate if the client ocr should be reviewed and filled")


class PageFilterRequest(BaseModel):
    file_id: Optional[str] = Field(None, description="Filter pages by file ID")
    status: Optional[str] = Field(None, description="Filter pages by status")
    page_index: Optional[int] = Field(None, description="Filter by page index")

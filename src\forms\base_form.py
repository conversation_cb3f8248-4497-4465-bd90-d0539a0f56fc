from pydantic import BaseModel, <PERSON>, condecimal
from typing import Optional,List, Any


# Define the base classes with metadata annotations
class TextField(BaseModel):
    value: Optional[str]
    sequence: Optional[str] = None
    type: str = "text"


class CurrencyField(BaseModel):
    value: Optional[str] = None
    sequence: Optional[str] = None
    type: str = "currency"

class ContactField(BaseModel):
    value: Optional[str] = None
    sequence: Optional[str] = None
    type: str = "contact"

class ZipCodeField(BaseModel):
    value: Optional[str] = None
    sequence: Optional[str] = None
    type: str = "zipcode"

class EINField(BaseModel):
    value: Optional[str] = None
    sequence: Optional[str] = None
    type: str = "einfield"

class CheckboxField(BaseModel):
    value: Optional[bool] = None
    sequence: Optional[str] = None
    type: str = "checkbox"

class BooleanField(BaseModel):
    value: Optional[bool] = None
    sequence: Optional[str] = None
    type: str = "boolean"

class FloatField(BaseModel):
    value: Optional[float] = None
    sequence: Optional[str] = None
    type: str = "float"

class YearField(BaseModel):
    value: Optional[int] = None
    sequence: Optional[str] = None
    type: str = "year"


class DateField(BaseModel):
    value: Optional[str] = None
    sequence: Optional[str] = None
    type: str = "date"

class PercentageField(BaseModel):
    value: Optional[float] = None
    sequence: Optional[str] = None
    type: str = "percentage"

class ListField(BaseModel):
    value: Optional[List[Any]] = None
    sequence: Optional[str] = None
    type: str = "list"
class FormDetails(BaseModel):
    form_type: TextField = Field(default_factory=lambda: TextField(type="text"))
    calendar_year: YearField = Field(default_factory=lambda: YearField(type="year"))
    document_id: TextField = Field(default_factory=lambda: TextField(type="text"))
    page_number: TextField = Field(default_factory=lambda: TextField(type="text"))
    page_id: ListField = Field(default_factory=lambda: ListField(type="list"))
    form_id: TextField = Field(default_factory=lambda: TextField(type="text"))
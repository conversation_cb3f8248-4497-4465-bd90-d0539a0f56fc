from src.constants.enum import FormType
from typing import Callable, Dict, List, Optional
from src.constants.constants import BASE_PROMPT
from src.utils.form_utility import (
    check_inner_masking_trigger,
    mask_employee_name_and_address,
    mask_recipient_tin_1099_DIV,
    mask_phrases,
    mask_ssn_k1,
    cover_phrases_with_box_K1
)

# Form Configuration Structure
class FormTypeConfig:
    def __init__(
        self,
        sensitive_fields: List[str],
        important_fields: List[str] = None,
        avoid_masking_fields: List[List[str]] = None,
        ignore_page_numbers: List[int] = None,
        ignore_cell_indices: List[int] = None,
        inner_masking_trigger: Dict[str, Callable[[str], bool]] = None,
        prompt: Optional[str] = None,
        draw_border: bool = False,
        post_processing: List[Callable] = None,
        post_processing_func_config: List[List[Dict]] = None,
        draw_border_config: Dict[str, any] = None,
        check_this_field_for_multiple_data_in_same_form: str = None,
        do_not_check_this_field_for_multiple_data_in_same_form_if_there_is_info_in_this_field: str = None
    ):
        self.sensitive_fields = sensitive_fields
        self.important_fields = important_fields or []
        self.avoid_masking_fields = avoid_masking_fields or []
        self.ignore_page_numbers = ignore_page_numbers or []
        self.ignore_cell_indices = ignore_cell_indices or []
        self.inner_masking_trigger = inner_masking_trigger or {}
        self.prompt = prompt or ""
        self.draw_border = draw_border
        self.post_processing = post_processing or []
        self.post_processing_func_config = post_processing_func_config or []
        self.check_this_field_for_multiple_data_in_same_form = check_this_field_for_multiple_data_in_same_form or None
        self.do_not_check_this_field_for_multiple_data_in_same_form_if_there_is_info_in_this_field = do_not_check_this_field_for_multiple_data_in_same_form_if_there_is_info_in_this_field or None

        # Default draw_border_config if not provided
        self.draw_border_config = draw_border_config or {
            "min_area": 10000,
            "aspect_ratio_range": (2.0, 4.0),
            "border_thickness": 1,
            "kernel_size": (3, 3),
            "iterations": 2,
        }


# Form Configuration Mapping



FORM_CONFIG: Dict[FormType, FormTypeConfig] = {
    FormType.FORM_1098_E.value: FormTypeConfig(
        sensitive_fields=["Borrower’s", "Borrower",
                          "street", "city"],
        avoid_masking_fields=[["Recipient"], [
            "Recipient"], ["Recipient"], ["Recipient"]],
        prompt=BASE_PROMPT),
    FormType.FORM_1099_DIV.value: FormTypeConfig(
        sensitive_fields=["Recipient’s", "Recipient", "Address",
                           "Holder", "City", "Name of person"],
        avoid_masking_fields=[[], ["Exempt"], [
            "Payer's", "Payer"], [], ["Payer's", "Payer"], []],
        draw_border=True,
        prompt=BASE_PROMPT,
        post_processing=[mask_recipient_tin_1099_DIV, mask_ssn_k1],
        post_processing_func_config=[[], []],
        draw_border_config={
            "min_area": 10000,
            "aspect_ratio_range": (1.0, 7.0),
            "border_thickness": 2,
            "kernel_size": (5, 3),
            "iterations": 5,
        },
    ),
    FormType.FORM_1099_INT.value: FormTypeConfig(
        sensitive_fields=["Recipient’s", "Recipient"],
        avoid_masking_fields=[[], ["Box"]],
        prompt=BASE_PROMPT,
        check_this_field_for_multiple_data_in_same_form="financial_fields",
        do_not_check_this_field_for_multiple_data_in_same_form_if_there_is_info_in_this_field="totals.interest_income",
    ),
    FormType.FORM_1099_MISC.value: FormTypeConfig(
        sensitive_fields=["Recipient", "Recipient's", "name",
                          "street",
                          "city",
                          "country"],
        avoid_masking_fields=[["Payer"], ["Payer"], [
            "PAYER"], ["PAYER"], ["PAYER"], ["PAYER"]],
        prompt=BASE_PROMPT
    ),
    FormType.FORM_1098_T.value: FormTypeConfig(
        sensitive_fields=[
            "Students",
            "Student's",
            "Student's TIN",
            "Student's first name and initial",
            "Student's address and ZIP code",
            "Street",
            "City",
            "State",
            "Country"
        ],
        avoid_masking_fields=[[], [], [], [], [], ["filer", "filer's"], [
            "filer", "filer's"], ["filer", "filer's"], ["filer", "filer's"]],
        prompt=BASE_PROMPT
    ),
    FormType.FORM_1098.value: FormTypeConfig(
        sensitive_fields=[
            "Payer's",
            "Borrower’s", 
            "Payer",
            "Borrower",
            "street", 
            "city", 
            "state", 
            "country"],
        avoid_masking_fields=[
            ["Recipient", "checked", "mortgage", "lender", "mongage", "interest", "received"], 
            ["Recipient", "checked", "mortgage", "lender", "mongage", "interest", "received"],
            ["Recipient", "checked", "mortgage", "lender", "mongage", "interest", "received"], 
            ["Recipient", "checked", "mortgage", "lender", "mongage", "interest", "received"], 
            ["Recipient", "lender", "mongage", "mortgage"], 
            ["Recipient", "lender", "mongage", "mortgage"], 
            ["Recipient", "lender", "mongage", "mortgage"], 
            ["Recipient", "lender", "mongage", "mortgage"]],
        draw_border=True,
        prompt=BASE_PROMPT
    ),
    FormType.FORM_1099_G.value: FormTypeConfig(
        sensitive_fields=[
            "Recipient",
            "Recipient's",
            "Recipient's TIN",
            "Recipient's first name and initial",
            "Recipient's address and ZIP code",
        ],
        avoid_masking_fields=[["Tax Account", "copy"],
                              ["Tax Account", "copy"], [], [], []],
        prompt=BASE_PROMPT,
        draw_border=True
    ),
    FormType.FORM_1099_R.value: FormTypeConfig(
        sensitive_fields=["Recipient’s", "Recipient"],
        avoid_masking_fields=[["Payer's"], ["Payer"]],

        prompt=BASE_PROMPT
    ),
    FormType.FORM_1099_B.value: FormTypeConfig(
        sensitive_fields=[
            "Recipient",
            "Recipient's",
            "Taxpayer ID Number",
            "Recipient's first name and initial",
            "Recipient's address and ZIP code"
        ],
        avoid_masking_fields=[["Copy"], ["Copy"], [], [], []],
        prompt=BASE_PROMPT,
        post_processing=[mask_phrases],
        post_processing_func_config=[[
            {
                "phrase": "Account No:",
                "mask_height_factor": 5,
                "mask_width_extension": 810,
                "top_padding": -40,
                "left_padding": 0,
            },
            {
                "phrase": "Account Number:",
                "mask_height_factor": 20,
                "mask_width_extension": 310,
                "top_padding": -200,
                "left_padding": 0,
            },
        ]]
    ),
    FormType.FORM_CRYPTO_TAX.value: FormTypeConfig(
        sensitive_fields=[
            "Recipient",
            "Recipient's",
            "Taxpayer ID Number",
            "Recipient's first name and initial",
            "Recipient's address and ZIP code"
        ],
        avoid_masking_fields=[["Copy"], ["Copy"], [], [], []],
        prompt=BASE_PROMPT,
        post_processing=[mask_phrases],
        post_processing_func_config=[[
            {
                "phrase": "Account No:",
                "mask_height_factor": 5,
                "mask_width_extension": 810,
                "top_padding": -40,
                "left_padding": 0,
            },
            {
                "phrase": "Account Number:",
                "mask_height_factor": 20,
                "mask_width_extension": 310,
                "top_padding": -200,
                "left_padding": 0,
            },
        ]]
    ),
    FormType.FORM_CRYPTO_TAX_COINBASE.value: FormTypeConfig(
        sensitive_fields=[
            "Recipient",
            "Recipient's",
            "Taxpayer ID Number",
            "Recipient's first name and initial",
            "Recipient's address and ZIP code"
        ],
        avoid_masking_fields=[["Copy"], ["Copy"], [], [], []],
        prompt=BASE_PROMPT,
        post_processing=[mask_phrases],
        post_processing_func_config=[[
            {
                "phrase": "Account No:",
                "mask_height_factor": 5,
                "mask_width_extension": 810,
                "top_padding": -40,
                "left_padding": 0,
            },
            {
                "phrase": "Account Number:",
                "mask_height_factor": 20,
                "mask_width_extension": 310,
                "top_padding": -200,
                "left_padding": 0,
            },
        ]]
    ),
    FormType.FORM_CRYPTO_TAX_SIMPLE.value: FormTypeConfig(
        sensitive_fields=[
            "Recipient",
            "Recipient's",
            "Taxpayer ID Number",
            "Recipient's first name and initial",
            "Recipient's address and ZIP code"
        ],
        avoid_masking_fields=[["Copy"], ["Copy"], [], [], []],
        prompt=BASE_PROMPT,
        post_processing=[mask_phrases],
        post_processing_func_config=[[
            {
                "phrase": "Account No:",
                "mask_height_factor": 5,
                "mask_width_extension": 810,
                "top_padding": -40,
                "left_padding": 0,
            },
            {
                "phrase": "Account Number:",
                "mask_height_factor": 20,
                "mask_width_extension": 310,
                "top_padding": -200,
                "left_padding": 0,
            },
        ]]
    ),
    FormType.FORM_1099_NEC.value: FormTypeConfig(
        sensitive_fields=[
            "Recipient",
            "Recipient's",
            "Recipient's TIN",
            "Recipient's first name and initial",
            "Recipient's address and ZIP code"
        ],
        avoid_masking_fields=[["payer", "1099-NEC", "Nonemployee"], ["payer",
                                                                     "1099-NEC", "Nonemployee"], ["payer"], ["payer"], ["payer"]],
        prompt=BASE_PROMPT
    ),
    FormType.FORM_1095_A.value: FormTypeConfig(
        sensitive_fields=[
            "Recipient",
            "Recipient's",
            "Recipient's SSN",
            "Street",
            "City",
            "State",
            "Country"
        ],
        avoid_masking_fields=[[], [], [], [], [], ["Do", "Not"], []],
        prompt=BASE_PROMPT,
        draw_border=True,
        draw_border_config={
            "min_area": 10000,
            "aspect_ratio_range": (2.0, 6.0),
            "border_thickness": 8,
            "kernel_size": (3, 3),
            "iterations": 3,
        }
    ),
    FormType.FORM_1099_SSA.value: FormTypeConfig(
        sensitive_fields=[
            "Box 1.",
            "Box 2",
            "Address",
            "Beneficiary Name",
            "Beneficiary's Social Security Number",
            "Claim Number"
        ],
        avoid_masking_fields=[[], [], [], [], [], []],
        prompt=BASE_PROMPT
    ),
    FormType.FORM_W_2.value: FormTypeConfig(
        sensitive_fields=[
            "Employee",
            "Employee's",
            "Employee's first name and initial",
            "Last name",
            "Employee's address and ZIP code",
        ],
        avoid_masking_fields=[['Copy'], ['Copy'], [], [], []],
        prompt=BASE_PROMPT,
        draw_border=True,
        post_processing=[mask_employee_name_and_address,],
        post_processing_func_config=[[]],
    ),
    FormType.FORM_W_2G.value: FormTypeConfig(
        sensitive_fields=[
            "Telephone",
            "WINNER",
            "WINNER'S",
            "name",
            "street",
            "city",
            "country",
        ],
        avoid_masking_fields=[["FEDERAL"], [], [], ["PAYER", "LOCAL", "OCAL"], ["PAYER"], ["PAYER"], ["PAYER"]],
        inner_masking_trigger={
            "telephone": check_inner_masking_trigger,
            "Telephone": check_inner_masking_trigger,
        },
        prompt=BASE_PROMPT
    ),
    FormType.FORM_K_1_1041.value: FormTypeConfig(
        sensitive_fields=["Beneficiary's", "Fiduciary", "estate's or trust's",
                          "ordinary dividends",
                          "qualified dividends",
                          "net short-term capital gain",
                          "other portfolio and",
                          "ordinary business income",
                          "net rental real estate income"],
        avoid_masking_fields=[["1041","K-1"], [], [], [], [], [], [], [], []],
        inner_masking_trigger={
        },
        prompt=("You are an OCR assistant. Extract the table values from the image "
                "and provide them as key-value pairs in valid JSON. Do not include any "
                "sequence numbers or additional text in your response."),
        draw_border=True,
        draw_border_config={
            "min_area": 10000,
            "aspect_ratio_range": (4.0, 15.0),
            "border_thickness": 1,
            "kernel_size": (3, 3),
            "iterations": 5,
        },
        post_processing=[mask_ssn_k1],
        post_processing_func_config=[[]],

    ),
    FormType.FORM_K_1_1065.value: FormTypeConfig(
        sensitive_fields=["Partner's", "partner", "Name", "Mailing address", "City"],
        avoid_masking_fields=[["Partnership", "nonbusiness", "CAPITAL", "Information", "Share"], ["qbi or qualified ptp","information","Partnership's", "contribute",], ["Partnership", "Partnership's"],
                              ["Partnership", "Partnership's"], ["Partnership", "Partnership's"]],
        inner_masking_trigger={
        },
        prompt=("You are an OCR assistant. Extract the table values from the image "
                "and provide them as key-value pairs in valid JSON. Do not include any "
                "sequence numbers or additional text in your response."),
        draw_border=True,
        draw_border_config={
            "min_area": 10000,
            "aspect_ratio_range": (4.0, 15.0),
            "border_thickness": 1,
            "kernel_size": (3, 3),
            "iterations": 5,
        },
        post_processing=[mask_ssn_k1, cover_phrases_with_box_K1, mask_phrases],
        post_processing_func_config=[[], ["Partner's name"], [{
                "phrase": "Partner name",
                "mask_height_factor": 9,
                "mask_width_extension": 610,
                "top_padding": 0,
                "left_padding": 0,
            },]],

    ),
    FormType.FORM_K_1_1120_S.value: FormTypeConfig(
        sensitive_fields=["Name", "Mailing address", "City",
                          "Shareholder's", "Shareholder's name", "Shareholder's identifying no"],
        avoid_masking_fields=[["Corporation's", "Corporation"], ["Corporation's", "Corporation"], ["Corporation's", "Corporation"],
                              ["Corporation's", "Corporation"], ["Corporation's", "Corporation"], ["Corporation's employer identification number"]],
        inner_masking_trigger={
        },
        prompt=("You are an OCR assistant. Extract the table values from the image "
                "and provide them as key-value pairs in valid JSON. Do not include any "
                "sequence numbers or additional text in your response."),
        draw_border=True,
        draw_border_config={
            "min_area": 10000,
            "aspect_ratio_range": (4.0, 15.0),
            "border_thickness": 1,
            "kernel_size": (3, 3),
            "iterations": 5,
        },
        post_processing=[mask_ssn_k1, cover_phrases_with_box_K1],
        post_processing_func_config=[[], ["Shareholder's name",
                        "Name(s) as shown on K1", "Name as shown on original return"]],

    ),
    # FormType.FORM_1099_COMPOSITE.value: FormTypeConfig(
    #     sensitive_fields=["recipient's", "account"],
    #     avoid_masking_fields=[[
    #         "paid",
    #         "show",
    #         "treasury",
    #         "loss",
    #         "was",
    #         "your",
    #         "you",
    #     ],
    #         [
    #         "payer",
    #         "with",
    #         "treasury",
    #         "loss",
    #         "was",
    #         "your",
    #         "you",
    #     ],
    #     ],
    #     prompt=BASE_PROMPT
    # ),
    # FormType.FORM_1099_CONSOLIDATED.value: FormTypeConfig(
    #     sensitive_fields=[],
    #     avoid_masking_fields=[],
    #     prompt=BASE_PROMPT,
    #     post_processing=[mask_phrases],
    #     post_processing_func_config=[[{
    #         "phrase": "Name Reported",
    #         "mask_height_factor": 10,
    #         "mask_width_extension": 500,
    #         "top_padding": -15,
    #         "left_padding": 0,
    #     },
    #         {
    #         "phrase": "Taxpayer ID",
    #         "mask_height_factor": 3,
    #         "mask_width_extension": 250,
    #         "top_padding": -16,
    #         "left_padding": 0,
    #     },
    #         {
    #         "phrase": "Account No:",
    #         "mask_height_factor": 10,
    #         "mask_width_extension": 400,
    #         "top_padding": -16,
    #         "left_padding": 0,
    #     },
    #         {
    #         "phrase": "Reporting Statement",
    #         "mask_height_factor": 2,
    #         "mask_width_extension": 50,
    #         "top_padding": 0,
    #         "left_padding": 0,
    #     },
    #         {
    #         "phrase": "Supplemental Information",
    #         "mask_height_factor": 2,
    #         "mask_width_extension": 80,
    #         "top_padding": 0,
    #         "left_padding": 0,
    #     }]]
    # ),
    FormType.FORM_1099_PATR.value: FormTypeConfig(
        sensitive_fields=["Recipient", "Recipient's", "name",
                          "street",
                          "city",
                          "country",
                          "Account Number"],
        avoid_masking_fields=[["Payer"], ["Payer"], [
            "PAYER"], ["PAYER"], ["PAYER"], ["PAYER"], ["PAYER"]],
        prompt=BASE_PROMPT
    ),
    FormType.FORM_1099_OID.value: FormTypeConfig(
        sensitive_fields=["Recipient", "Recipient's", "name",
                          "street",
                          "city",
                          "country",
                          "Account Number"],
        avoid_masking_fields=[["Payer"], ["Payer"], [
            "PAYER"], ["PAYER"], ["PAYER"], ["PAYER"], ["PAYER"]],
        prompt=BASE_PROMPT
    ),
    FormType.FORM_1099_LTC.value: FormTypeConfig(
        sensitive_fields=["Policyholder", "Policyholder's", "name",
                          "street",
                          "city",
                          "country",
                          "Account Number"],
        avoid_masking_fields=[["Payer"], ["Payer"], [
            "PAYER"], ["PAYER"], ["PAYER"], ["PAYER"], ["PAYER"]],
        prompt=BASE_PROMPT
    ),
    FormType.FORM_1099_S.value: FormTypeConfig(
        sensitive_fields=["TRANSFEROR", "TRANSFEROR's", "name",
                          "street",
                          "city",
                          "country",
                          "Account Number"],
        avoid_masking_fields=[["Filer"], ["Filer"], [
            "Filer"], ["Filer","legal"], ["Filer","legal"], ["Filer","legal"], ["Filer"]],
        prompt=BASE_PROMPT
    ),
    FormType.FORM_1099_SA.value: FormTypeConfig(
        sensitive_fields=["Recipient", "Recipient's", "name",
                          "street",
                          "city",
                          "country",
                          "Account Number"],
        avoid_masking_fields=[["Payer", "Trustee"], ["Payer", "Trustee"], [
            "Payer", "Trustee"], ["Payer", "Trustee"], ["Payer", "Trustee"], ["Payer", "Trustee"], ["Payer", "Trustee"]],
        prompt=BASE_PROMPT
    ),
    # FormType.FORM_5498.value: FormTypeConfig(
    #     sensitive_fields=["Participant", "Participant's", "name",
    #                       "street",
    #                       "city",
    #                       "country",
    #                       "Account Number"],
    #     avoid_masking_fields=[["TRUSTEE", "ISSUER"], ["TRUSTEE", "ISSUER"], ["TRUSTEE", "ISSUER"], 
    #                           ["TRUSTEE", "ISSUER"], ["TRUSTEE", "ISSUER"], ["TRUSTEE", "ISSUER"], ["TRUSTEE", "ISSUER"]],
    #     prompt=BASE_PROMPT
    # ),
    FormType.FORM_5498_SA.value: FormTypeConfig(
        sensitive_fields=["Participant", "Participant's", "name",
                          "street",
                          "city",
                          "country",
                          "Account Number"],
        avoid_masking_fields=[["TRUSTEE"], ["TRUSTEE"], ["TRUSTEE"], 
                              ["TRUSTEE"], ["TRUSTEE"], ["TRUSTEE"], ["TRUSTEE"]],
        prompt=BASE_PROMPT
    ),
    FormType.FORM_1099_C.value: FormTypeConfig(
        sensitive_fields=["DEBTOR", "DEBTOR's", "name",
                          "street",
                          "city",
                          "country",
                          "Account Number"],
        avoid_masking_fields=[["CREDITOR"], ["CREDITOR"], [
            "CREDITOR"], ["CREDITOR"], ["CREDITOR"], ["CREDITOR"], ["CREDITOR"]],
        prompt=BASE_PROMPT
    ),
    FormType.FORM_RRB_1099.value: FormTypeConfig(
        sensitive_fields=["Recipient", "Recipient's", "name",
                          "street",
                          "city",
                          "country",
                          "Account Number",
                          "Claim Number"],
        avoid_masking_fields=[["Payer"], ["Payer"], [
            "PAYER"], ["PAYER"], ["PAYER"], ["PAYER"], ["PAYER"],[]],
        prompt=BASE_PROMPT
    ),
    FormType.FORM_RRB_1099_R.value: FormTypeConfig(
        sensitive_fields=["Recipient", "Recipient's", "name",
                          "street",
                          "city",
                          "country",
                          "Account Number",
                          "Claim Number"],
        avoid_masking_fields=[["Payer"], ["Payer"], [
            "PAYER"], ["PAYER"], ["PAYER"], ["PAYER"], ["PAYER"],[]],
        prompt=BASE_PROMPT
    ),
    FormType.FORM_PROPERTY_TAX.value: FormTypeConfig(
        sensitive_fields=[],
        avoid_masking_fields=[],
        prompt=BASE_PROMPT
    ),
    FormType.UNKNOWN.value: FormTypeConfig(sensitive_fields=[], avoid_masking_fields=[], prompt=BASE_PROMPT),
    FormType.INSTRUCTIONS.value: FormTypeConfig(sensitive_fields=[], avoid_masking_fields=[], prompt=BASE_PROMPT),
}

# Form Identifiers
FORM_IDENTIFIERS = {
    FormType.FORM_1098_E.value: ["Student Loan Interest", "Student Loan"],
    FormType.FORM_1099_DIV.value: ["1099-DIV", "Dividends and Distributions", "Dividends and", "DIVIDENDS AND DISTRIBUTIONS", "DIVIDENDS AND", "Annual Summary", "Information Returns"],
    FormType.FORM_1099_INT.value: ["Interest Income", ["Interest", "Income", "1099-INT", "-INT"], ["1099-INT", "-INT"]],
    FormType.FORM_1099_MISC.value: ["Miscellaneous Income", ["Miscellaneous", "-MISC"], ["Miscellaneous", "Income", "-MISC"], ["Miscellaneous", "income", "-MISC"], ["Miscellaneous", "information", "-MISC"]],
    FormType.FORM_1099_NEC.value: ["Nonemployee Compensation", "Nonemployee"],
    FormType.FORM_1098_T.value: ["Tuition Statement", "Tuition"],
    FormType.FORM_1098.value: ["Mortgage Interest", "Mortgage"],
    FormType.FORM_1099_G.value: ["Certain Government Payments", "CERTAIN GOVERNMENT PAYMENTS", ["Government", "Payments"]],
    FormType.FORM_1099_R.value: ["Distributions From Pensions", "Pensions, Annuities", "Annuities", "Profit-Sharing", "Insurance Contracts", "1099-R"],
    FormType.FORM_1099_B.value: ["1099-B", "1099B"],
    FormType.FORM_CRYPTO_TAX.value: ["1099-B", "1099B"],
    FormType.FORM_CRYPTO_TAX_SIMPLE.value: ["1099-B", "1099B"],
    FormType.FORM_CRYPTO_TAX_COINBASE.value: ["1099-B", "1099B"],
    FormType.FORM_1095_A.value: ["Health Insurance", "Policy Issuer's Name", "Policy Termination Date"],
    FormType.FORM_1099_SSA.value: ["Social Security Benefit Statement", "SOCIAL SECURITY BENEFIT", "Social Security Benefit", "Security Benefit Statement"],
    FormType.FORM_W_2G.value: ["Winner", "Gamble"],
    FormType.FORM_W_2.value: ["W2","Tax Statement", "wage and Tax Statement", ["Wage", "Tax", "Statement"]],
    FormType.FORM_K_1_1065.value: ["1065", "Schedule K-1", "K-1", "K-1-P", ["Schedule", "K-1"], ["Shareholder's", "Entity", "Reporting"], ["Allocation", "Losses"], ["Schedule B", "shareholder"], ["Beneficiary's", "share", "Income", "trust", "estate", "Schedule K-1-T", "K-1-T"]],
    FormType.FORM_K_1_1120_S.value: ["1120-S", "Schedule K-1", "K-1", ["Schedule", "K-1"], ["Shareholder's", "Entity", "Reporting"], ["Allocation", "Losses"], ["Schedule B", "shareholder"], ["Beneficiary's", "share", "Income", "trust", "estate", "Schedule K-1-T", "K-1-T"]],
    FormType.FORM_K_1_1041.value: ["1041", "Schedule K-1", "K-1", ["Schedule", "K-1"], ["Shareholder's", "Entity", "Reporting"], ["Allocation", "Losses"], ["Schedule B", "shareholder"], ["Beneficiary's", "share", "Income", "trust", "estate", "Schedule K-1-T", "K-1-T"]],
    FormType.FORM_1099_PATR.value: ["1099-PATR", "Patronage Dividends", ["Taxable", "Distributions", "Received", "From", "Cooperatives"]],
    FormType.FORM_1099_OID.value: ["1099-OID", "Original Issue Discount", "Original issue discount for", "Tax-exempt OID"],
    FormType.FORM_1099_LTC.value: ["1099-LTC", "Long-Term Care and Accelerated Death Benefits", ["Long-Term Care and", "Accelerated Death", "Benefits"] , "Accelerated death benefits"],
    FormType.FORM_1099_S.value: ["TRANSFEROR" , "transferor is a foreign person", ["Proceeds From Real", "Estate Transactions"], ["Proceeds From", "Real Estate", "Transactions"]],
    FormType.FORM_1099_SA.value: ["1099-SA",["Archer MSA, or", "Medicare Advantage"]],
    # FormType.FORM_5498.value: ["Form 5498", ["IRA contributions (other", "Rollover contributions"]],
    FormType.FORM_5498_SA.value: ["Form 5498-SA", ["Total HSA or Archer MSA contributions", "Fair market value of HSA"]], 
    FormType.FORM_1099_C.value: ["1099-C", "Amount of debt discharged", "Check here if the debtor was"],
    FormType.FORM_RRB_1099.value: ["Payments by the Railroad"],
    FormType.FORM_RRB_1099_R.value: ["Annuities or Pensions by"],
    FormType.FORM_PROPERTY_TAX.value: ["First Installment", "Second Installment", "Property Taxes", "Property Tax", ["1st", "Installment"], ["2nd", "Installment"], "Parcel Number"],
}


NOT_PRESENT_FORM_IDENTIFIERS = {
    FormType.FORM_1098_E.value: ["Interest Income"],
    FormType.FORM_1099_DIV.value: ["1099-INT", "Interest Income", "Form 5498"],
    FormType.FORM_1099_INT.value: ["Winner", "Gamble", "k-1", "1099-G", "Notice 703", "1099-PATR"],
    FormType.FORM_1099_MISC.value: ["Winner", "Gamble", "k-1"],
    FormType.FORM_1099_NEC.value: ["1099-MISC"],
    FormType.FORM_1098_T.value: [],
    FormType.FORM_1098.value: ["1099-S", "First Installment", "Second Installment", "1st Installment", "2nd Installment", "Parcel Number"],
    FormType.FORM_1099_G.value: [],
    FormType.FORM_1099_R.value: ["Wage", "Tax Statement", "k-1", "Payments by the Railroad", "Annuities or Pensions by"],
    FormType.FORM_1099_B.value: ["1099-C"],
    FormType.FORM_CRYPTO_TAX.value: ["1099-C"],
    FormType.FORM_CRYPTO_TAX_COINBASE.value: ["1099-C"],
    FormType.FORM_CRYPTO_TAX_SIMPLE.value: ["1099-C"],
    FormType.FORM_1095_A.value: ["W-2", "W2"],
    FormType.FORM_1099_SSA.value: ["Notice 703"],
    FormType.FORM_W_2G.value: [],
    FormType.FORM_W_2.value: ["Schedule k-1", "k-1", "Notice 703", "Payments by the Railroad", "Annuities or Pensions by", "Parcel Number", "First Installment", "Second Installment", "Property Taxes", "Property Tax", "TRANSFEROR"],
    FormType.FORM_K_1_1065.value: ["1041", "Form IL-1120", "Beneficiary", "Estate or Trust", "corporation", "Notice 703"],
    FormType.FORM_K_1_1120_S.value: ["1041", "Beneficiary", "Estate or Trust", "Partner's Share of Income", "Notice 703"],
    FormType.FORM_K_1_1041.value: ["Form IL-1120", "1065", "Partner's Share of Income", "Shareholder's share of income", "Notice 703"],
    FormType.FORM_1099_PATR.value: ["Notice 703"],
    FormType.FORM_1099_OID.value: ["Notice 703"],
    FormType.FORM_1099_LTC.value: ["Notice 703"],
    FormType.FORM_1099_S.value: ["Notice 703", "1099-SA"],
    FormType.FORM_1099_SA.value: ["Notice 703", "5498-SA"],
    # FormType.FORM_5498.value: ["Notice 703", "5498-SA"],
    FormType.FORM_5498_SA.value: ["Notice 703"],
    FormType.FORM_1099_C.value: ["Notice 703"],
    FormType.FORM_RRB_1099.value: ["Notice 703"],
    FormType.FORM_RRB_1099_R.value: ["Notice 703"],
    FormType.FORM_PROPERTY_TAX.value: ["Notice 703"]

}


NON_IMPORTANT_FORM_TYPES = [
    FormType.INSTRUCTIONS.value,
    FormType.UNKNOWN.value
]

FORM_TYPE_TO_IGNORE_WHILE_FORM_FINALISATION =[
    FormType.FORM_PROPERTY_TAX.value
]
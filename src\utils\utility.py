import base64
import re
import logging
import json
from typing import List, Optional
import cv2
import pytesseract
import numpy as np
from datetime import datetime
from src.provider.llm.llm_provider import get_llm_provider
from langchain_core.messages import HumanMessage
from src.constants.constants import (
    EIN_PATTERN,
    SEQUENCE_NUMBER_PATTERN
)
from src.config.form_config import FORM_CONFIG, FormTypeConfig
from src.constants.enum import LLM_PROVIDER, FormType
import os

def perform_inner_cell_masking(form_type,cell_image, original_img):
    custom_config = r'--oem 3 --psm 6'
    data = pytesseract.image_to_data(cell_image, config=custom_config, output_type=pytesseract.Output.DICT)

    # Retrieve form configuration
    form_config = FORM_CONFIG.get(form_type)

    is_masked = False

    if not form_config:
        return False, original_img  # Return False if the form type is not configured

    # Iterate through the detected words
    n_boxes = len(data['text'])
    for i in range(n_boxes):
        if int(data['conf'][i]) > 5:  # Confidence threshold to filter out false positives
            text = data['text'][i].strip().lower()

            for sensitive_field, masking_func in form_config.inner_masking_trigger.items():
                # print("SENSITIVE FIELD", sensitive_field, text)
                if masking_func(text):
                    (x, y, w, h) = (data['left'][i], data['top'][i], data['width'][i], data['height'][i])
                    
                    is_masked = True
                    
                    # Mask the detected word with a black rectangle
                    cv2.rectangle(cell_image, (x, y), (x + w, y + h), (255, 0, 0), -1)
    
    return is_masked, cell_image

def is_sensitive_label(form_type: FormType, text: str) -> bool:
    """
    Check if the text contains any sensitive labels for the given form type.

    Args:
        form_type (FormType): The type of form being processed.
        text (str): Text to check.

    Returns:
        bool: True if the text contains sensitive information, otherwise False.
    """
    # Retrieve form configuration
    form_config = FORM_CONFIG.get(form_type)

    if not form_config:
        return False  # Return False if the form type is not configured

    # Clean the input text for comparison replace / with space
    text_clean = re.sub(r'[^\w\s]', '', text.replace('/', ' ')).lower()

    final_output = False

    # Check for sensitive labels in the text
    for (sensitive_label_idx,label) in enumerate(form_config.sensitive_fields):
        label_clean = re.sub(r'[^\w\s]', '', label).lower()
        # Math whole word instead of substrings
        if form_type == FormType.FORM_1098.value:
            print("Text--->", text_clean)
            print("Label--->", label_clean)
        if re.search(rf'\b{label_clean}\b', text_clean):
            final_output = True
            # If sensitive label is found, check for fields to avoid mask
            for avoid_field in form_config.avoid_masking_fields[sensitive_label_idx]:
                masking_clean = re.sub(r'[^\w\s]', '', avoid_field).lower()
                if masking_clean in text_clean:
                    return False
    if final_output:
        print('====>> sensitive')
    return final_output  # No sensitive label found

def is_ein_number(text: str) -> bool:
    """
    Check if the text matches an EIN number pattern.

    Args:
        text (str): Text to check.

    Returns:
        bool: True if matches EIN pattern, else False.
    """
    return bool(re.search(EIN_PATTERN, text))

def remove_sequence_numbers(image: np.ndarray) -> np.ndarray:
    """
    Remove sequence numbers from the image by masking them.

    Args:
        image (np.ndarray): Image array.

    Returns:
        np.ndarray: Image with sequence numbers masked.
    """
    data = pytesseract.image_to_data(
        image, output_type=pytesseract.Output.DICT, config='--oem 3 --psm 6 -l eng')
    image_no_seq = image.copy()

    for i, text in enumerate(data['text']):
        if text.strip() == '':
            continue
        if is_sequence_number(text):
            x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
            cv2.rectangle(image_no_seq, (x, y),
                          (x + w, y + h), (255, 255, 255), -1)
            logging.debug(
                f"Removed sequence number: '{text}' at position: ({x}, {y}, {w}, {h})")
    return image_no_seq

def is_sequence_number(text: str) -> bool:
    """
    Check if the text is a sequence number.

    Args:
        text (str): Text to check.

    Returns:
        bool: True if it's a sequence number, else False.
    """
    return bool(re.match(SEQUENCE_NUMBER_PATTERN, text.strip()))


def get_prompt(form_type: FormType) -> str:
    """
    Retrieve the prompt for the given form type from the FORM_CONFIG dictionary.

    Args:
        form_type (FormType): The type of form for which the prompt is required.

    Returns:
        str: The prompt associated with the given form type. If no prompt is found, 
             returns a default message indicating no prompt is available.
    """
    form_config = FORM_CONFIG.get(form_type)

    if form_config and form_config.prompt:
        return form_config.prompt
    else:
        return f"No specific prompt found for form type: {form_type}"
    
def draw_table_borders(image_path, output_path, config: FormTypeConfig):
    # Extract draw_border_config from the provided config object
    draw_border_config = config.draw_border_config

    # Unpack values from draw_border_config with defaults
    min_area = draw_border_config.get("min_area", 10000)
    aspect_ratio_range = draw_border_config.get("aspect_ratio_range", (2.0, 4.0))
    border_thickness = draw_border_config.get("border_thickness", 1)
    kernel_size = draw_border_config.get("kernel_size", (3, 3))
    iterations = draw_border_config.get("iterations", 2)

    # Read the image
    image = cv2.imread(image_path)

    # Convert to grayscale
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Thresholding to get binary image
    _, binary = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY_INV)

    # Create kernel and apply dilation
    kernel = np.ones(kernel_size, np.uint8)
    binary = cv2.dilate(binary, kernel, iterations=iterations)

    # Find contours in the binary image
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # Filter contours based on area and aspect ratio range
    filtered_contours = []
    for contour in contours:
        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = w / float(h)
        
        if cv2.contourArea(contour) > min_area and aspect_ratio_range[0] < aspect_ratio < aspect_ratio_range[1]:
            filtered_contours.append(contour)

    # Sort contours by their vertical position (y-coordinate)
    filtered_contours = sorted(filtered_contours, key=lambda ctr: cv2.boundingRect(ctr)[1])

    # Draw borders around the filtered contours
    for contour in filtered_contours:
        x, y, w, h = cv2.boundingRect(contour)
        cv2.rectangle(image, (x, y), (x + w, y + h), (0, 0, 0), border_thickness)

    # Save the output image
    cv2.imwrite(output_path, image)


def transform_file_path(input_path: str) -> str:
    # Extract the filename with extension
    filename = os.path.basename(input_path)
    
    # Construct the new path with 'border' as the folder
    new_folder = 'border'
    new_path = os.path.join(new_folder, filename)
    
    # Create the 'border' folder if it doesn't exist
    os.makedirs(new_folder, exist_ok=True)
    
    return new_path


def remove_top_bottom_whitespace(input_path, output_path):
    """
    Remove white space from the top and bottom of an image, given input and output paths.

    Parameters:
    - input_path: str
        The file path to the input image.
    - output_path: str
        The file path where the processed image will be saved.
    """
    # Load the image from the input path
    image = cv2.imread(input_path)

    # Check if the image was loaded successfully
    if image is None:
        logging.warning(f"Error: Unable to read image at {input_path}")
        return

    # Convert to grayscale if the image is in color
    if len(image.shape) == 3:
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        gray = image.copy()

    # Apply Otsu's thresholding after inverting the grayscale image
    # Invert the image to make the text white and the background black
    _, thresh = cv2.threshold(
        255 - gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU
    )

    # Apply morphological operations to remove noise and fill gaps
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 5))
    thresh = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)

    # Find contours on the binary image
    contours, _ = cv2.findContours(
        thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
    )

    # If no contours are found, save the original image
    if not contours:
        logging.warning("Warning: No content found in the image. Saving the original image.")
        cv2.imwrite(output_path, image)
        return

    # Initialize variables to store the extreme points
    y_min = image.shape[0]
    y_max = 0

    # Loop through contours to find the extreme top and bottom points
    for cnt in contours:
        _, y, _, h = cv2.boundingRect(cnt)
        y_min = min(y_min, y)
        y_max = max(y_max, y + h)

    # Crop the image vertically between y_min and y_max
    cropped_image = image[y_min:y_max, :]
    cv2.imwrite(output_path, cropped_image)
    cv2.imwrite('this.png', cropped_image)
    logging.info(f"Cropped image saved to {output_path}")

def parse_confidence(osd_output):
    """
    Parses the 'Orientation confidence' from Tesseract's OSD text.
    """
    conf_match = re.search(r"Orientation confidence:\s+([\d.]+)", osd_output)
    if conf_match:
        return float(conf_match.group(1))
    return None

def correct_orientation(image_path, output_path):
    img = cv2.imread(image_path)
    if img is None:
        logging.warning(f"Error: Unable to open image file '{image_path}'.")
        return

    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # Try Tesseract OSD
    try:
        osd = pytesseract.image_to_osd(gray)
    except pytesseract.TesseractError:
        # Retry with custom config if the first attempt fails
        custom_config = r'--psm 0 -c min_characters_to_try=1'
        try:
            osd = pytesseract.image_to_osd(gray, config=custom_config)
        except pytesseract.TesseractError as e:
            logging.warning(f"Tesseract OCR error: {e}")
            logging.warning("Assuming angle is 0.")
            angle = 0
            confidence = 0
        else:
            angle = parse_angle(osd) or 0
            confidence = parse_confidence(osd) or 0
    else:
        angle = parse_angle(osd) or 0
        confidence = parse_confidence(osd) or 0

    # Optional: If confidence is below a threshold, skip rotation
    if confidence < 5:  # pick a threshold that makes sense for your images
        logging.info(f"Orientation confidence too low ({confidence}). Skipping rotation.")
        angle = 0

    # Tesseract typically returns multiples of 90 for orientation,
    # but you could also round to the nearest multiple of 90 if needed:
    valid_angles = [0, 90, 180, 270]
    if angle not in valid_angles:
        # Round to the nearest multiple of 90
        angle = round(angle / 90) * 90

    # If angle is 0 after these checks, no rotation is needed
    if angle == 0:
        cv2.imwrite(output_path, img)
        logging.info(f"No rotation applied. Saved to '{output_path}'.")
        return

    # Otherwise, rotate
    rotated = rotate_image(img, angle)
    cv2.imwrite(output_path, rotated)
    logging.info(f"Image rotated by {angle} degrees with confidence {confidence} and saved to '{output_path}'.")
    return angle

def parse_angle(osd_output):
    # Parse the rotation angle from OSD output
    angle_match = re.search('(?<=Rotate: )\d+', osd_output)
    if angle_match:
        angle = int(angle_match.group(0))
        # Validate angle
        if angle in [0, 90, 180, 270]:
            return angle
    # Default to 0 if angle is invalid
    return 0

def rotate_image(image, angle):
    if angle == 90:
        return cv2.rotate(image, cv2.ROTATE_90_CLOCKWISE)
    elif angle == 180:
        return cv2.rotate(image, cv2.ROTATE_180)
    elif angle == 270:
        return cv2.rotate(image, cv2.ROTATE_90_COUNTERCLOCKWISE)
    else:
        # For arbitrary angles, use warpAffine
        (h, w) = image.shape[:2]
        center = (w // 2, h // 2)
        M = cv2.getRotationMatrix2D(center, -angle, 1.0)
        rotated = cv2.warpAffine(image, M, (w, h), flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_REPLICATE)
        return rotated
    
def get_number(f):
    # Extract numbers that are preceded and followed by underscores or end of string
    match = re.search(r'_(\d+)(?:_|$)', f)
    if match:
        return int(match.group(1))  # Return the matched number as an integer
    else:
        return float('inf')
    


def parse_llm_response(response_text: str):
    """
    Attempt to robustly parse the LLM response text into a list of strings
    (e.g. tax form types).
    """
    # 1. Try direct JSON parse (Case A: proper JSON array)
    try:
        if response_text.startswith('[') and response_text.endswith(']'):
            return json.loads(response_text)  # If it's valid JSON, great!
    except json.JSONDecodeError:
        pass

    # 2. Try double-parsing if we suspect a string-encoded JSON (Case B)
    #    e.g. "\"[\"Form 1099-DIV\"]\""
    try:
        # If it starts with a quote, it might be a JSON-string of a JSON array.
        if response_text.startswith('"') and response_text.endswith('"'):
            first_pass = json.loads(response_text)  # remove the outer quotes
            if isinstance(first_pass, str) and first_pass.startswith('[') and first_pass.endswith(']'):
                return json.loads(first_pass)  # parse the actual array
    except json.JSONDecodeError:
        pass

    # 3. Attempt bracketed fix (Case C: [Form 1099-DIV] with no quotes)
    #    We do a quick check if it starts with '[' and ends with ']'.
    #    We'll manually parse the content inside.
    #    - This is *not* valid JSON, but we can fix it.
    pattern = r'^\[\s*[A-Za-z0-9].*\]$'
    if re.match(pattern, response_text.strip()):
        # remove leading '[' and trailing ']'
        content = response_text.strip()[1:-1].strip()
        # now you might have "Form 1099-DIV" or "Form 1099-DIV, Form 1099-INT"
        # split by commas to separate multiple forms
        parts = [p.strip() for p in content.split(',')]
        # For each part, we ensure it's wrapped in quotes
        # Then we produce a final string that *is* valid JSON array
        items = []
        for p in parts:
            # If user didn't add quotes, we add them:
            if not (p.startswith('"') and p.endswith('"')):
                p = f'"{p}"'
            items.append(p)
        # join the items with commas again, wrap in brackets
        repaired_str = "[" + ",".join(items) + "]"
        try:
            return json.loads(repaired_str)
        except json.JSONDecodeError:
            # If for any reason that fails, we just treat everything as one item
            return [response_text]

    # 4. If everything fails, treat as single item
    return [response_text]


def extract_value_before_underscore(file_name):
    """
    Extracts the value before the first underscore in the given string.

    :param file_name: The input string, e.g., "100-00-0007_2024.json"
    :return: The substring before the first underscore, or the full string if no underscore is found.
    """
    return file_name.split('_', 1)[0]

def remove_hyphens(input_string):
    """
    Removes all hyphens from the given string.

    :param input_string: The input string, e.g., "12-23434-23243"
    :return: The string without hyphens.
    """
    return input_string.replace('-', '')

def flatten_1099b(data):
    flattened = []

    for form in data.get(FormType.FORM_1099_B.value, []):
        form_details = form.get("form_details", {})
        payer_information = form.get("payer_information", {})
        transaction_details = form.get("transaction_details", {}).get("value", [])

        # If there are no transactions, create a single entry with the base details
        if not transaction_details:
            flattened.append({
                "form_details": form_details,
                "payer_information": payer_information,
                "transaction": None
            })
        else:
            # Iterate over each transaction group
            for transaction_group in transaction_details:
                description = transaction_group.get("description_of_property", {}).get("value", "")
                transactions = transaction_group.get("transactions", [])

                # Create a separate object for each transaction
                for transaction in transactions:
                    flattened.append({
                        "form_details": form_details,
                        "payer_information": payer_information,
                        "transaction_description": description,
                        "transaction": transaction
                    })

    return flattened

def convert_date_to_mm_dd_yyyy(date_str, input_format="%Y-%m-%d"):
    """
    Converts a date string from a given format to MM-DD-YYYY format.
    If the input format does not match the date string, it returns the original string.

    Parameters:
    - date_str (str): The input date string.
    - input_format (str): The format of the input date string.

    Returns:
    - str: The date string in MM-DD-YYYY format, or the original string if the format does not match.
    """
    try:
        # Try to parse the input date string using the given format
        parsed_date = datetime.strptime(date_str, input_format)
        # Convert to MM-DD-YYYY format
        formatted_date = parsed_date.strftime('%m-%d-%Y')
        return formatted_date
    except ValueError:
        # If parsing fails, return the input string as is
        return date_str
    
def send_images_to_openai(
    prompt: str,
    image_paths: Optional[List[str]] = None,
    base64_images: Optional[List[str]] = None
) -> str:
    """
    Sends multiple images to OpenAI for image processing.

    Args:
        prompt (str): The text prompt to send.
        image_paths (Optional[List[str]]): A list of file paths to the images.
        base64_images (Optional[List[str]]): A list of base64 encoded images.

    Returns:
        str: The OCR-processed text in JSON format.
    """
    try:
        # Get the LLM provider (assumes get_llm_provider and LLM_PROVIDER are defined elsewhere)
        llm = get_llm_provider(LLM_PROVIDER.OPENAI_GPT_4O, metadata={})
        
        # Start with the text prompt in the content list
        content_list = [{"type": "text", "text": prompt}]
        
        # Process images provided via file paths
        if image_paths:
            for image_path in image_paths:
                with open(image_path, "rb") as image_file:
                    encoded_image = base64.b64encode(image_file.read()).decode("utf-8")
                image_url = f"data:image/png;base64,{encoded_image}"
                content_list.append({
                    "type": "image_url",
                    "image_url": {"url": image_url}
                })

        # Process images provided as base64 strings
        if base64_images:
            for b64_img in base64_images:
                image_url = f"data:image/png;base64,{b64_img}"
                content_list.append({
                    "type": "image_url",
                    "image_url": {"url": image_url}
                })

        # Create the HumanMessage with all the content (text + multiple images)
        image_prompt = HumanMessage(content=content_list)
        
        # Invoke the model
        response = llm.invoke([image_prompt])
        return response.content

    except Exception as e:
        logging.error(f"Failed to send images for OpenAI processing: {e}")
        return ""

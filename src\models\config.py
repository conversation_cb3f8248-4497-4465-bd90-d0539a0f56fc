from src.constants.env_constant import env_var
from src.constants.enum import UserRole
import mongoengine as me
from src.models.users import User
from src.utils.bcrypt_utils import get_hash_text

def init_db():
    me.connect(
        db=env_var["MONGO_DB"],
        host=env_var["MONGO_URI"],
    )
    create_admin_if_not_exists()

def create_admin_if_not_exists():
    # Check if an admin user already exists (adjust the query as needed)
    admin_user = User.get(roles=[UserRole.ADMIN.value])
    if not admin_user:
        # Create a new admin user if one does not exist.
        # You can customize the default admin details as necessary.
        hashed_pw = get_hash_text(env_var["DEFAULT_ADMIN_PASSWORD"])
        admin = User(
            name="Admin",
            email=env_var["DEFAULT_ADMIN_EMAIL"],
            password=hashed_pw,
            roles=[UserRole.ADMIN.value],
        )
        admin.save()
        print("Admin user created.")

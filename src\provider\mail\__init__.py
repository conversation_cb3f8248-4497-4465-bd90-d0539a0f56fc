from src.config.mail import mail_configuration
from src.config.logger import logging


def get_mail_provider(provider):
    """
    Get the mail provider object based on the provided provider name.

    Args:
    - provider (str): The name of the mail provider to retrieve.

    Returns:
    - provider_object: The mail provider object.

    Raises:
    - ValueError: If the provided provider name is invalid.
    """
    try:
        # Attempt to retrieve the provider object from the configuration
        provider_object = mail_configuration[provider]['provider']

    except Exception as e:
        # Log the error along with the configuration for debugging purposes
        logging.error(f"Invalid provider: {provider}. Configuration: {mail_configuration} = {e}")

        # Raise a ValueError with a meaningful error message
        raise ValueError(f"Invalid Mail provider: {provider}")

    return provider_object

import os
import re
import logging
import cv2
import numpy as np
import pytesseract
from src.constants.constants import (
    OUTPUTS_FOLDER,
    BORDER_SIZE,
    ADAPTIVE_THRESH_BLOCK_SIZE,
    ADAPTIVE_THRESH_C,
    VERTICAL_KERNEL_RATIO,
    PYTESSERACT_CONFIG
)
from pytesseract import Output
from src.constants.entities import Cell


def check_inner_masking_trigger(word: str) -> bool:
    """
    Checks if the word matches any trigger criteria for masking.
    1. Word contains fields such as 'identification', 'telephone', 'number', or 'federal'.
    2. Word matches a specific format: 9-digit number, (XXX), or XXX-XXXX.
    3. Removes unwanted special characters such as ", ', ,, ?, &, #, `, ', and alphabetic characters if they appear at the start or end.
       Hyphens (-) are allowed only if they are not at the start or end.
    :param word: Input word to validate.
    :return: True if it matches any of the masking triggers or formats, False otherwise.
    """

    # Remove special characters including the left single quotation mark (') and others except hyphen
    # remove unwanted special characters including backtick (`) and left single quotation mark (')
    cleaned_word = re.sub(r"[\"',?&#!`]", "", word)

    # Remove hyphens or alphabetic characters at the start or end
    cleaned_word = re.sub(r"(^[-a-zA-Z]+|[-a-zA-Z]+$)", "", cleaned_word)

    # Check for specific formats
    # if re.fullmatch(r"\d{9}", word):  # 9-digit number
    #     return True
    if re.fullmatch(r"\(\d{3}\)", cleaned_word):  # (XXX)
        return True
    if re.fullmatch(r"\d{3}-\d{4}", cleaned_word):  # XXX-XXXX
        return True
    if re.fullmatch(r"\d{3}-\d{3}-\d{4}", cleaned_word):  # XXX-XXX-XXXX
        return True
    if re.fullmatch(r"\d{2}-\d{3}-\d{2}", cleaned_word):  # XX-XXX-XX
        return True

    return False


def draw_table_borders_on_image(original_img, min_area=10000, aspect_ratio_range=(2.0, 6.0), border_thickness=2):
    """
    Draw table borders on the given image based on contour detection.

    Args:
        original_img (np.ndarray): The original image loaded with OpenCV.
        min_area (int): Minimum contour area to consider as a table. Default is 10000.
        aspect_ratio_range (tuple): Range of aspect ratios for valid contours. Default is (2.0, 6.0).
        border_thickness (int): Thickness of the drawn border. Default is 2.

    Returns:
        np.ndarray: Image with table borders drawn.
    """
    # Convert to grayscale
    import cv2
    import numpy as np

    gray = cv2.cvtColor(original_img, cv2.COLOR_BGR2GRAY)

    # Threshold the image to binary (invert colors)
    _, binary = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY_INV)

    # Dilate the image to enhance contours
    kernel = np.ones((5, 5), np.uint8)
    binary = cv2.dilate(binary, kernel, iterations=5)

    # Find contours
    contours, _ = cv2.findContours(
        binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    filtered_contours = []
    for contour in contours:
        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = w / float(h)

        # Filter contours by area and aspect ratio
        if cv2.contourArea(contour) > min_area and aspect_ratio_range[0] < aspect_ratio < aspect_ratio_range[1]:
            filtered_contours.append(contour)

    # Sort contours by their vertical position (y-coordinate)
    filtered_contours = sorted(
        filtered_contours, key=lambda ctr: cv2.boundingRect(ctr)[1])

    # Draw rectangles around the detected table-like contours
    for contour in filtered_contours:
        x, y, w, h = cv2.boundingRect(contour)
        cv2.rectangle(original_img, (x, y), (x + w, y + h),
                      (0, 0, 0), border_thickness)

    return original_img


def mask_employee_name_and_address(original_img):
    """
    Detect and mask the 'Employee Name and Address' section in the given image.

    Args:
        original_img (np.ndarray): The original image loaded with OpenCV.

    Returns:
        np.ndarray: The modified image with the sensitive section masked.
    """
    import cv2
    import pytesseract
    from pytesseract import Output

    # Apply OCR to extract text with bounding box information
    detection_data = pytesseract.image_to_data(
        original_img, output_type=Output.DICT, config=r'--oem 3 --psm 6')

    # Initialize variables to store the bounding box of the "Employee Name and Address" section
    bounding_boxes = []

    # Extract text and search for the phrase "Employee Name and Address"
    detected_words = detection_data['text']
    num_words = len(detected_words)

    for i in range(num_words - 3):  # Look ahead to match the full phrase
        phrase = (
            detected_words[i].strip().lower() + " " +
            detected_words[i + 1].strip().lower() + " " +
            detected_words[i + 2].strip().lower() + " " +
            detected_words[i + 3].strip().lower()
        )

        # If the phrase matches, save its bounding box
        if "employee name and address" in phrase:
            x = detection_data['left'][i]
            y = detection_data['top'][i]
            w = detection_data['width'][i]
            h = detection_data['height'][i]
            bounding_boxes.append((x, y, w, h))

    # Mask each detected section dynamically
    for (x, y, w, h) in bounding_boxes:
        # Dynamically determine mask dimensions based on detected area
        mask_start_y = y + h + 5  # Start just below the label
        mask_height = 15 * h  # Estimate height for the address block dynamically
        mask_end_y = mask_start_y + mask_height

        mask_width = w + 500  # Extend width to account for possible long address lines
        mask_start_x = max(0, x - 10)  # Slight padding to the left
        mask_end_x = min(original_img.shape[1], mask_start_x + mask_width)

        # Ensure mask stays within image boundaries
        mask_start_y = max(0, mask_start_y)
        mask_end_y = min(original_img.shape[0], mask_end_y)

        # Apply the mask (white rectangle) over the detected region
        cv2.rectangle(original_img, (mask_start_x, mask_start_y),
                      (mask_end_x, mask_end_y), (255, 255, 255), -1)

    return original_img


def sort_contours(cnts, hierarchy, method="top-to-bottom"):
    """
    Sort contours based on the specified method.

    Args:
        cnts: List of contours.
        hierarchy: Hierarchy array corresponding to the contours.
        method (str): Sorting method.

    Returns:
        Tuple[List, List, List]: Sorted contours, their bounding boxes, and sorted hierarchy.
    """
    bounding_boxes = [cv2.boundingRect(c) for c in cnts]
    cnts_with_data = list(zip(cnts, bounding_boxes, hierarchy))

    # Sort by y (top to bottom), then x (left to right)
    cnts_with_data.sort(key=lambda b: (b[1][1], b[1][0]))
    sorted_cnts, sorted_boxes, sorted_hierarchy = zip(
        *cnts_with_data) if cnts_with_data else ([], [], [])
    return sorted_cnts, sorted_boxes, sorted_hierarchy


def preprocess_image(image: np.ndarray) -> np.ndarray:
    os.makedirs(OUTPUTS_FOLDER, exist_ok=True)
    mean_bgr = cv2.mean(image)[:3]
    border_color = tuple(int(c) for c in mean_bgr)
    image_with_border = cv2.copyMakeBorder(
        image,
        top=BORDER_SIZE,
        bottom=BORDER_SIZE,
        left=BORDER_SIZE,
        right=BORDER_SIZE,
        borderType=cv2.BORDER_CONSTANT,
        value=border_color
    )
    if len(image_with_border.shape) == 3:
        gray = cv2.cvtColor(image_with_border, cv2.COLOR_BGR2GRAY)
    else:
        gray = image_with_border
    return gray


def preprocess_text(extracted_text):
    """
    Preprocess the OCR extracted text to improve matching accuracy.
    - Replace common OCR misinterpretations.
    - Remove special characters.
    - Normalize spacing and casing.
    """
    # Replace common OCR misinterpretations
    replacements = {
        '‘': '',
        '’': '',
        '“': '',
        '”': '',
        # Add more replacements as needed
    }
    for key, value in replacements.items():
        extracted_text = extracted_text.replace(key, value)

    # Remove non-printable characters
    extracted_text = re.sub(r'[^\x20-\x7E]', '', extracted_text)

    return extracted_text


def mask_recipient_tin_1099_DIV(original_img):
    """
    Detects and mask the 'Recipient's TIN' section in the given image of the format xxx-xx-1234.

    Args:
        original_img (np.ndarray): The original image loaded with OpenCV.

    Returns:
        np.ndarray: The modified image with the sensitive section masked.
    """

    img = original_img
    if len(original_img.shape) == 3:
        img = cv2.cvtColor(original_img, cv2.COLOR_BGR2GRAY)

    if img is None:
        logging.error("Error: Image not found or unable to read.")
        return [], []

    # Apply adaptive thresholding to invert the image
    img_bin = cv2.adaptiveThreshold(
        ~img, 255, cv2.ADAPTIVE_THRESH_MEAN_C,
        cv2.THRESH_BINARY, ADAPTIVE_THRESH_BLOCK_SIZE, ADAPTIVE_THRESH_C
    )

    # Define kernels to detect vertical and horizontal lines
    kernel_length = img.shape[1] // VERTICAL_KERNEL_RATIO
    vertical_kernel = cv2.getStructuringElement(
        cv2.MORPH_RECT, (1, kernel_length))
    horizontal_kernel = cv2.getStructuringElement(
        cv2.MORPH_RECT, (kernel_length, 1))
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))

    # Detect vertical lines
    vertical_lines = cv2.erode(img_bin, vertical_kernel, iterations=3)
    vertical_lines = cv2.dilate(vertical_lines, vertical_kernel, iterations=3)

    # Detect horizontal lines
    horizontal_lines = cv2.erode(img_bin, horizontal_kernel, iterations=3)
    horizontal_lines = cv2.dilate(
        horizontal_lines, horizontal_kernel, iterations=3)

    # Combine horizontal and vertical lines
    table_structure = cv2.addWeighted(
        vertical_lines, 0.5, horizontal_lines, 0.5, 0.0)
    table_structure = cv2.erode(~table_structure, kernel, iterations=2)
    _, table_structure = cv2.threshold(
        table_structure, 128, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU
    )

    # Find contours to detect cells
    contours, hierarchy = cv2.findContours(
        table_structure, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

    hierarchy = hierarchy[0]  # Convert hierarchy to a usable format

    # Sort contours from top to bottom, then left to right
    sorted_contours, _, sorted_hierarchy = sort_contours(
        contours, hierarchy, method="top-to-bottom")

    cell_images = []
    cell_coords = []

    for idx, (contour, hier) in enumerate(zip(sorted_contours, sorted_hierarchy)):
        # Check if contour has child contours
        if hier[2] != -1:
            # Contour has child contours, skip it
            continue
        x, y, w, h = cv2.boundingRect(contour)
        # Filter out too small or too large boxes
        # Extract the cell image
        cell_img = img[y:y+h, x:x+w]
        cell = Cell(index=idx, bounding_box=(x, y, w, h), image=cell_img)
        cell_images.append(cell.image)
        cell_coords.append(cell)

    logging.info(f"Extracted {len(cell_images)} cells from the table.")

    for idx, cell_img in enumerate(cell_images):
        cell = cell_coords[idx]
        preprocessed_cell_img = preprocess_image(cell_img)

        cell_filename = f'cells/cell_{idx}.png'
        cv2.imwrite(cell_filename, preprocessed_cell_img)

        # Perform OCR on the cell
        try:
            text = pytesseract.image_to_string(
                preprocessed_cell_img, config=PYTESSERACT_CONFIG)
            logging.debug(
                f"OCR extracted text from cell {cell.index}: '{text.strip()}'")
        except Exception as e:
            logging.error(f"OCR failed for cell {cell.index}: {e}")
            text = ""

        # print(f'Index: {idx} | Text: {text}')

        pattern_recipient_tin = r'^[A-Z]{3}-[A-Z]{2}-\d{4}'  # TIN format
        # Account Number format
        pattern_account_number = r"^[A-Za-z0-9]{4} \d{10}$"

        if re.match(pattern_recipient_tin, text) or re.match(pattern_account_number, text):
            x, y, w, h = cell.bounding_box
            cv2.rectangle(original_img, (x, y),
                          (x + w, y + h), (255, 255, 255), -1)
            logging.info(
                f'Masked cell at index {cell.index} containing sensitive data.')

    return original_img

# def mask_recipient_info_1099_DIV(original_image):
#     """
#     This function takes an image file, detects the word 'Recipient', and masks the area below it
#     (where the name and address are usually located), unless the word 'PAYER' is also present
#     on the same line.

#     Args:
#     - original_image (numpy array): The input image in which to detect and mask recipient information.

#     Returns:
#     - None: The function saves the masked image to 'new.png'.
#     """

#     # Load the image
#     image = original_image

#     # Convert the image to grayscale for better OCR accuracy
#     gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

#     # Perform OCR on the image using Tesseract
#     custom_config = r'--oem 3 --psm 6'  # Default OCR engine, psm 6 assumes a uniform block of text
#     detection = pytesseract.image_to_data(gray, config=custom_config, output_type=Output.DICT)

#     # Iterate over each detected text block to find the word "Recipient"
#     for i in range(len(detection['text'])):
#         text = detection['text'][i].strip().lower()

#         # If the detected text contains "Recipient" and does not contain "PAYER"
#         if "recipient" in text and "payer" not in text:
#             # Get the bounding box coordinates for this text
#             x, y, w, h = detection['left'][i], detection['top'][i], detection['width'][i], detection['height'][i]

#             # Define the area to be masked (below the "Recipient" line)
#             # Adjust the width (w*6) and height (h*6) based on how much area you need to cover
#             mask_top_left = (x, y)
#             mask_bottom_right = (x + w * 6, y + h * 6)

#             # Define the color (black) and thickness (-1 to fill the rectangle)
#             color = (255, 255, 255)  # White color
#             thickness = -1     # Fills the rectangle

#             # Draw a black rectangle over the recipient's information area
#             cv2.rectangle(image, mask_top_left, mask_bottom_right, color, thickness)

#     return image


def mask_ssn_k1(original_image):
    # Load image using OpenCV
    img = original_image

    # Convert image to grayscale (improves OCR accuracy)
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # Use Tesseract to extract text along with bounding box details
    boxes = pytesseract.image_to_data(
        gray, output_type=pytesseract.Output.DICT)

    # Regular expression to find SSN-like patterns (e.g., ***********)
    ssn_pattern = r'\b\d{3}-\d{2}-\d{4}\b'

    n_boxes = len(boxes['text'])

    for i in range(n_boxes):
        # Extract each word and its bounding box
        word = boxes['text'][i]
        x, y, w, h = boxes['left'][i], boxes['top'][i], boxes['width'][i], boxes['height'][i]

        # Check if the word matches the SSN pattern
        if re.match(ssn_pattern, word):
            # Mask the SSN by coloring that area white
            cv2.rectangle(img, (x, y), (x + w, y + h), (255, 255, 255), -1)

    # Save the output image

    return img


def cover_phrases_with_box_K1(original_img, phrases_to_cover):
    # Load the image
    image = original_img

    # Perform OCR to detect text and their bounding boxes
    data = pytesseract.image_to_data(image, output_type=Output.DICT)

    # Initialize an empty list to hold lines of text
    lines = []
    current_line = []
    # Start with the Y position of the first detected word
    current_y = data['top'][0]

    # Group words into lines based on vertical position
    for i in range(len(data['text'])):
        if data['text'][i].strip() == "":
            continue  # Skip empty text detections

        word = data['text'][i]
        x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]

        # Check if the word belongs to the current line (within a certain y-threshold)
        if abs(y - current_y) <= 10:
            current_line.append((word, x, y, w, h))
        else:
            # Add the current line to lines and start a new line
            lines.append(current_line)
            current_line = [(word, x, y, w, h)]
            current_y = y  # Update the current Y position

    # Add the last line if there’s any remaining words
    if current_line:
        lines.append(current_line)

    # Process each line to find phrases and cover them with blue boxes
    for i, line in enumerate(lines):
        # Join the words to form the line text
        line_text = " ".join([word[0] for word in line])

        # Check if any of the target phrases are in this line or the next one
        for phrase in phrases_to_cover:
            if phrase.lower() in line_text.lower():
                # Get bounding box coordinates covering the phrase in the current line
                x_min = min(word[1] for word in line)
                y_min = min(word[2] for word in line)
                x_max = max(word[1] + word[3] for word in line)
                y_max = max(word[2] + word[4] for word in line)

                # Check if the phrase might continue onto the next line
                if i + 1 < len(lines):
                    next_line_text = " ".join(
                        [word[0] for word in lines[i + 1]])
                    if phrase.lower() in (line_text + " " + next_line_text).lower():
                        # Extend the bounding box to cover the next line
                        next_line = lines[i + 1]
                        x_min = min(x_min, min(word[1] for word in next_line))
                        y_max = max(y_max, max(
                            word[2] + word[4] for word in next_line))

                # Draw a blue rectangle over the detected text area covering one or two lines
                # White box with filled color
                cv2.rectangle(image, (x_min, y_min),
                              (x_max+200, y_max+5), (255, 255, 255), -1)
    return image


def mask_phrases(original_img, phrases_to_mask):
    """
    Detect and mask specified phrases in the given image with phrase-specific parameters.

    Args:
        original_img (np.ndarray): The original image loaded with OpenCV.
        phrases_to_mask (list of dict): List of dictionaries, each containing:
            - 'phrase' (str): Phrase to detect and mask in the image.
            - 'mask_height_factor' (int): Multiplier for mask height based on detected text height.
            - 'mask_width_extension' (int): Additional width to extend the mask beyond detected text width.
            - 'top_padding' (int): Padding to apply around detected bounding boxes from the top.
            - 'left_padding' (int): Padding to apply around detected bounding boxes from the left.

    Returns:
        np.ndarray: The modified image with specified sections masked.
    """

    # gray_img = cv2.cvtColor(original_img, cv2.COLOR_BGR2GRAY)

    # # Thresholding to isolate the white area at the top
    # _, binary_img = cv2.threshold(gray_img, 240, 255, cv2.THRESH_BINARY)

    # # Find contours to detect the white area at the top
    # contours, _ = cv2.findContours(
    #     binary_img, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # # Crop out the white area at the top
    # for contour in contours:
    #     x, y, w, h = cv2.boundingRect(contour)
    #     # Assuming the white patch is near the top and takes up the full width of the image
    #     if y < original_img.shape[0] // 3 and w > original_img.shape[1] * 0.9:
    #         # Crop the image below the white area
    #         cropped_img = original_img[y + h:, :]
    #         break
    #     else:
    #         # In case no white patch is detected, use the original image
    #         cropped_img = original_img

    # # Further preprocessing for OCR
    # # Convert to grayscale
    # gray_cropped_img = cropped_img

    # Apply thresholding for clearer text
    # thresh_img = gray_cropped_img
    thresh_img = original_img

    # Apply OCR to extract text with bounding box information
    detection_data = pytesseract.image_to_data(
        thresh_img, output_type=Output.DICT, config=r'--oem 3 --psm 6')

    # Extract text data
    detected_words = detection_data['text']
    num_words = len(detected_words)

    # Loop through each phrase and its specific configuration to apply masking
    for phrase_config in phrases_to_mask:
        phrase_to_mask = phrase_config.get('phrase').lower()
        words_in_phrase = phrase_to_mask.split()
        phrase_length = len(words_in_phrase)

        mask_height_factor = phrase_config.get('mask_height_factor', 15)
        mask_width_extension = phrase_config.get('mask_width_extension', 500)
        top_padding = phrase_config.get('top_padding', 10)
        left_padding = phrase_config.get('left_padding', 10)

        bounding_boxes = []

        # Search for the phrase in detected text
        for i in range(num_words - phrase_length + 1):
            detected_phrase = " ".join(
                detected_words[i + j].strip().lower() for j in range(phrase_length))

            # If the phrase matches, save its bounding box
            if phrase_to_mask in detected_phrase:
                x = detection_data['left'][i]
                y = detection_data['top'][i]
                w = detection_data['width'][i]
                h = detection_data['height'][i]
                bounding_boxes.append((x, y, w, h))

        # Mask each detected section dynamically using specific parameters
        for (x, y, w, h) in bounding_boxes:
            mask_start_y = y + h + top_padding  # Start just below the detected text
            # Mask height based on factor
            mask_end_y = mask_start_y + int(mask_height_factor * h)

            # Slight padding to the left
            mask_start_x = max(0, x - left_padding)
            mask_end_x = min(
                thresh_img.shape[1], mask_start_x + w + mask_width_extension)

            # Ensure mask stays within image boundaries
            mask_start_y = max(0, mask_start_y)
            mask_end_y = min(thresh_img.shape[0], mask_end_y)

            # Apply the mask (white rectangle) over the detected region
            cv2.rectangle(thresh_img, (mask_start_x, mask_start_y),
                          (mask_end_x, mask_end_y), (255, 255, 255), -1)

    return thresh_img

from bson import ObjectId
from src.models.users import User
from src.models.pages import Page
from src.models.files import File
from src.constants.enum import QueueProcessingStatus

def update_user_status(user_id: str, new_status: str) -> bool:
    """
    Update the status of a user.
    :param user_id: The ID of the user to update.
    :param new_status: The new status to assign.
    :return: True if the update was successful, False otherwise.
    """
    if new_status not in [status.value for status in QueueProcessingStatus]:
        raise ValueError(f"Invalid status: {new_status}")
    
    updated_count = User.update_one(
        query={"id": ObjectId(user_id)},
        update_data={"set__queue_status": new_status}
    )
    return updated_count > 0


def update_page_status(page_id: str, new_status: str) -> bool:
    """
    Update the status of a page.
    :param page_id: The ID of the page to update.
    :param new_status: The new status to assign.
    :return: True if the update was successful, False otherwise.
    """
    if new_status not in [status.value for status in QueueProcessingStatus]:
        raise ValueError(f"Invalid status: {new_status}")
    
    updated_count = Page.update_one(
        query={"id": ObjectId(page_id)},
        update_data={"set__status": new_status}
    )
    return updated_count > 0

def update_file_status(file_id: str, new_status: str) -> bool:
    """
    Update the status of a file.
    :param file_id: The ID of the file to update.
    :param new_status: The new status to assign.
    :return: True if the update was successful, False otherwise.
    """
    if new_status not in [status.value for status in QueueProcessingStatus]:
        raise ValueError(f"Invalid status: {new_status}")
    
    updated_count = File.update_one(
        query={"id": ObjectId(file_id)},
        update_data={"set__status": new_status}
    )
    return updated_count > 0
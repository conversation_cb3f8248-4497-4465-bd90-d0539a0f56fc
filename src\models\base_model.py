from typing import List, Optional, Dict
from datetime import datetime, date
from bson import ObjectId
import mongoengine as me


class BaseDocument(me.Document):
    meta = {'abstract': True}  # Ensure this class is not treated as a collection

    created_at = me.DateTimeField(default=datetime.utcnow)
    updated_at = me.DateTimeField(default=datetime.utcnow)

    def save(self, *args, **kwargs):
        self.updated_at = datetime.utcnow()
        return super().save(*args, **kwargs)

    @classmethod
    def get(cls, **kwargs) -> Optional[me.Document]:
        return cls.objects(**kwargs).first()

    @classmethod
    def get_count(cls, filters: Dict = {}) -> int:
        return cls.objects(__raw__=filters).count()

    @classmethod
    def get_all(cls, skip: int = 0, limit: int = 100, filters: Dict = {}, exclude_fields: Optional[List[str]] = None) -> me.QuerySet:
        query = cls.objects(__raw__=filters)
        if exclude_fields:
            query = query.exclude(*exclude_fields)
        results = query.skip(skip).limit(limit)
        # Convert each document in the QuerySet to a dictionary
        return [doc.to_dict() for doc in results]

    @classmethod
    def update_one(cls, query: dict, update_data: dict) -> int:
        update_data['set__updated_at'] = datetime.utcnow()
        return cls.objects(**query).update_one(**update_data)

    @classmethod
    def update_many(cls, query: dict, update_data: dict) -> int:
        update_data['set__updated_at'] = datetime.utcnow()
        return cls.objects(**query).update(**update_data)

    def to_dict(self, exclude_fields: Optional[List] = []) -> Dict:

        if exclude_fields is None:
            exclude_fields = []
        if 'password' not in exclude_fields:
            exclude_fields.append('password')
            
        document = self.to_mongo().to_dict()
        document["id"] = document["_id"]
        del document["_id"]

        def convert_field(value):
            try:
                if isinstance(value, datetime):
                    return value.strftime("%a, %d %b %Y %H:%M:%S GMT")
                elif isinstance(value, ObjectId):
                    return str(value)
                elif isinstance(value, date):
                    return value.strftime("%a, %d %b %Y %H:%M:%S GMT")
                elif isinstance(value, me.EmbeddedDocument):  # Handling embedded documents
                    return value.to_dict()  # Assuming the embedded document also has a to_dict method
                elif isinstance(value, list):  # Handling list fields
                    return [convert_field(item) for item in value]
                elif isinstance(value, me.Document):  # Handling references to other documents
                    if hasattr(value, "to_dict") and callable(value.to_dict):
                        return value.to_dict()
                    return value
            except Exception as e:
                import traceback
                traceback.print_exc()
                return None  # Reference does not exist
            return value

        # Return a dictionary with converted fields
        return {field: convert_field(getattr(self, field, document[field])) for field in document.keys() if field not in exclude_fields}

    @classmethod
    def create(cls, *args, **kwargs) -> me.Document:
        # Create an instance of the document
        instance = cls(**kwargs)
        instance.save()
        return instance

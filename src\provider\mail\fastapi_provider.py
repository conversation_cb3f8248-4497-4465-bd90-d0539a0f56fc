from fastapi_mail import MessageSchema, ConnectionConfig, FastMail
from src.constants.env_constant import env_var
from jinja2 import Environment, FileSystemLoader

env = Environment(loader=FileSystemLoader("src/templates"))

class MailService:
    def __init__(self):
        self.conf = ConnectionConfig(
            MAIL_USERNAME=str(env_var["FASTAPI_MAIL_USERNAME"]),
            MAIL_PASSWORD=str(env_var["FASTAPI_MAIL_PASSWORD"]),
            MAIL_FROM=str(env_var["FASTAPI_MAIL_FROM"]),
            MAIL_PORT=587,
            MAIL_SERVER="smtp.gmail.com",
            MAIL_STARTTLS=True,   # Enable STARTTLS
            MAIL_SSL_TLS=False,   # Do not use SSL/TLS as we use STARTTLS
            USE_CREDENTIALS=True,
            VALIDATE_CERTS=True
        )

    async def send_mail(self, subject: str, recipients: list, template_name: str, context: dict) -> bool:
        """
        Sends an email with the specified subject, recipient list, and body.
        
        Args:
            subject (str): The subject of the email.
            recipients (list): A list of email addresses to send the email to.
            body (str): The body content of the email.
            body_type (str, optional): The MIME subtype for the email body (e.g., "html" or "plain").
                                       Defaults to "html".
        
        Returns:
            bool: True if the email was sent successfully, False otherwise.
        """
        template = env.get_template(template_name)
        html_content = template.render(**context)

        message = MessageSchema(
            subject=subject,
            recipients=recipients,
            body=html_content,
            subtype="html"
        )
        try:
            fm = FastMail(self.conf)
            await fm.send_message(message)
            return True
        except Exception as e:
            print(f"Failed to send email: {e}")
            return False

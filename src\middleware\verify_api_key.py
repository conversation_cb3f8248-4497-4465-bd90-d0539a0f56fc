from fastapi import Header, HTTPException, status, Depends
from fastapi.security import API<PERSON>eyHeader
from src.models.api<PERSON>ey import APIKey  # Assuming this is where the APIKey model is located

# Create an APIKeyHeader instance to retrieve the API key from headers
api_key_header = <PERSON>KeyHeader(name="Authorization", auto_error=True)

async def verify_api_key(x_api_key: str = Depends(api_key_header)):
    """
    Middleware to verify the API key from headers.
    """
    api_key_exist = APIKey.get(key=x_api_key)
    if not api_key_exist:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API Key",
        )
    return x_api_key

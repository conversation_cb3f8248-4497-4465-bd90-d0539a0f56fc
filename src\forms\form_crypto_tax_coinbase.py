from typing import List, Optional
from enum import Enum
from pydantic import BaseModel, Field
from src.forms.base_form import (
    EINField,
    TextField,
    YearField,
    CurrencyField,
    DateField,
    CheckboxField,
    FormDetails,
    FloatField,
    ZipCodeField
)


class TermType(str, Enum):
    SHORT_TERM = "short term"
    LONG_TERM = "long term"


class TaxSlotType(str, Enum):
    COVERED_TAX_SLOTS = "covered tax slots"
    NON_COVERED_TAX_SLOTS = "non-covered tax slots"


class PartType(str, Enum):
    PART_1 = "part 1"
    PART_2 = "part 2"
    PART_3 = "part 3"


class BoxType(str, Enum):
    BOX_A = "box A"
    BOX_B = "box B"
    BOX_C = "box C"
    BOX_D = "box D"
    BOX_E = "box E"
    BOX_F = "box F"

class SummaryRow(BaseModel):
    long_term: FloatField = Field(default_factory=lambda: FloatField(type="float"), description="Gain or Loss, along with - sign in case of loss.")
    short_term: FloatField = Field(default_factory=lambda: FloatField(type="float"), description="Gain or Loss, along with - sign in case of loss.")
    total_gain_loss: FloatField = Field(default_factory=lambda: FloatField(type="float"), description="Gain or Loss, along with - sign in case of loss.")

class Summary(BaseModel):
    coinbase: SummaryRow = Field(
        default_factory=SummaryRow)
    total: SummaryRow = Field(
        default_factory=SummaryRow)


class PayerInformation(BaseModel):
    payer_name: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_street_address: TextField = Field(
        default_factory=lambda: TextField(type="text"), description="The payer's street address as listed on the form without city, state, zipcode."
    )
    payer_state: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_city: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_zip_code: ZipCodeField = Field(
        default_factory=lambda: ZipCodeField(type="zipcode"))
    # telephone_number: ContactField = Field(
    #     default_factory=lambda: ContactField(type="contact"))
    tin: EINField = Field(default_factory=lambda: EINField(type="einfield"))
    rtn_optional: Optional[TextField] = Field(
        default_factory=lambda: TextField(type="text")
    )

class AcquiredData(BaseModel):
    date_sold: DateField = Field(default_factory=lambda: DateField(
        type="date"))
    
    amount: FloatField = Field(default_factory=lambda: FloatField(
        type="float"))
    
    proceeds: FloatField = Field(default_factory=lambda: FloatField(
        type="float"))
    
    cost_basis: FloatField = Field(default_factory=lambda: FloatField(
        type="float"))
    
    long_term: FloatField = Field(default_factory=lambda: FloatField(
        type="float"))
    
    short_term: FloatField = Field(default_factory=lambda: FloatField(
        type="float"))
    
class TransactionDetail(BaseModel):
    date_sold: DateField = Field(default_factory=lambda: DateField(
        type="date"))
    
    event: TextField = Field(default_factory=lambda: TextField(
        type="text"))
    
    amount: FloatField = Field(default_factory=lambda: FloatField(
        type="float"))
    
    proceeds: FloatField = Field(default_factory=lambda: FloatField(
        type="float"))
    
    cost_basis: FloatField = Field(default_factory=lambda: FloatField(
        type="float"))
    
    long_term: FloatField = Field(default_factory=lambda: FloatField(
        type="float"))
    
    short_term: FloatField = Field(default_factory=lambda: FloatField(
        type="float"))
    
    total_gain_loss: FloatField = Field(default_factory=lambda: FloatField(
        type="float"))

class TransactionDetail(BaseModel):
    date_sold: DateField = Field(default_factory=lambda: DateField(
        type="date"))
    
    event: TextField = Field(default_factory=lambda: TextField(
        type="text"))
    
    amount: FloatField = Field(default_factory=lambda: FloatField(
        type="float"))
    
    proceeds: FloatField = Field(default_factory=lambda: FloatField(
        type="float"))
    
    cost_basis: FloatField = Field(default_factory=lambda: FloatField(
        type="float"))
    
    long_term: FloatField = Field(default_factory=lambda: FloatField(
        type="float"))
    
    short_term: FloatField = Field(default_factory=lambda: FloatField(
        type="float"))
    
    total_gain_loss: FloatField = Field(default_factory=lambda: FloatField(
        type="float"))
    
    # date_acquired: List[AcquiredData] = Field(default_factory=list)

    

class FormCryptoTaxCoinbase(BaseModel):
    form_details: FormDetails = Field(default_factory=FormDetails)
    payer_information: PayerInformation = Field(
        default_factory=PayerInformation)
    transaction_details: List[TransactionDetail] = Field(default_factory=list)
    summary: Summary = Field(default_factory=Summary)

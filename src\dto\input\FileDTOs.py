from pydantic import BaseModel, Field
from typing import Optional, Dict, List

class FileCreateRequest(BaseModel):
    file_name: str = Field(..., description="Name of the file")
    input_file_path: str = Field(..., description="Path where the file will be uploaded")
    masked_file_path: str = Field(..., description="Path for the masked file")
    message: Optional[str] = Field(None, description="Optional message")
    client_id: str = Field(..., description="ID of the associated client")
    form_types_per_page: Optional[Dict[str, List[str]]] = Field(
        default=None, description="Mapping of page numbers to form types"
    )

class FileUpdateRequest(BaseModel):
    file_id: str = Field(..., description="ID of the file to update")
    file_name: Optional[str] = Field(None, description="Updated file name")
    input_file_path: Optional[str] = Field(None, description="Updated input file path")
    masked_file_path: Optional[str] = Field(None, description="Updated masked file path")
    message: Optional[str] = Field(None, description="Updated message")
    form_types_per_page: Optional[Dict[str, List[str]]] = Field(
        default=None, description="Updated mapping of page numbers to form types"
    )

class FileFallbackRequest(BaseModel):
    client_id: str = Field(..., description="Client Id of given file id")
    file_ids: List[str] = Field(..., description="List of file IDs to fallback")
    is_review_required: bool = Field(..., description="Flag to indicate if the client ocr should be reviewed and filled")

class FileFilterRequest(BaseModel):
    client_id: Optional[str] = Field(None, description="Filter by client ID")
    status: Optional[str] = Field(None, description="Filter by file status")
    file_name: Optional[str] = Field(None, description="Filter files by file name")

class PresignedUrlRequest(BaseModel):
    social_security_number: Optional[str] = Field(None, description="Social Security Number of the user")
    financial_year: int = Field(..., description="Financial year for the file")
    filenames: list[str] = Field(..., description="List of filenames to generate presigned URLs for")
    client_id: Optional[str] = Field(None, description="Client ID of the client requesting the presigned URL")
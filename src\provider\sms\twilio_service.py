from twilio.rest import Client
from src.constants.env_constant import env_var
from src.config.logger import logging

class SMSService:
    def __init__(self):
        try:
            self.client = Client(
                env_var["TWILIO_ACCOUNT_SID"],
                env_var["TWILIO_AUTH_TOKEN"]
            )
            self.from_number = str(env_var["TWILIO_PHONE_NUMBER"])
        except KeyError as e:
            logging.error(f"Missing Twilio env var: {e}")
            raise

    async def send_sms(self, to: str, body: str) -> bool:
        """
        Sends an SMS via Twilio.

        Args:
            to (str): Recipient phone number (E.164 format).
            body (str): Message content.

        Returns:
            bool: True if successfully queued, False otherwise.
        """
        try:
            message = self.client.messages.create(
                body=body,
                from_=self.from_number,
                to=to
            )
            logging.info(f"SMS queued: SID={message.sid}")
            return True
        except Exception as e:
            logging.error(f"Failed to send SMS to {to}: {e}")
            return False

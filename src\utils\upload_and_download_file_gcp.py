import json
import logging
import base64
import os

from cryptography.hazmat.primitives.ciphers.aead import AESGCM
from src.constants.env_constant import env_var
from google.cloud import storage

def initialize_storage_client() -> storage.Client:
    """Initializes and returns a Google Cloud Storage Client."""
    decoded_service_account = json.loads(
        base64.b64decode(env_var['GOOGLE_SERVICE_ACCOUNT_JSON_ENCODED'])
    )
    return storage.Client.from_service_account_info(decoded_service_account)


# key = AESGCM.generate_key(bit_length=256)
# print(base64.b64encode(key).decode())


key = base64.b64decode("AMXNZ30EIuEUmofPei78BkTLEPwNsnh1x5GACpe/qSY=") # encoded AESGCM key from above logic

def upload_file_to_bucket(file_path_with_ext: str, blob_path: str, bucket_name: str):
    try:
        storage_client = initialize_storage_client()
        bucket = storage_client.get_bucket(bucket_name)
        blob = bucket.blob(blob_path)

        blob.upload_from_filename(file_path_with_ext)

        logging.info(
            f"File '{file_path_with_ext}' successfully uploaded to '{blob_path}' in bucket '{bucket_name}'.")
    except Exception as e:
        logging.error(f"Error during uploading file to bucket {bucket_name}: {e}")


def upload_encrypted_file_to_bucket(file_path_with_ext: str, blob_path: str, bucket_name: str):
    try:
        with open(file_path_with_ext, "rb") as file:
            data = file.read()

        nonce = os.urandom(12)
        aesgcm = AESGCM(key)
        encrypted_data = aesgcm.encrypt(nonce, data, None)

        storage_client = initialize_storage_client()
        bucket = storage_client.get_bucket(bucket_name)
        blob = bucket.blob(blob_path)

        blob.metadata = {'nonce': base64.b64encode(nonce).decode('utf-8')}
        blob.upload_from_string(encrypted_data)

        logging.info(
            f"Encrypted file '{file_path_with_ext}' uploaded to '{blob_path}' in bucket '{bucket_name}'.")
    except Exception as e:
        logging.error(f"Error uploading encrypted file to bucket {bucket_name}: {e}")


def download_blob_as_string(blob_path: str, bucket_name: str) -> str:
    try:
        storage_client = initialize_storage_client()
        bucket = storage_client.get_bucket(bucket_name)
        blob = bucket.blob(blob_path)
        return blob.download_as_text()
    except Exception as e:
        logging.error(f"Failed to download file as string: {e}")
        return False


def download_encrypted_blob_as_string(blob_path: str, bucket_name: str) -> str:
    try:
        storage_client = initialize_storage_client()
        bucket = storage_client.get_bucket(bucket_name)
        blob = bucket.blob(blob_path)

        encrypted_data = blob.download_as_bytes()
        nonce_b64 = blob.metadata.get("nonce") if blob.metadata else None

        if nonce_b64:
            nonce = base64.b64decode(nonce_b64)
            aesgcm = AESGCM(key)
            decrypted_data = aesgcm.decrypt(nonce, encrypted_data, None)
            return decrypted_data.decode("utf-8")
        else:
            # File is not encrypted
            logging.warning(f"File '{blob_path}' is not encrypted, returning as plain text.")
            return encrypted_data.decode("utf-8")

    except Exception as e:
        logging.error(f"Failed to download or decrypt blob: {e}")
        return False



def download_blob_as_file(blob_path: str, bucket_name: str, local_path: str) -> bool:
    try:
        storage_client = initialize_storage_client()
        bucket = storage_client.bucket(bucket_name)
        blob = bucket.blob(blob_path)
        blob.download_to_filename(local_path)
        logging.info(f"Downloaded file from {blob_path} to {local_path}")
        return True
    except Exception as e:
        logging.error(f"Failed to download file: {e}")
        return False


def download_encrypted_blob_as_file(blob_path: str, bucket_name: str, local_path: str) -> bool:
    try:
        storage_client = initialize_storage_client()
        bucket = storage_client.get_bucket(bucket_name)
        blob = bucket.blob(blob_path)

        encrypted_data = blob.download_as_bytes()
        nonce_b64 = blob.metadata.get("nonce") if blob.metadata else None

        if nonce_b64:
            nonce = base64.b64decode(nonce_b64)
            aesgcm = AESGCM(key)
            decrypted_data = aesgcm.decrypt(nonce, encrypted_data, None)
        else:
            logging.warning(f"File '{blob_path}' is not encrypted, saving as is.")
            decrypted_data = encrypted_data

        with open(local_path, "wb") as file:
            file.write(decrypted_data)

        logging.info(f"Decrypted or plain file downloaded to {local_path}")
        return True
    except Exception as e:
        logging.error(f"Failed to download or decrypt file: {e}")
        return False


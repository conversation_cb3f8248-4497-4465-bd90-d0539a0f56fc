from src.dto.output.CommonResponseModel import CommonResponseModel
from src.models.base_model import BaseDocument
from datetime import datetime, timedelta
from src.constants.enum import OTPType
from src.utils.otp_utils import generate_otp as gen_otp, get_otp_expiry, OTP_EXPIRATION_TIME_IN_MINUTES
import mongoengine as me
import random


class OTP(BaseDocument):
    email = me.EmailField(required=True)
    otp = me.StringField(required=True)
    type = me.StringField(required=True, choices=[otp_type.value for otp_type in OTPType])
    expires_at = me.DateTimeField(required=True)

    meta = {"collection": "otps"}

    @staticmethod
    def generate_otp(email: str, otp_type: str, minutes: int = OTP_EXPIRATION_TIME_IN_MINUTES, digits: int = 6) -> str:
        otp_code = gen_otp(digits = digits)  # Generate a 6-character OTP
        expires_at = get_otp_expiry(minutes=minutes)  # OTP valid for given minutes

        # Check if an OTP entry already exists for the email
        otp_entry = OTP.objects(email=email, type=otp_type).first()

        if otp_entry:
            # Update the existing OTP object
            otp_entry.otp = otp_code
            otp_entry.type = otp_type
            otp_entry.expires_at = expires_at
        else:
            # Create a new OTP object
            otp_entry = OTP(email=email, otp=otp_code, expires_at=expires_at, type=otp_type)

        otp_entry.save()
        return otp_code

    @staticmethod
    def verify_otp(email: str, otp_code: str, otp_type: str) -> bool:

        # Validate the OTP existence
        if not OTP.get(email=email, type=otp_type):
            response = CommonResponseModel(
                success=False, message=f"No OTP request found. Please initiate {otp_type}.")
            return response, False
        
        # Validate the OTP and its expiry
        if not OTP.get(email=email, otp=otp_code, type=otp_type):
            response = CommonResponseModel(
                success=False, message="Invalid OTP provided.")
            return response, False

        otp = OTP.get(email=email, otp=otp_code, type=otp_type)

        if datetime.utcnow() > otp.expires_at:
            response = CommonResponseModel(
                success=False, message="OTP has expired. Please request a new one.")
            return response, False
        
        response = CommonResponseModel(success=True, message="OTP verified successfully.")
    
        return response , True
    
    @staticmethod
    def verify_otp_and_delete(email: str, otp_code: str, otp_type: str) -> bool:

        # Validate the OTP existence
        if not OTP.get(email=email, type=otp_type):
            response = CommonResponseModel(
                success=False, message=f"No OTP request found. Please initiate {otp_type}.")
            return response, False
        
        # Validate the OTP and its expiry
        if not OTP.get(email=email, otp=otp_code, type=otp_type):
            response = CommonResponseModel(
                success=False, message="Invalid OTP provided.")
            return response, False

        otp = OTP.get(email=email, otp=otp_code, type=otp_type)

        if datetime.utcnow() > otp.expires_at:
            response = CommonResponseModel(
                success=False, message="OTP has expired. Please request a new one.")
            return response, False

        otp.delete()  # Delete OTP after verification
        
        response = CommonResponseModel(success=True, message="OTP verified successfully.")
    
        return response , True

from pydantic import BaseModel, Field
from typing import List
from src.forms.base_form import TextField, CurrencyField, DateField, CheckboxField, FormDetails

class RecipientInformation(BaseModel):
    recipient_marketplace_identifier: TextField = Field(default_factory=lambda: TextField(sequence="1", type="text"))
    recipient_marketplace_assigned_policy_number: TextField = Field(default_factory=lambda: TextField(sequence="2", type="text"))
    recipient_policy_issuer_name: TextField = Field(default_factory=lambda: TextField(sequence="3", type="text"))
    recipient_policy_start_date: DateField = Field(default_factory=lambda: DateField(sequence="10", type="date"))
    recipient_policy_termination_date: DateField = Field(default_factory=lambda: DateField(sequence="11", type="date"))

class CoveredIndividuals(BaseModel):
    covered_individual_name: TextField = Field(default_factory=lambda: TextField(type="text"))
    covered_individual_date_of_birth: DateField = Field(default_factory=lambda: DateField(type="date"))
    coverage_start_date: DateField = Field(default_factory=lambda: DateField(type="date"))
    coverage_termination_date: DateField = Field(default_factory=lambda: DateField(type="date"))

class MonthlyCoverageFields(BaseModel):
    monthly_enrollment_premiums: CurrencyField = Field(default_factory=lambda: CurrencyField(type="currency"))
    monthly_second_lowest_cost_silver_plan_slcsp_premiums: CurrencyField = Field(default_factory=lambda: CurrencyField(type="currency"))
    monthly_advance_payment_of_premium_tax_credit: CurrencyField = Field(default_factory=lambda: CurrencyField(type="currency"))

class CoverageByMonth(BaseModel):
    january: MonthlyCoverageFields = Field(default_factory=MonthlyCoverageFields)
    february: MonthlyCoverageFields = Field(default_factory=MonthlyCoverageFields)
    march: MonthlyCoverageFields = Field(default_factory=MonthlyCoverageFields)
    april: MonthlyCoverageFields = Field(default_factory=MonthlyCoverageFields)
    may: MonthlyCoverageFields = Field(default_factory=MonthlyCoverageFields)
    june: MonthlyCoverageFields = Field(default_factory=MonthlyCoverageFields)
    july: MonthlyCoverageFields = Field(default_factory=MonthlyCoverageFields)
    august: MonthlyCoverageFields = Field(default_factory=MonthlyCoverageFields)
    september: MonthlyCoverageFields = Field(default_factory=MonthlyCoverageFields)
    october: MonthlyCoverageFields = Field(default_factory=MonthlyCoverageFields)
    november: MonthlyCoverageFields = Field(default_factory=MonthlyCoverageFields)
    december: MonthlyCoverageFields = Field(default_factory=MonthlyCoverageFields)
    annual_totals: MonthlyCoverageFields = Field(default_factory=MonthlyCoverageFields)

class CoverageInformation(BaseModel):
    monthly_coverage: CoverageByMonth = Field(default_factory=CoverageByMonth)

class AdditionalFields(BaseModel):
    corrected: CheckboxField = Field(default_factory=lambda: CheckboxField(sequence="CORRECTED", type="checkbox"))
    void: CheckboxField = Field(default_factory=lambda: CheckboxField(sequence="VOID", type="checkbox"))

# Main Pydantic model for Form 1095-A
class Form1095A(BaseModel):
    recipient_information: RecipientInformation = Field(default_factory=RecipientInformation)
    form_details: FormDetails = Field(default_factory=FormDetails)
    covered_individuals: List[CoveredIndividuals] = Field(default_factory=list)
    coverage_information: CoverageInformation = Field(default_factory=CoverageInformation)
    additional_fields: AdditionalFields = Field(default_factory=AdditionalFields)

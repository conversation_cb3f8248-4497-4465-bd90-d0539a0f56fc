from src.provider.mail.fastapi_provider import MailService  # Import the MailService class for email functionalities
from src.provider.mail.postmark_provider import PostmarkMailService  # Import the MailService class for email functionalities

# Initialize the Mail provider with configuration details
mail_configuration = {
    "fastapi": {
        "provider": MailService()
    },
    "postmark":{
        "provider": PostmarkMailService() 
    }
}
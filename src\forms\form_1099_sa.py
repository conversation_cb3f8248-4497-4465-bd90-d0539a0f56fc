from enum import Enum
from pydantic import BaseModel, Field
from typing import List, Optional
from src.forms.base_form import TextField, YearField, CurrencyField, CheckboxField, FormDetails, ZipCodeField, EINField, DateField

class PlanTypeEnum(str, Enum):
    hsa = "hsa"
    archer_msa = "archer_msa"
    ma_msa = "ma_msa"

class TypeOfPlan(BaseModel):
    plan_type: PlanTypeEnum = Field(...)
    
class TrusteeOrPayerInformation(BaseModel):
    trustee_or_payer_name: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    trustee_or_payer_street_address: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    trustee_or_payer_city: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    trustee_or_payer_state: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    trustee_or_payer_zip_code: ZipCodeField = Field(
        default_factory=lambda: ZipCodeField(type="zipcode"))
    trustee_or_payer_tin_or_federal_dentification_number: EINField = Field(default_factory=lambda: EINField(type="einfield"))

class FinancialFields(BaseModel):
    
    gross_distributions: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    earnings_on_excess_cont: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    distribution_code: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    fmv_on_date_of_death: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    type_of_plan_or_account: TypeOfPlan = Field(default_factory=TypeOfPlan)

    # account_type :TextField = Field(
    #     default_factory=lambda: TextField(type="text"))


class AdditionalFields(BaseModel):
    corrected: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    omb_number: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    calendar_year: YearField = Field(
        default_factory=lambda: YearField(type="year"))


# Main Pydantic model for Form 1099 SA
class Form1099SA(BaseModel):
    trustee_or_payer_information: TrusteeOrPayerInformation = Field(
        default_factory=TrusteeOrPayerInformation)
    form_details: FormDetails = Field(default_factory=FormDetails)
    financial_fields: FinancialFields = Field(default_factory=FinancialFields)
    additional_fields: AdditionalFields = Field(
        default_factory=AdditionalFields)

from typing import Optional
from src.models.users import User
from src.models.clients import Client
from src.constants.enum import QueueProcessingStatus
from src.models.base_model import BaseDocument
from bson import ObjectId
import mongoengine as me


class File(BaseDocument):
    file_name = me.StringField(required=True)
    input_file_path = me.StringField(required=True)
    masked_file_path = me.StringField(required=True)
    message = me.StringField(required=False)
    status = me.StringField(
        choices=[status.value for status in QueueProcessingStatus],
        default=QueueProcessingStatus.QUEUED.value
    )
    # Reference to the Client model
    client = me.ReferenceField(Client, reverse_delete_rule=me.CASCADE, required=True)

    # New field to store form types per page
    form_types_per_page = me.DictField(
        required=False,
        default=None,
        help_text="Dictionary mapping page numbers to a list of form types. Example: {1: ['Form A', 'Form B'], 2: ['Form C']}"
    )

    # Maintain user who updated the document last
    last_updated_by = me.<PERSON><PERSON><PERSON>(User, required=False)

    def to_dict(self, exclude_fields: Optional[list] = []) -> dict:
        document = super().to_dict(exclude_fields)
        document["status"] = self.status
        # Include client ID in output
        document["client_id"] = str(self.client["id"]) if self.client else None
        # Include form types per page in output
        document["form_types_per_page"] = self.form_types_per_page
        return document

    meta = {
        'collection': 'files'
    }

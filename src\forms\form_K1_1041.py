from pydantic import BaseModel, Field
from src.forms.base_form import CheckboxField, TextField, CurrencyField, FormDetails, YearField, ZipCodeField, EINField
from typing import List


class EstateInformation(BaseModel):
    estate_ein: EINField = Field(default_factory=lambda: EINField(type="einfield"))
    estate_name: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    estate_address: TextField = Field(
        default_factory=lambda: TextField(type="text"), description="The estate's street address as listed on the form without city, state, zipcode.")
    estate_city: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    estate_state: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    estate_zip: ZipCodeField = Field(
        default_factory=lambda: ZipCodeField(type="zipcode"))
    check_if_Form_1041_T_was_filed_and_enter_the_date_it_was_filed: CheckboxField = Field(
        default_factory=lambda: CheckboxField(sequence="D", type="checkbox"))
    check_if_this_is_the_final_Form_1041_for_the_estate_or_trust: CheckboxField = Field(
        default_factory=lambda: CheckboxField(sequence="E", type="checkbox"))


class DirectlyApportionedDeductions(BaseModel):
    directly_apportioned_deductions_code: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    directly_apportioned_deductions_federal_amount: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))


class FinalYearDeductions(BaseModel):
    final_year_deductions_code: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    final_year_deductions_federal_amount: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))


class AlternativeMinimumTaxAdjustment(BaseModel):
    alternative_minimum_tax_adjustment_code: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    alternative_minimum_tax_adjustment_federal_amount: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))


class CreditsAndCreditRecapture(BaseModel):
    credits_and_credit_recapture_code: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    credits_and_credit_recapture_federal_amount: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))


class OtherInformation(BaseModel):
    other_information_code: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    other_information_federal_amount: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))


class BeneficiaryInformation(BaseModel):
    domestic_beneficiary: CheckboxField = Field(
        default_factory=lambda: CheckboxField(sequence="H", type="checkbox"))
    foreign_beneficiary: CheckboxField = Field(
        default_factory=lambda: CheckboxField(sequence="I", type="checkbox"))


class IncomeDeductionsCredits(BaseModel):
    interest_income: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="1", type="currency"))
    ordinary_dividends: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="2a", type="currency"))
    qualified_dividends: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="2b", type="currency"))
    net_short_term_capital_gain: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="3a", type="currency"))
    net_long_term_capital_gain: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="3b", type="currency"))
    percent_28_rate_gain: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="3c", type="currency"))
    unrecaptured_section_1250_gain: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="3d", type="currency"))
    other_portfolio_and_nonbusiness_income: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="4", type="currency"))
    ordinary_business_income: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="5", type="currency"))
    net_rental_real_estate_income: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="6", type="currency"))
    other_rental_income: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="7", type="currency"))
    directly_apportioned_deductions: List[DirectlyApportionedDeductions] = Field(
        default_factory=list)
    estate_tax_deduction: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="8", type="currency"))
    final_year_deductions: List[FinalYearDeductions] = Field(
        default_factory=list)
    alternative_minimum_tax_adjustment: List[AlternativeMinimumTaxAdjustment] = Field(
        default_factory=list)
    credits_and_credit_recapture: List[CreditsAndCreditRecapture] = Field(
        default_factory=list)
    other_information: List[OtherInformation] = Field(default_factory=list)

class AdditionalFields(BaseModel):
    period_begin: YearField = Field(
        default_factory=lambda: YearField(type="year"))
    period_end: YearField = Field(
        default_factory=lambda: YearField(type="year"))
    check_final_k1: CheckboxField = Field(
        default_factory=lambda: CheckboxField(sequence="Final K-1", type="checkbox"))
    check_amended_k1: CheckboxField = Field(
        default_factory=lambda: CheckboxField(sequence="Amended K-1", type="checkbox"))

class FormK1_1041(BaseModel):
    form_details: FormDetails = Field(default_factory=FormDetails)
    additional_fields: AdditionalFields = Field(default_factory=AdditionalFields)
    estate_information: EstateInformation = Field(
        default_factory=EstateInformation)
    beneficiary_information: BeneficiaryInformation = Field(
        default_factory=BeneficiaryInformation)
    income_deductions_credits: IncomeDeductionsCredits = Field(
        default_factory=IncomeDeductionsCredits)

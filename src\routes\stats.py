from typing import Optional
from fastapi import APIRouter, Depends, status as HTTPStatus
from fastapi.responses import JSONResponse
from datetime import datetime, timedelta
from bson import ObjectId
import logging

from src.middleware.verify_api_key import verify_api_key
from src.config.form_config import NON_IMPORTANT_FORM_TYPES
from src.dto.input.StatsDTOs import ClientStatsRequest, FileStatsRequest, FormStatsRequest, PageStatsRequest
from src.dto.output.CommonResponseModel import CommonResponseModel
from collections import defaultdict
from src.models.clients import Client
from src.models.files import File
from src.models.pages import Page
from src.auth.auth_handler import user_jwt_required
from src.constants.enum import FormType, Tag, QueueProcessingStatus, DrakeProcessingStatus, UserRole

router = APIRouter(prefix="/v1/stats", tags=[Tag.STATS.value])

# Helper for status counts
def count_status(model, query, status_field="queue_status", check_drake_status=True):
    total = model.objects(**query).count()
    queue_breakdown = {status.value: model.objects(**{**query, status_field: status.value}).count() for status in QueueProcessingStatus}
    
    if not check_drake_status:
        return {"total": total, "queue_status": queue_breakdown}
    else:
        # Include drake status breakdown if applicable
        drake_breakdown = {status.value: model.objects(**{**query, "drake_status": status.value}).count() for status in DrakeProcessingStatus}
        return {"total": total, "queue_status": queue_breakdown, "drake_status": drake_breakdown}

@router.get("/clients", summary="Get global and user-specific client stats")
async def get_client_stats(filters: ClientStatsRequest = Depends(), current_user: dict = Depends(user_jwt_required)):
    try:
        base_query = {}
        if filters.financial_year:
            base_query["financial_year"] = filters.financial_year
        if filters.from_date and filters.to_date:
            base_query["created_at__gte"] = filters.from_date
            base_query["created_at__lte"] = filters.to_date
        else:
            # If from_date not provided, use last 24hrs UTC time
            now = datetime.utcnow()
            base_query["created_at__gte"] = now - timedelta(days=1)
            base_query["created_at__lte"] = now

        global_stats = count_status(Client, base_query)
        user_query = {**base_query, "user": ObjectId(current_user["id"])}
        user_stats = count_status(Client, user_query)

        processing_clients = Client.objects(**{**user_query, "queue_status": QueueProcessingStatus.PROCESSING.value})
        processing_list = [c.to_dict() for c in processing_clients]

        return JSONResponse(status_code=HTTPStatus.HTTP_200_OK, content=CommonResponseModel(success=True, message="Client stats retrieved", data={"global": global_stats, "user": user_stats, "processing_clients": processing_list}).dict())
    except Exception as e:
        logging.error(f"Error retrieving client stats: {e}")
        response = CommonResponseModel(success=False, message="Error retrieving client stats", data={})
        return JSONResponse(status_code=HTTPStatus.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())

from datetime import datetime, timedelta
from fastapi import Query

@router.get("/platform", summary="Get overall platform file stats (last 24h or custom date range)")
async def get_platform_file_stats(
    current_user: dict = Depends(user_jwt_required),
    start_date: Optional[datetime] = Query(None, description="Start date for filtering file uploads"),
    end_date: Optional[datetime] = Query(None, description="End date for filtering file uploads")
):
    try:
        if UserRole.ADMIN.value not in current_user["roles"]:
            return JSONResponse(
                status_code=HTTPStatus.HTTP_403_FORBIDDEN,
                content=CommonResponseModel(success=False, message="Only admins can access this route", data={}).dict()
            )
    
        if end_date and end_date.date() == datetime.utcnow().date():
            end_date = datetime.utcnow()
            if start_date and start_date.date() == end_date.date() - timedelta(days=1):
                start_date = end_date - timedelta(days=1)

        # Default to last 24 hours if no dates are provided
        if not start_date and not end_date:
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=1)

        print(start_date, end_date)

        # Build time-filter query
        file_query = {}
        if start_date:
            file_query["created_at"] = {"$gte": start_date}
        if end_date:
            file_query.setdefault("created_at", {})["$lte"] = end_date

        all_files = File.objects(__raw__=file_query).only("id")
        file_ids = list(all_files.scalar("id"))
        total_files_uploaded = len(file_ids)

        if not file_ids:
            return JSONResponse(
                status_code=HTTPStatus.HTTP_200_OK,
                content=CommonResponseModel(success=True, message="No files found in given time range", data={"stats": {}}).dict()
            )

        pages_cursor = Page.objects(__raw__={"file": {"$in": file_ids}}).only("form_type", "status").as_pymongo()

        valid_pages = 0
        error_count = 0
        unknown_count = 0
        form_count = 0

        identified_form_counts = defaultdict(int)
        error_form_counts = defaultdict(int)
        unknown_form_counts = defaultdict(int)

        for page in pages_cursor:
            form_types = page.get("form_type", [])
            status = page.get("status", "")

            valid_forms = [f for f in form_types if f not in NON_IMPORTANT_FORM_TYPES]
            instruction_only = all(f in NON_IMPORTANT_FORM_TYPES for f in form_types)

            if instruction_only and len(form_types) == 1:
                continue

            if not valid_forms:
                unknown_count += 1
                for f in form_types:
                    unknown_form_counts[f] += 1
                continue

            if status == QueueProcessingStatus.ERROR.value:
                error_count += 1
                for f in valid_forms:
                    error_form_counts[f] += 1
                continue

            valid_pages += 1
            for f in valid_forms:
                identified_form_counts[f] += 1
                form_count += 1

        stats = {
            "files_uploaded": total_files_uploaded,
            "valid_pages_processed": valid_pages,
            "total_error_pages": error_count,
            "total_form_count": form_count,
            "identified_forms": dict(identified_form_counts),
            "error_forms": dict(error_form_counts),
            "unknown_forms": dict(unknown_form_counts),
            "unknown_page_count": unknown_count,
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat()
        }

        return JSONResponse(
            status_code=HTTPStatus.HTTP_200_OK,
            content=CommonResponseModel(success=True, message="Platform file stats retrieved", data={"stats": stats}).dict()
        )

    except Exception as e:
        logging.error(f"Error retrieving platform file stats: {e}")
        return JSONResponse(
            status_code=HTTPStatus.HTTP_500_INTERNAL_SERVER_ERROR,
            content=CommonResponseModel(success=False, message="Error retrieving platform file stats", data={}).dict()
        )


@router.get("/files", summary="Get client specific file stats")
async def get_file_stats(filters: FileStatsRequest = Depends(), current_user: dict = Depends(user_jwt_required)):
    try:
        base_query = {}
        user_clients = [str(c.id) for c in Client.objects(user=ObjectId(current_user["id"]))]

        if UserRole.ADMIN.value in current_user["roles"]:
            if filters.client_id:
                client_id = ObjectId(filters.client_id)
            else:
                return JSONResponse(
                    status_code=HTTPStatus.HTTP_400_BAD_REQUEST,
                    content=CommonResponseModel(success=False, message="Client ID is required", data={}).dict()
                )
        else:
            if filters.client_id not in user_clients:
                return JSONResponse(
                    status_code=HTTPStatus.HTTP_400_BAD_REQUEST,
                    content=CommonResponseModel(success=False, message="Client ID not found in user's clients", data={}).dict()
                )
            client_id = ObjectId(filters.client_id)

        # Step 1: Get all file IDs for the client
        file_ids = list(File.objects(client=client_id).only("id").scalar("id"))

        if not file_ids:
            return JSONResponse(
                status_code=HTTPStatus.HTTP_200_OK,
                content=CommonResponseModel(success=True, message="No files found", data={"stats": {}}).dict()
            )

        # Step 2: Query pages with those file IDs
        page_query = {"file": {"$in": file_ids}}
        pages_cursor = Page.objects(__raw__=page_query).only("form_type", "status").as_pymongo()

        identified_count = 0
        error_count = 0
        unknown_count = 0

        identified_form_counts = defaultdict(int)
        error_form_counts = defaultdict(int)
        unknown_form_counts = defaultdict(int)

        for page in pages_cursor:
            form_types = page.get("form_type", [])
            status = page.get("status", "")

            for form in form_types:
                if form in NON_IMPORTANT_FORM_TYPES and len(form_types) == 1:
                    # If the form is in NON_IMPORTANT_FORM_TYPES and it's the only form type, count it as unknown
                    unknown_count += 1
                    unknown_form_counts[form] += 1
                elif form not in NON_IMPORTANT_FORM_TYPES:
                    identified_count += 1
                    identified_form_counts[form] += 1
                    if status == QueueProcessingStatus.ERROR.value:
                        error_count += 1
                        error_form_counts[form] += 1

        stats = {
            "identified": {
                "total": identified_count,
                "form_counts": dict(identified_form_counts)
            },
            "error": {
                "total": error_count,
                "form_counts": dict(error_form_counts)
            },
            "unknown": {
                "total": unknown_count,
                "form_counts": dict(unknown_form_counts)
            }
        }

        return JSONResponse(
            status_code=HTTPStatus.HTTP_200_OK,
            content=CommonResponseModel(success=True, message="File stats retrieved", data={"stats": stats}).dict()
        )

    except Exception as e:
        logging.error(f"Error retrieving file stats: {e}")
        return JSONResponse(
            status_code=HTTPStatus.HTTP_500_INTERNAL_SERVER_ERROR,
            content=CommonResponseModel(success=False, message="Error retrieving file stats", data={}).dict()
        )
    

@router.get("/pages", summary="Get file specific page stats")
async def get_page_stats(filters: PageStatsRequest = Depends(), current_user: dict = Depends(user_jwt_required)):
    try:
        base_query = {}
        user_files = [f.id for f in File.objects(client__in=[c.id for c in Client.objects(user=ObjectId(current_user["id"]))])]

        if UserRole.ADMIN.value in current_user["roles"]:
            if filters.file_id:
                base_query["file"] = ObjectId(filters.file_id)
        else:
            if filters.file_id and ObjectId(filters.file_id) not in user_files:
                return JSONResponse(
                    status_code=HTTPStatus.HTTP_400_BAD_REQUEST,
                    content=CommonResponseModel(success=False, message="File ID not found in user's files", data={}).dict()
                )
            if filters.file_id:
                base_query["file"] = ObjectId(filters.file_id)
            else:
                base_query["file__in"] = user_files

        # Fetch only required fields using raw query for speed
        pages_cursor = Page.objects(**base_query).only("form_type", "status").as_pymongo()

        identified_count = 0
        error_count = 0
        unknown_count = 0

        identified_form_counts = defaultdict(int)
        error_form_counts = defaultdict(int)
        unknown_form_counts = defaultdict(int)

        for page in pages_cursor:
            form_types = page.get("form_type", [])
            status = page.get("status", "")

            for form in form_types:
                if form in NON_IMPORTANT_FORM_TYPES and len(form_types) == 1:
                    # If the form is in NON_IMPORTANT_FORM_TYPES and it's the only form type, count it as unknown
                    unknown_count += 1
                    unknown_form_counts[form] += 1
                elif form not in NON_IMPORTANT_FORM_TYPES:
                    identified_count += 1
                    identified_form_counts[form] += 1
                    if status == QueueProcessingStatus.ERROR.value:
                        error_count += 1
                        error_form_counts[form] += 1

        stats = {
            "identified": {
                "total": identified_count,
                "form_counts": dict(identified_form_counts)
            },
            "error": {
                "total": error_count,
                "form_counts": dict(error_form_counts)
            },
            "unknown": {
                "total": unknown_count,
                "form_counts": dict(unknown_form_counts)
            }
        }

        return JSONResponse(
            status_code=HTTPStatus.HTTP_200_OK,
            content=CommonResponseModel(success=True, message="Page stats retrieved", data={"stats": stats}).dict()
        )

    except Exception as e:
        logging.error(f"Error retrieving page stats: {e}")
        return JSONResponse(
            status_code=HTTPStatus.HTTP_500_INTERNAL_SERVER_ERROR,
            content=CommonResponseModel(success=False, message="Error retrieving page stats", data={}).dict()
        )
    

@router.get("/form_status", summary="Get client specific form filled status stats")
async def get_form_status_stats(filters: FormStatsRequest = Depends(), current_user: dict = Depends(user_jwt_required)):
    try:
        base_query = {}
        user_clients = [str(c.id) for c in Client.objects(user=ObjectId(current_user["id"]))]

        if UserRole.ADMIN.value in current_user["roles"]:
            if filters.client_id:
                client_id = ObjectId(filters.client_id)
            else:
                return JSONResponse(
                    status_code=HTTPStatus.HTTP_400_BAD_REQUEST,
                    content=CommonResponseModel(success=False, message="Client ID is required", data={}).dict()
                )
        else:
            if filters.client_id not in user_clients:
                return JSONResponse(
                    status_code=HTTPStatus.HTTP_400_BAD_REQUEST,
                    content=CommonResponseModel(success=False, message="Client ID not found in user's clients", data={}).dict()
                )
            client_id = ObjectId(filters.client_id)

        # Step 1: Get all file IDs for the client
        file_ids = list(File.objects(client=client_id).only("id").scalar("id"))

        if not file_ids:
            return JSONResponse(
                status_code=HTTPStatus.HTTP_200_OK,
                content=CommonResponseModel(success=True, message="No files found", data={"stats": {}}).dict()
            )

        # Step 2: Query pages with those file IDs
        page_query = {"file": {"$in": file_ids}}
        pages_cursor = Page.objects(__raw__=page_query).only("id","forms_status", "form_type").as_pymongo()

        form_status_stats = {}

        for page in pages_cursor:
            forms_status = page.get("forms_status", {})
            page_id = str(page.get("_id", ""))
            if 'form_type' in page and not (FormType.INSTRUCTIONS.value in page.get("form_type", []) or FormType.UNKNOWN.value in page.get("form_type", [])):
                if forms_status:
                    if page_id not in form_status_stats:
                        form_status_stats[page_id] = {}
                    for form_id, form_status in forms_status.items():
                        form_status_stats[page_id][form_id] = form_status

        return JSONResponse(
            status_code=HTTPStatus.HTTP_200_OK,
            content=CommonResponseModel(success=True, message="Form filled status stats retrieved", data={"stats": form_status_stats}).dict()
        )

    except Exception as e:
        logging.error(f"Error retrieving form filled status stats: {e}")
        return JSONResponse(
            status_code=HTTPStatus.HTTP_500_INTERNAL_SERVER_ERROR,
            content=CommonResponseModel(success=False, message="Error retrieving form filled status stats", data={}).dict()
        )
    

@router.get("/form_fill_status", summary="Get client specific form filled status stats")
async def get_form_fill_status_stats(filters: FormStatsRequest = Depends(), current_user: dict = Depends(verify_api_key)):
    try:
        client_id = ObjectId(filters.client_id)

        # Step 1: Get all file IDs for the client
        file_ids = list(File.objects(client=client_id).only("id").scalar("id"))

        if not file_ids:
            return JSONResponse(
                status_code=HTTPStatus.HTTP_200_OK,
                content=CommonResponseModel(success=True, message="No files found", data={"stats": {}}).dict()
            )

        # Step 2: Query pages with those file IDs
        page_query = {"file": {"$in": file_ids}}
        pages_cursor = Page.objects(__raw__=page_query).only("id","forms_status", "form_type").as_pymongo()

        form_status_stats = {}

        for page in pages_cursor:
            forms_status = page.get("forms_status", {})
            page_id = str(page.get("_id", ""))
            if 'form_type' in page and not (FormType.INSTRUCTIONS.value in page.get("form_type", []) or FormType.UNKNOWN.value in page.get("form_type", [])):
                if page_id not in form_status_stats:
                    form_status_stats[page_id] = {}
                if forms_status:
                    for form_id, form_status in forms_status.items():
                        form_status_stats[page_id][form_id] = form_status

        return JSONResponse(
            status_code=HTTPStatus.HTTP_200_OK,
            content=CommonResponseModel(success=True, message="Form filled status stats retrieved", data={"stats": form_status_stats}).dict()
        )

    except Exception as e:
        logging.error(f"Error retrieving form filled status stats: {e}")
        return JSONResponse(
            status_code=HTTPStatus.HTTP_500_INTERNAL_SERVER_ERROR,
            content=CommonResponseModel(success=False, message="Error retrieving form filled status stats", data={}).dict()
        )


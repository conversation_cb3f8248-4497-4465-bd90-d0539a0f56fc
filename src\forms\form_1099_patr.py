from pydantic import BaseModel, Field
from src.forms.base_form import TextField, DateField, CurrencyField, CheckboxField, FormDetails, ZipCodeField, EINField


class PayerInformation(BaseModel):
    payer_name: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_street_address: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_city: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_state: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_zip_code: ZipCodeField = Field(
        default_factory=lambda: ZipCodeField(type="zipcode"))
    payer_tin: EINField = Field(default_factory=lambda: EINField(type="einfield"))


class FinancialFields(BaseModel):
    patronage_dividends: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    non_patronage_distributions: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    per_unit_retain_allocations: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    federal_income_tax_withheld: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    redeemed_non_qualified_notices: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    section_199a_g_deductions: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    qualified_payments_section_199a_b_7: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    section_199a_qualified_items: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    section_199a_sstb_items: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    investment_credit: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    work_opportunity_credit: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    other_credits_and_deductions: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    
class AdditionalFields(BaseModel):
    corrected: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    specified_coop: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    omb_number: TextField = Field(
        default_factory=lambda: TextField(type="text"))


# Main Pydantic model for Form 1099-PATR
class Form1099PATR(BaseModel):
    payer_information: PayerInformation = Field(
        default_factory=PayerInformation)
    form_details: FormDetails = Field(default_factory=FormDetails)
    financial_fields: FinancialFields = Field(default_factory=FinancialFields)
    additional_fields: AdditionalFields = Field(
        default_factory=AdditionalFields)

from pydantic import BaseModel, Field
from typing import Any, Dict, Optional, List

from src.constants.enum import UpdateClientJsonType

class ExchangeTokenRequest(BaseModel):
    authorization_code: str

class RefreshTokenRequest(BaseModel):
    refresh_token: str

class ClientUpdateRequest(BaseModel):
    client_id: str = Field(..., description="ID of the client to update")
    name: Optional[str] = Field(None, description="Updated name")
    message: Optional[str] = Field(None, description="Updated message")

class ClientUpdateDrakeStatusRequest(BaseModel):
    client_id: str = Field(..., description="ID of the client to update")
    drake_status: Optional[str] = Field(None, description="Updated drake status")

class ClientUpdateIsReviewedRequest(BaseModel):
    is_reviewed: bool = Field(..., description="Flag to indicate if the client ocr is reviewed")

class ClientFilterRequest(BaseModel):
    financial_year: Optional[int] = Field(None, description="Filter clients by financial year")
    queue_status: Optional[str] = Field(None, description="Filter clients by queue status")
    name: Optional[str] = Field(None, description="Filter clients by name")

class ClientListResponse(BaseModel):
    clients: List[dict]
    total_clients: int
    current_page: int
    total_pages: int

class FileFormTypeInfo(BaseModel):
    filename: str = Field(..., description="Name of the file")
    original_filename: Optional[str] = Field(None, description="Original name of the file")
    form_types_per_page: Dict[int, List[str]] = Field(..., description="Mapping of page numbers to form types")  # Maps page numbers to a list of form types

class DocumentUploadRequest(BaseModel):
    filedatas: List[FileFormTypeInfo]  = Field(..., description="List of file data with form type information")
    name: str = Field(..., description="Name of the client")
    social_security_number: Optional[str] = Field(None, description="Client's Social Security Number")
    financial_year: int = Field(..., description="Financial year for the client")
    client_id: Optional[str] = Field(None, description="ID of the associated client")
    is_review_req : bool = Field(False, description="Flag indicating if the document requires review before sending to drake")
    is_priority : bool = Field(False, description="Flag indicating if the document is priority")

class ClientJsonUpdateRequest(BaseModel):
    form_type: str = Field(..., description="Type of form")
    arr_index: int = Field(..., description="Index of the form in the array")
    document_id: str = Field(..., description="ID of the document")
    modified_json: Dict[str, Any]  = Field(..., description="Modified JSON data")
    update_type: str = Field(..., description="Type of update")
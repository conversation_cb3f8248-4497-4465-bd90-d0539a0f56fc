from cryptography.hazmat.primitives import padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
from dotenv import load_dotenv
import base64
import os

load_dotenv(override=True)

# Load these securely from env
AES_KEY = os.getenv("AES_KEY", "THIS_IS_32_BYTE_KEY_1234567890123").encode()[:32]  # 32 bytes for AES-256
AES_IV = os.getenv("AES_IV", "STATIC_16_BYTE_IV_").encode()[:16]  # 16 bytes
ENCRYPTION_PREFIX = "ENC::"

def deterministic_encrypt(plain_text: str) -> str:
    if plain_text.startswith(ENCRYPTION_PREFIX):
        return plain_text  # Already encrypted, skip

    padder = padding.PKCS7(128).padder()
    padded_data = padder.update(plain_text.encode()) + padder.finalize()

    cipher = Cipher(algorithms.AES(AES_KEY), modes.CBC(AES_IV), backend=default_backend())
    encryptor = cipher.encryptor()
    encrypted = encryptor.update(padded_data) + encryptor.finalize()

    encoded = base64.urlsafe_b64encode(encrypted).decode()
    return ENCRYPTION_PREFIX + encoded

def deterministic_decrypt(cipher_text: str) -> str:
    if not cipher_text.startswith(ENCRYPTION_PREFIX):
        raise ValueError("Input is not encrypted or missing required prefix.")

    encoded = cipher_text[len(ENCRYPTION_PREFIX):]
    encrypted_data = base64.urlsafe_b64decode(encoded)

    cipher = Cipher(algorithms.AES(AES_KEY), modes.CBC(AES_IV), backend=default_backend())
    decryptor = cipher.decryptor()
    decrypted_padded = decryptor.update(encrypted_data) + decryptor.finalize()

    unpadder = padding.PKCS7(128).unpadder()
    decrypted = unpadder.update(decrypted_padded) + unpadder.finalize()
    return decrypted.decode()

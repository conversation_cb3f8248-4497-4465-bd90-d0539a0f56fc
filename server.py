import base64
from enum import Enum
from fastapi import FastAPI
from pydantic import BaseModel
from google.cloud import storage
from datetime import timedelta
import pika
import os
import json
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from src.models.config import init_db
from src.constants.env_constant import env_var
from src.routes.user import router as user_router
from src.routes.client import router as client_router
from src.routes.file import router as file_router
from src.routes.page import router as page_router
from src.routes.forms import router as forms_router
from src.routes.apikey import router as apikey_router
from src.routes.stats import router as stats_router
import src.config.logger


# Initialize the database connection
init_db()

app = FastAPI(
        title="Monotelo",
        version="1.0.0"
    )


app.add_middleware(
    CORSMiddleware,
    allow_origins=env_var["ALLOW_ORIGINS"],  # Allow specific origins, split from a comma-separated string
    allow_credentials=True,  # Allow sending cookies and other credentials in requests
    allow_methods=["*"],  # Allow all HTTP methods (GET, POST, etc.)
    allow_headers=["*"],  # Allow all headers in requests
)

app.include_router(user_router)
app.include_router(client_router)
app.include_router(file_router)
app.include_router(page_router)
app.include_router(forms_router)
app.include_router(apikey_router)
app.include_router(stats_router)

if __name__ == "__main__":
    uvicorn.run(
        "server:app",  # Specify the module and application instance to run
        host="0.0.0.0",  # Bind to all IP addresses available on the machine
        port=int(env_var['PORT']),  # Convert the port from the environment variable to an integer
        reload = True
    )


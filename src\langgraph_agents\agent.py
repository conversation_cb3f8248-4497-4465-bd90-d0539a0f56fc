from datetime import datetime
import os
import re
import json
import math
import json_repair
from typing import Literal, Union, List, Optional, Any, Dict
import PIL
from bson import ObjectId
from pydantic import BaseModel
from src.config.form_config import FORM_CONFIG , FORM_TYPE_TO_IGNORE_WHILE_FORM_FINALISATION
from src.constants.constants import DEFAULT_OCR_LLM, DEFAULT_FORM_TYPE_LLM
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langgraph.graph import StateGraph, MessagesState, START, END
import base64
import logging
from src.provider.llm.llm_provider import get_llm_provider
from src.constants.enum import LLM_PROVIDER, QueueProcessingStatus, FormType
from src.provider.llm.vertex_ai_provider import GoogleVertexAIProvider
from src.constants.env_constant import env_var

from src.models.pages import Page
from src.forms.form_1095_a import Form1095A
from src.forms.form_1098_e import Form1098E
from src.forms.form_1098_t import Form1098T
from src.forms.form_1098 import Form1098
from src.forms.form_1099_b import Form1099B
# TODO composite
# TODO consolidated
from src.forms.form_1099_div import Form1099DIV
from src.forms.form_1099_g import Form1099G
from src.forms.form_1099_int import Form1099INT
from src.forms.form_1099_misc import Form1099MISC
from src.forms.form_1099_nec import Form1099NEC
from src.forms.form_1099_r import Form1099R
from src.forms.form_1099_ssa import Form1099SSA

from src.forms.form_1099_patr import Form1099PATR
from src.forms.form_1099_oid import Form1099OID
from src.forms.form_1099_ltc import Form1099LTC
from src.forms.form_1099_s import Form1099S
# from src.forms.form_5498 import Form5498
from src.forms.form_5498_sa import Form5498SA
from src.forms.form_property_taxes import FormPropertyTax
from src.forms.form_1099_c import Form1099C
from src.forms.form_crypto_tax import FormCryptoTax
from src.forms.form_crypto_tax_simple import FormCryptoTaxSimple
from src.forms.form_crypto_tax_coinbase import FormCryptoTaxCoinbase
from src.forms.form_1099_sa import Form1099SA
from src.forms.form_rrb_1099_r import FormRRB1099R
from src.forms.form_rrb_1099 import FormRRB1099

from src.forms.form_K1_1041 import FormK1_1041
from src.forms.form_k1_1065 import FormK1_1065
from src.forms.form_k1_1120s import FormK1_1120S
from src.forms.form_w_2 import FormW2
from src.forms.form_w2g import FormW2G
import uuid

from copy import deepcopy

# Keep existing FORM_TEMPLATES
FORM_TEMPLATES = {
    FormType.FORM_1095_A.value: Form1095A.model_json_schema(),
    FormType.FORM_1098_E.value: Form1098E.model_json_schema(),
    FormType.FORM_1098_T.value: Form1098T.model_json_schema(),
    FormType.FORM_1098.value: Form1098.model_json_schema(),
    FormType.FORM_1099_DIV.value: Form1099DIV.model_json_schema(),
    FormType.FORM_1099_INT.value: Form1099INT.model_json_schema(),
    FormType.FORM_1099_MISC.value: Form1099MISC.model_json_schema(),
    FormType.FORM_1099_NEC.value: Form1099NEC.model_json_schema(),
    FormType.FORM_1099_R.value: Form1099R.model_json_schema(),
    FormType.FORM_1099_SSA.value: Form1099SSA.model_json_schema(),
    FormType.FORM_K_1_1041.value: FormK1_1041.model_json_schema(),
    FormType.FORM_K_1_1065.value: FormK1_1065.model_json_schema(),
    FormType.FORM_K_1_1120_S.value: FormK1_1120S.model_json_schema(),
    FormType.FORM_W_2.value: FormW2.model_json_schema(),
    FormType.FORM_W_2G.value: FormW2G.model_json_schema(),
    FormType.FORM_1099_B.value: Form1099B.model_json_schema(),
    FormType.FORM_1099_G.value: Form1099G.model_json_schema(),
    FormType.FORM_1099_PATR.value: Form1099PATR.model_json_schema(),
    FormType.FORM_1099_OID.value: Form1099OID.model_json_schema(),
    FormType.FORM_1099_LTC.value: Form1099LTC.model_json_schema(),
    FormType.FORM_1099_S.value: Form1099S.model_json_schema(),
    # FormType.FORM_5498.value: Form5498.model_json_schema(),
    FormType.FORM_5498_SA.value: Form5498SA.model_json_schema(),
    FormType.FORM_PROPERTY_TAX.value: FormPropertyTax.model_json_schema(),
    FormType.FORM_1099_C.value: Form1099C.model_json_schema(),
    FormType.FORM_CRYPTO_TAX.value: FormCryptoTax.model_json_schema(),
    FormType.FORM_CRYPTO_TAX_SIMPLE.value: FormCryptoTaxSimple.model_json_schema(),
    FormType.FORM_CRYPTO_TAX_COINBASE.value: FormCryptoTaxCoinbase.model_json_schema(),
    FormType.FORM_1099_SA.value: Form1099SA.model_json_schema(),
    FormType.FORM_RRB_1099_R.value: FormRRB1099R.model_json_schema(),
    FormType.FORM_RRB_1099.value: FormRRB1099.model_json_schema()
}

FORM_AGENT_MAPPING = {
    FormType.FORM_1095_A.value: "form_1095a_agent",
    FormType.FORM_1098_E.value: "form_1098e_agent",
    FormType.FORM_1098_T.value: "form_1098t_agent",
    FormType.FORM_1098.value: "form_1098_agent",
    FormType.FORM_1099_DIV.value: "1099div_agent",
    FormType.FORM_1099_INT.value: "1099int_agent",
    FormType.FORM_1099_MISC.value: "form_1099misc_agent",
    FormType.FORM_1099_NEC.value: "form_1099nec_agent",
    FormType.FORM_1099_R.value: "1099r_agent",
    FormType.FORM_1099_SSA.value: "form_1099ssa_agent",
    FormType.FORM_K_1_1041.value: "form_k1_1041_agent",
    FormType.FORM_K_1_1065.value: "form_k1_1065_agent",
    FormType.FORM_K_1_1120_S.value: "form_k1_1120s_agent",
    FormType.FORM_W_2.value: "w2_agent",
    FormType.FORM_W_2G.value: "w2g_agent",
    FormType.FORM_1099_B.value: "form1099b_agent",
    FormType.FORM_1099_G.value: "form1099g_agent",
    FormType.FORM_1099_PATR.value: "form_1099patr_agent",
    FormType.FORM_1099_OID.value: "form_1099oid_agent",
    FormType.FORM_1099_LTC.value: "form_1099ltc_agent",
    FormType.FORM_1099_S.value: "form_1099s_agent",
    # FormType.FORM_5498.value: "form_5498_agent",
    FormType.FORM_5498_SA.value: "form_5498sa_agent",
    FormType.FORM_PROPERTY_TAX.value: "form_property_tax_agent",
    FormType.FORM_1099_C.value: "form_1099c_agent",
    FormType.FORM_CRYPTO_TAX.value: "form_crypto_tax_agent",
    FormType.FORM_CRYPTO_TAX_SIMPLE.value: "form_crypto_tax_simple_agent",
    FormType.FORM_CRYPTO_TAX_COINBASE.value: "form_crypto_tax_coinbase_agent",
    FormType.FORM_1099_SA.value: "form_1099sa_agent",
    FormType.FORM_RRB_1099_R.value: "form_rrb_1099r_agent",
    FormType.FORM_RRB_1099.value: "form_rrb_1099_agent"
}


def create_agent_literal(mapping: dict):
    return Literal[
        "form_identifier",
        "form_type_finalizer",
        *mapping.values(),
        "end"
    ]


AgentLiteral = create_agent_literal(FORM_AGENT_MAPPING)


class TaxAgentState(MessagesState):

    next: AgentLiteral  # type: ignore
    admin_determined_form_type: List[str]
    current_form_type: Union[str, List[str]]
    predetermined_form_type: Optional[str]  # New property
    processed_data: dict
    image_data: str
    image_path:str
    identified_forms: List[str]
    t_forms: List[str] # to be removed
    l_forms: List[str] # to be removed
    accumulated_data:List[dict]
    document_id: str
    page_id: str
    page_number: str
    isSuccess: bool
    error_message: str
    model: Any
    form_model: Any


import json

# model = get_llm_provider(DEFAULT_OCR_LLM)
# form_model = get_llm_provider(DEFAULT_FORM_TYPE_LLM)

def ensure_is_dict(mapping, key):
    """If `mapping[key]` does not exist or is not a dict, set it to an empty dict."""
    if key not in mapping or not isinstance(mapping[key], dict):
        mapping[key] = {}


def create_form_agent(form_type: str):
    """Factory function to create form processing agents"""
    def process_form(state: TaxAgentState):
        template = FORM_TEMPLATES[form_type]
        image_data = state["image_data"]
        doc_id = state["document_id"]
        page_id = state["page_id"]
        page_number = state["page_number"]
        response = {}
        model = state["model"]

        if DEFAULT_OCR_LLM == LLM_PROVIDER.OPENAI_GPT_4O:
            image_url = f"data:image/png;base64,{image_data}"

            image_prompt = HumanMessage(content=[
                {
                    "type": "text",
                    "text": f"""
                        You are an OCR assistant tasked with extracting values from a tax form image.Document id is {doc_id} and page number is {page_number}
                        Match all extracted values to the fields provided in the template below.

                        Template:
                        {json.dumps(template, indent=2)}

                        Rules:
                        - The corresponding data is used for tax filing so do accurate OCR.
                        - Focus exclusively on table data and ensure no data from outside the table is considered unless explicitly labeled for extraction.
                        - Match the field names on the form (as they appear on the page) directly to the keys provided in the template. Extract only the data for these specific fields.
                        - **Do not map any data to a field unless its name on the form matches the key in the provided template.** 
                        - Ensure extracted values are mapped to the correct field without overlap or misplacement.
                        - Convert amounts to floats (remove currency symbols and commas).
                        - Use empty strings for missing text fields and 0.0 for missing numeric fields.
                        - Format addresses properly.
                        - Format TIN/EIN/SSN correctly (XX-XXXXXXX for EIN, XXX-XX-XXXX for SSN).
                        - Use MM-DD-YYYY for dates.
                        - Extract the sequence number or index of fields as in the form template for each field wherever applicable.
                        - **Critical Rule**: **Do not mistake sequence numbers or index fields as the field value. Sequence numbers are not field values and must be excluded when determining the actual field content.**
                        - If a numeric sequence number or index appears near a field, verify that the number aligns with the context of a sequence label and not the field's value. For example:
                        - For fields like "amounts_allocated_or_apportioned_to_sc", a number like "1" that is clearly a sequence identifier must not be included in the "value" field. 
                        - This means:
                            - Extract only explicit monetary amounts (e.g., 1234.56).
                            - Exclude numbers that are part of form numbering conventions.
                        - Make sure all the data extracted from the image is present in the template.
                        - Do not make any additions to the extracted data such as '\n' in the address field.
                        - Do not take sequence numbers as currency.
                        - Add the document_id and page_number in the FormDetails."Document id" and "page number" are provided in the prompt
                        - This form data may be spread across various pages, and you have been given a single page to OCR. If some sections of the form are not present in the current page, use the default values for those sections' fields.
                        Default Values:
                            "text": "",
                            "currency": 0.00
                            "checkbox": False,
                            "year": None,
                            "date": None,
                            "float": 0.0,
                            "percentage": 0.0,
                            "list":[]
                        Ensure to apply these rules every time while processing.
                        Return the extracted data as a JSON object matching the template structure.
                    """
                },
                {
                    "type": "image_url",
                    "image_url": {"url": image_url}
                }
            ])

            # Invoke the model
            response = model.invoke([image_prompt])
            response = response.content
        
        elif DEFAULT_OCR_LLM == LLM_PROVIDER.GEMINI_1_5_PRO:
            image_path = state["image_path"]
            # img = PIL.Image.open(image_path)
            prompt = f"""
                        You are an OCR assistant tasked with extracting values from a {form_type} tax form image.Document id is {doc_id} and page number is {page_number}
                        Match all extracted values to the fields provided in the template below.

                        Template:
                        {json.dumps(template, indent=2)}

                        Rules:
                        - The corresponding data is used for tax filing so do accurate OCR.
                        - If a checkbox is marked as ticked or crossed, it means the checkbox is marked, and its value will be marked as True. If a checkbox is empty, its value will be False.
                        - Focus exclusively on table data and ensure no data from outside the table is considered unless explicitly labeled for extraction.
                        - Match the field names on the form (as they appear on the page) directly to the keys provided in the template. Extract only the data for these specific fields.
                        - **Do not map any data to a field unless its name on the form matches the key in the provided template.** 
                        - **The template schema may include field descriptions; ensure to follow the instructions provided in those descriptions when capturing the corresponding field.**
                        - Ensure extracted values are mapped to the correct field without overlap or misplacement.
                        - Convert amounts to floats (remove currency symbols and commas).
                        - Use empty strings for missing text fields and 0.0 for missing numeric fields.
                        - Format addresses properly.
                        - Format TIN/EIN/SSN correctly (XX-XXXXXXX for EIN, XXX-XX-XXXX for SSN).
                        - Use MM-DD-YYYY for dates.
                        - Extract the sequence number or index of fields as in the form template for each field wherever applicable.
                        - If a page contains same {form_type} form type multiple times then only consider one of the form which has maximum data.
                        - **Critical Rule**: **Do not mistake sequence numbers or index fields as the field value. Sequence numbers are not field values and must be excluded when determining the actual field content.**
                        - If a numeric sequence number or index appears near a field, verify that the number aligns with the context of a sequence label and not the field's value. For example:
                        - For fields like "amounts_allocated_or_apportioned_to_sc", a number like "1" that is clearly a sequence identifier must not be included in the "value" field. 
                        - This means:
                            - Extract only explicit monetary amounts (e.g., 1234.56).
                            - Exclude numbers that are part of form numbering conventions.
                        - Make sure all the data extracted from the image is present in the template.
                        - Do not make any additions to the extracted data such as '\n' in the address field.
                        - Do not take sequence numbers as currency.
                        - Add the document_id and page_number in the FormDetails."Document id" and "page number" are provided in the prompt
                        - This form data may be spread across various pages, and you have been given a single page to OCR. If some sections of the form are not present in the current page, use the default values for those sections' fields.
                        Default Values:
                            "text": "",
                            "currency": 0.00
                            "checkbox": False,
                            "year": None,
                            "date": None,
                            "float": 0.0,
                            "percentage": 0.0,
                            "list":[]
                        Ensure to apply these rules every time while processing.
                        Return the extracted data as a JSON object matching the template structure.
                    """
            # response = model.generate_content(image_path=image_path, prompt=prompt)
            vertex_model = GoogleVertexAIProvider(project_id = env_var["PROJECT_ID"],model_id = DEFAULT_OCR_LLM.value)
            response = vertex_model.generate_content(image_path=image_path, prompt=prompt)
            print("ocr print", response)

            try:
                if response.get("success",False) == False:
                    print("inside vertex")
                    # vertex_model = GoogleVertexAIProvider(project_id = env_var["PROJECT_ID"],model_id = DEFAULT_OCR_LLM.value)
                    # response = vertex_model.generate_content(image_path=image_path, prompt=prompt)
                    response = model.generate_content(image_path=image_path, prompt=prompt)
                if response.get("success",False) == False:
                    raise Exception(response.get("error", "Unknown exception during OCR"))
                response = response.get("response", None)
                response = response["candidates"][0]["content"]["parts"][0]['text']
            except Exception as e:
                import traceback
                traceback.print_exc()
                return {
                    "messages": [AIMessage(content=json.dumps({"status": "error", "message": str(e)}))],
                    "processed_data": template,
                    "next": "ocr_validation_agent",
                    "isSuccess": False,
                    "error_message": str(e)
                }
            
        elif DEFAULT_OCR_LLM == LLM_PROVIDER.OR_QWEN_2_5_VL:
            image_path = state["image_path"]
            # img = PIL.Image.open(image_path)
            prompt = f"""
                        You are an OCR assistant tasked with extracting values from a {form_type} tax form image.Document id is {doc_id} and page number is {page_number}
                        Match all extracted values to the fields provided in the template below.

                        Template:
                        {json.dumps(template, indent=2)}

                        Rules:
                        - The corresponding data is used for tax filing so do accurate OCR.
                        - If a checkbox is marked as ticked or crossed, it means the checkbox is marked, and its value will be marked as True. If a checkbox is empty, its value will be False.
                        - Focus exclusively on table data and ensure no data from outside the table is considered unless explicitly labeled for extraction.
                        - Match the field names on the form (as they appear on the page) directly to the keys provided in the template. Extract only the data for these specific fields.
                        - **Do not map any data to a field unless its name on the form matches the key in the provided template.** 
                        - **The template schema may include field descriptions; ensure to follow the instructions provided in those descriptions when capturing the corresponding field.**
                        - Ensure extracted values are mapped to the correct field without overlap or misplacement.
                        - Convert amounts to floats (remove currency symbols and commas).
                        - Use empty strings for missing text fields and 0.0 for missing numeric fields.
                        - Format addresses properly.
                        - Format TIN/EIN/SSN correctly (XX-XXXXXXX for EIN, XXX-XX-XXXX for SSN).
                        - Use MM-DD-YYYY for dates.
                        - Extract the sequence number or index of fields as in the form template for each field wherever applicable.
                        - If a page contains same {form_type} form type multiple times then only consider one of the form which has maximum data.
                        - **Critical Rule**: **Do not mistake sequence numbers or index fields as the field value. Sequence numbers are not field values and must be excluded when determining the actual field content.**
                        - If a numeric sequence number or index appears near a field, verify that the number aligns with the context of a sequence label and not the field's value. For example:
                        - For fields like "amounts_allocated_or_apportioned_to_sc", a number like "1" that is clearly a sequence identifier must not be included in the "value" field. 
                        - This means:
                            - Extract only explicit monetary amounts (e.g., 1234.56).
                            - Exclude numbers that are part of form numbering conventions.
                        - Make sure all the data extracted from the image is present in the template.
                        - Do not make any additions to the extracted data such as '\n' in the address field.
                        - Do not take sequence numbers as currency.
                        - Add the document_id and page_number in the FormDetails."Document id" and "page number" are provided in the prompt
                        - This form data may be spread across various pages, and you have been given a single page to OCR. If some sections of the form are not present in the current page, use the default values for those sections' fields.
                        Default Values:
                            "text": "",
                            "currency": 0.00
                            "checkbox": False,
                            "year": None,
                            "date": None,
                            "float": 0.0,
                            "percentage": 0.0,
                            "list":[]
                        Ensure to apply these rules every time while processing.
                        Return the extracted data as a JSON object matching the template structure.
                    """
            
            response = model.generate_content(image_path=image_path, prompt=prompt)
            print("ocr print", response)
            if response.get("success", False) == True:
                response = response["response"]
            else:
                import traceback
                traceback.print_exc()
                return {
                    "messages": [AIMessage(content=json.dumps({"status": "error", "message": str(e)}))],
                    "processed_data": template,
                    "next": "ocr_validation_agent",
                    "isSuccess": False,
                    "error_message": str(e)
                }

        try:
            cleaned_json_string = response.replace(
                "```json", "").replace("```", "").strip()
            processed_data = json_repair.loads(cleaned_json_string)
            if "form_details" not in processed_data:
                processed_data["form_details"] = {}

            if "document_id" not in processed_data["form_details"]:
                processed_data["form_details"]["document_id"] = {
                    "value": '',
                    "sequence": None,
                    "type": "text"
                }

            ensure_is_dict(processed_data["form_details"], "document_id")
            processed_data["form_details"]["document_id"]["value"] = doc_id

            if "page_number" not in processed_data["form_details"]:
                processed_data["form_details"]["page_number"] = {
                    "value": '',
                    "sequence": None,
                    "type": "text"
                }

            ensure_is_dict(processed_data["form_details"], "page_number")
            processed_data["form_details"]["page_number"]["value"] = page_number

            if "form_type" not in processed_data["form_details"]:
                processed_data["form_details"]["form_type"] = {
                    "value": '',
                    "sequence": None,
                    "type": "text"
                }

            ensure_is_dict(processed_data["form_details"], "form_type")
            processed_data["form_details"]["form_type"]["value"] = form_type

            if "page_id" not in processed_data["form_details"]:
                processed_data["form_details"]["page_id"] = {
                    "value": '',
                    "sequence": None,
                    "type": "list"
                }

            ensure_is_dict(processed_data["form_details"], "page_id")
            processed_data["form_details"]["page_id"]["value"] = [page_id]

            if "form_type" not in processed_data["form_details"]:
                processed_data["form_details"]["form_id"] = {
                    "value": '',
                    "sequence": None,
                    "type": "text"
                }

            ensure_is_dict(processed_data["form_details"], "form_id")
            processed_data["form_details"]["form_id"]["value"] = str(uuid.uuid4())


            return {
                "messages": [AIMessage(content=json.dumps({"status": "success", "form_type": form_type}))],
                "processed_data": processed_data,
                "next": "ocr_validation_agent"
            }
        except Exception as e:
            import traceback
            traceback.print_exc()
            return {
                "messages": [AIMessage(content=json.dumps({"status": "error", "message": str(e)}))],
                "processed_data": template,
                "next": "ocr_validation_agent",
                "isSuccess": False,
                "error_message": str(e)
            }

    return process_form


def form_identifier(state: TaxAgentState):
    """Agent that identifies the form type from the image"""
    image_data = state["image_data"]
    image_url = f"data:image/png;base64,{image_data}"
    form_model = state["form_model"]

    output = []
    counter = 1

    # Handle admin given form types if provided
    if state["admin_determined_form_type"][0] not in [FormType.UNKNOWN_BUT_IMPORTANT.value]: #TODO optimise this
        print("in agent py at line 473 admin_determined_form_type",state["admin_determined_form_type"])
        identified_forms = state["admin_determined_form_type"]
        return {
            "messages": [AIMessage(content=json.dumps({
                "status": "success",
                "identified_forms": identified_forms
            }))],
            "identified_forms": identified_forms,
            "current_form_type": identified_forms[0] if identified_forms else FormType.UNKNOWN.value,
            "next": "form_type_finalizer"
        }

    # Loop through all Enum values
    for form in FormType:
        # Extract key part of the form value
        form_key = form.value
        output.append(f"{counter}. {form_key}")
        counter += 1

    # Join the list into a formatted string
    possible_form_types_string = "\n".join(output)

    unknown_form_value = FormType.UNKNOWN.value
    unknown_but_important_form_value = FormType.UNKNOWN.value
    instructions_form_value = FormType.INSTRUCTIONS.value

    class FormIdentification(BaseModel):
        formtype: List[str]

    identification_prompt = HumanMessage(content=[
        {
            "type": "text",
            "text":
                # f"""
                #     You are an expert in identifying U.S. tax forms. Your task is to determine the type(s) of tax form(s) from a provided page. Here are the guidelines:
                #     **Critical Information**: Sometimes, some forms might not be declared in full on the page, and only part of the form type is mentioned. In such cases, go with the type(s) that are most appropriate.
                    
                #     Special Case: 
                #     - SC1041 K-1 corresponds to Form K-1 1041.
                #     - Schedule K-1-P corresponds to Form K-1 1065 if parternship is ticked otherwise it will be Form K-1 1120-S

                #     1. **Tax Form Types**: The possible tax form types are:
                #     {possible_form_types_string}

                #     2. **Instructions or Letter Pages**:
                #     - If the page contains **form filling instructions** on how to fill a tax form or is in a **letter format** and does not contain financial data, mark it as `"{instructions_form_value}"`.
                #     - If the page contains **form codes and instruction codes** only, mark it as '{instructions_form_value}'.
                #     - If the page does not contains any financial information but contains form name then mark the page as '{instructions_form_value}'.
                #     - If the page contains a list of identification codes, detailed reports about form filling, instructions for filing income tax returns, or details about the form, mark that page as '{instructions_form_value}'.
                #     - If the page contains a Frequently Asked Questions (FAQ) , mark that page as '{instructions_form_value}'.

                #     3. Recognition Criteria :
                #     - If the page has the structure of a form (e.g., fields, lines, tables) but does not contain any financial information, user information, or filled data, identify the form type as "{unknown_form_value}" or '{instructions_form_value}'.
                #     - If the page contains multiple form types (e.g., Form 1099-DIV and Form 1099-INT), list all identified form types as an **array**.
                    

                #     4. **Output Requirements**:
                #     - Return the identified form type(s) from the possible types listed above.
                #     - Always return the result in an array format. If the page contains multiple tax form types, include all identified form types as elements in the array.
                #     - Only return the identified form type from the given list or '{unknown_form_value}'.
                       
                #     Ensure that your identification is accurate and based strictly on the provided instructions .
                #     If the form type cannot be identified properly but it contains important financial information required for tax filling in that case return '{unknown_but_important_form_value}'.
                #     Return only the identified form type(s) or `"{unknown_form_value}"` if applicable.
                # """
                f"""
                    You are an expert in identifying U.S. tax forms. Analyze each page carefully and follow these guidelines:

                    **Tax Form Identification Protocol**

                    1. **Form Type Options**:
                    {possible_form_types_string}
                    Special Cases:
                    - SC1041 K-1 → Form K-1 1041
                    - Schedule K-1-P → Form K-1 1065 if partnership marked, else Form K-1 1120-S

                    2. **Instruction Identification** (mark as "{instructions_form_value}" if):
                    - Contains HOW-TO guidance, FAQs, or explanatory text
                    - Shows form codes/line references without data
                    - Is letter-formatted communication
                    - Lists ID codes or filing procedures
                    - Includes "Instructions for Form" headers
                    - Contains form templates WITHOUT user/financial data
                    - A handwritten Page

                    3. **Unknown Classification** (mark as "{unknown_form_value}" if):
                    - Blank form structure (fields/lines/tables) without data
                    - Notice 703 Pages
                    - Contains partial form references without financial info
                    - Shows document fragments without clear form context
                    - Has unrecognized format with no financial data

                    4. **Financial Content Priority**:
                    - If financial data exists but form isn't identifiable → "{unknown_but_important_form_value}"
                    - Prioritize form recognition over instruction/unknown when financial data exists
                    - Never mark pages with financial data as "{instructions_form_value}"

                    5. **Multi-Form Handling**:
                    - List ALL detected forms from {possible_form_types_string} in array when multiple present
                    - Verify each form has distinct financial data section

                    6. **Validation Rules**:
                    - Instruction pages MUST NOT contain user-specific data
                    - Unknown classification requires ABSENCE of financial data
                    - Forms with prefilled data but no user info = "{unknown_form_value}"

                    **Output Requirements**:
                    - Strictly use: [form_types] or ["{instructions_form_value}"] or ["{unknown_form_value}"] or ["{unknown_but_important_form_value}"]
                    - [form_types] must be from: {possible_form_types_string}
                    - Always array format
                    - No additional text/explanations

                    Focus on data presence over form structure. When uncertain between instruction/unknown, prefer "{instructions_form_value}" for text-heavy pages and "{unknown_form_value}" for blank forms.
                """
        },
        {
            "type": "image_url",
            "image_url": {"url": image_url}
        }
    ])

    # Invoke model
    try:
        response = form_model.with_structured_output(FormIdentification).invoke([identification_prompt])
        logging.info(f"Response: {response}")

        # # Clean up the response
        # identified_form = response.content.strip()
        # identified_form = identified_form.replace('```', '')
        # identified_form = identified_form.replace('json\n', '').strip()
        # identified_form = identified_form.replace('json', '').strip()

        # # 2) Parse using the robust helper
        # identified_forms = parse_llm_response(identified_form)

        # # 3) Ensure output is a list of strings
        # if not isinstance(identified_forms, list):
        #     identified_forms = [identified_forms]

        # # Remove duplicates while preserving order
        # identified_forms = list(dict.fromkeys(identified_forms))

        #     #TODO handle case-sensitive form type issue
        # logging.info(f"Identified Forms: {identified_forms}, {len(identified_forms)}")


        # return {
        #     "messages": [AIMessage(content=json.dumps({
        #         "status": "success",
        #         "identified_forms": identified_forms
        #     }))],
        #     "identified_forms": identified_forms,
        #     "current_form_type": identified_forms[0] if identified_forms else FormType.UNKNOWN.value,
        #     "next": "form_type_finalizer"
        # }

        # Extract the identified forms from the structured response
        identified_forms = response.formtype
        l_form = identified_forms
        # Remove duplicates while preserving order
        identified_forms = list(dict.fromkeys(identified_forms))

        valid_form_values = {form.value for form in FormType}  # Get a set of valid form values
        identified_forms = [
            form if form in valid_form_values else FormType.UNKNOWN.value
            for form in identified_forms
        ]

        # Ensure FormType.UNKNOWN.value is not present if the array length is greater than 1
        if len(identified_forms) > 1 and FormType.UNKNOWN.value in identified_forms:
            identified_forms = [form for form in identified_forms if form != FormType.UNKNOWN.value]

        logging.info(f"Identified Forms: {identified_forms}, {len(identified_forms)}")

        return {
            "messages": [AIMessage(content=json.dumps({
                "status": "success",
                "identified_forms": identified_forms
            }))],
            "t_forms": state["predetermined_form_type"] if state["predetermined_form_type"] else [], # to be removed
            "l_forms":l_form if l_form else [], # to be removed
            "identified_forms": identified_forms,
            "current_form_type": identified_forms[0] if identified_forms else FormType.UNKNOWN.value,
            "next": "form_type_finalizer"
        }
    
    except Exception as e:
        import traceback
        traceback.print_exc()
        return {
                "messages": [AIMessage(content=json.dumps({"status": "error", "message": str(e)}))],
                "isSuccess": False,
                "error_message": str(e),
                "next": "form_type_finalizer"
            }

    
def ocr_validation_agent(state: TaxAgentState):
    if DEFAULT_OCR_LLM == LLM_PROVIDER.OPENAI_GPT_4O:
        return {
            "messages": 'ocr llm and checkbox llm are same',
            "next": "validation_agent"
        }
    
    if state and not state["isSuccess"]:
        return {
            "messages": 'error in previous stage',
            "next": "validation_agent"
        }
    
    try:
        form_type = state["current_form_type"]
        processed_data = state["processed_data"]
        doc_id = state["document_id"]
        form_model = state["form_model"]
        page_number = state["page_number"]
        template = FORM_TEMPLATES[form_type]
        image_data = state["image_data"]
        image_url = f"data:image/png;base64,{image_data}"
        image_prompt =  HumanMessage(content=[
                    {
                        "type": "text",
                        "text": f"""
                            You are an OCR assistant tasked with extracting values from a tax form image.Document/page/form id is {doc_id} and page number is {page_number}
                            Match all extracted values to the fields provided in the template below.

                            Template:
                            {json.dumps(template, indent=2)}

                            Rules:
                            - The corresponding data is used for tax filing so do accurate OCR.
                            - Focus exclusively on quality of status of checkboxes.
                            **Checkbox Handling Rules (Primary Focus):**
                                - If a checkbox is marked as ticked, crossed (❌), or otherwise marked, its value will be True. If a checkbox is empty, its value will be False.
                                - For checkboxes named "corrected(if checked)" or "ira sep" (if available), mark the field as True only if the checkbox is explicitly marked.
                                - If the template identifies a field as a checkbox, treat it as such regardless of its appearance in the image. Extract and assign its value as True or False based on the marking.
                            - Match the field names on the form (as they appear on the page) directly to the keys provided in the template. Extract only the data for these specific fields.
                            - Make sure all the data extracted from the image is present in the template.
                            Default Values:
                                "text": "",
                                "currency": 0.00
                                "checkbox": False,
                                "year": None,
                                "date": None,
                                "float": 0.0,
                                "percentage": 0.0,
                                "list":[]
                            Ensure to apply these rules every time while processing.
                            Return the extracted data as a JSON object matching the template structure.
                        """
                    },
                    {
                        "type": "image_url",
                        "image_url": {"url": image_url}
                    }
                ])
        # Invoke the model
        response = form_model.invoke([image_prompt])
        response = response.content

        print("Response from OCR validation----->", response)

        cleaned_json_string = response.replace(
                    "```json", "").replace("```", "").strip()
        print("Cleaned json string", cleaned_json_string)
        ocr_data = json.loads(cleaned_json_string)
        print("Processed Datan from previous agent", processed_data)

        print("ocr Data: from this ocr agent", ocr_data)

        processed_data = override_checkboxes_in_data(processed_data,ocr_data)
        print("data after merging ocr", processed_data)
        return {
            "messages": 'checkbox replaced',
            "processed_data": processed_data,
            "next": "validation_agent"
        }
    except Exception as e:
        print('exception in ocr validation',e)
        import traceback
        traceback.print_exc()
        return {
            "messages": 'error',
            "next": "validation_agent"
        }

def override_checkboxes_in_data(processed_data: Any, ocr_data: Any) -> Any:
    """
    Recursively override only checkbox fields in `processed_data` with their corresponding
    values from `ocr_data`. A field is considered a 'checkbox' if:
      - It's a dict containing {'type': 'checkbox', 'value': bool}, OR
      - It's a dict where the 'value' is bool, even if 'type' is missing or different.
    In all other cases, we do NOT override.

    :param processed_data: The original dictionary (possibly nested) or list or leaf.
    :param ocr_data: The dictionary/list/leaf returned by OCR (possibly nested).
    :return: The updated `processed_data` with checkbox values overridden from `ocr_data`.
    """

    if isinstance(processed_data, dict) and isinstance(ocr_data, dict):
        if is_checkbox_candidate(processed_data):
            if is_checkbox_candidate(ocr_data):
                processed_data["value"] = ocr_data.get("value", processed_data.get("value", False))
            elif isinstance(ocr_data, bool):
                processed_data["value"] = ocr_data
            return processed_data
        else:
            for key, proc_val in processed_data.items():
                ocr_val = ocr_data.get(key) if key in ocr_data else None
                processed_data[key] = override_checkboxes_in_data(proc_val, ocr_val)
            return processed_data

    elif isinstance(processed_data, list) and isinstance(ocr_data, list):
        min_len = min(len(processed_data), len(ocr_data))
        for i in range(min_len):
            processed_data[i] = override_checkboxes_in_data(processed_data[i], ocr_data[i])
        return processed_data

    elif isinstance(processed_data, bool):
        if is_checkbox_candidate(ocr_data):
            return ocr_data.get("value", processed_data)
        return processed_data

    elif isinstance(processed_data, dict):
        if is_checkbox_candidate(processed_data):
            if isinstance(ocr_data, bool):
                processed_data["value"] = ocr_data
        return processed_data

    return processed_data


def is_checkbox_candidate(d: Any) -> bool:
    """
    Determine if d is a dictionary representing a checkbox field. 
    We consider it a 'checkbox' if:
      - d is a dict
      - EITHER d['type'] == 'checkbox'
      - OR d['value'] is strictly True or False (fallback)
    """
    if not isinstance(d, dict):
        return False
    if d.get("type") == "checkbox":
        return True
    # Fallback if 'value' is a bool, treat it as a checkbox
    val = d.get("value")
    if isinstance(val, bool):
        return True
    return False



def form_type_finalizer(state: TaxAgentState):
    """Node to finalize form type based on predetermined and identified types"""
    identified_forms = state.get("identified_forms", [])
    current_form_type = state["current_form_type"]
    predetermined_type = state.get("predetermined_form_type")

    print("Identified forms form_type_finalizer", identified_forms)
    print("Current form type form_type_finalizer", current_form_type)
    print("Predetermined form type form_type_finalizer", predetermined_type)
    
    isSuccess = state.get("isSuccess", False)

    if isSuccess == False:
        #if you have got error in the previous agent then it will return from here
        final_form_type = current_form_type
        next_agent = "end"
    else:
        if len(identified_forms) > 1:
            # if there are multiple formtypes identified in a single page either through admin or llm
            # then this block of code will be executed
            final_form_type = current_form_type
            next_agent = FORM_AGENT_MAPPING.get(final_form_type, "end")
        else:
            if current_form_type == FormType.UNKNOWN.value or current_form_type == FormType.INSTRUCTIONS.value:
                # if LLM has detected the form types as UNKNOWN or INSTRUCTIONS, then we give preference to that
                final_form_type = current_form_type
                next_agent = "end"
            elif current_form_type == FormType.UNKNOWN_BUT_IMPORTANT.value: # TODO Add error message why we are not processing this
                # if LLM has detected the form types as UNKNOWN_BUT_IMPORTANT
                if predetermined_type not in [FormType.UNKNOWN.value, None, ""] :
                    #then we give preference to script form type if it is not UNKNOWN, None, ""
                    final_form_type = predetermined_type
                    identified_forms = [predetermined_type]
                    next_agent = FORM_AGENT_MAPPING.get(final_form_type, "end")
                else:
                    # we will mark as UNKNOWN
                    final_form_type = FormType.UNKNOWN.value
                    next_agent = "end"
            else:
                if predetermined_type not in [FormType.UNKNOWN.value, None, ""] :
                    # if there is any form type detected by script(other than UNKNOWN) then we give preference to that
                    final_form_type = predetermined_type
                    next_agent = FORM_AGENT_MAPPING.get(final_form_type, "end")
                    identified_forms = [predetermined_type]
                else:
                    # if there is no form type detected by script(other than UNKNOWN) then we give preference to LLM
                    # TODO : check, if we are not allowing unknown form type to enter agent phase
                    if current_form_type in FORM_TYPE_TO_IGNORE_WHILE_FORM_FINALISATION:
                        final_form_type = FormType.UNKNOWN.value
                        identified_forms = [final_form_type]
                        next_agent = "end"
                    else:
                        final_form_type = current_form_type
                        next_agent = FORM_AGENT_MAPPING.get(final_form_type, "end")

    return {
        "messages": [AIMessage(content=json.dumps({
            "status": "success",
            "final_form_type": final_form_type,
            "predetermined_type": predetermined_type,
            "identified_type": current_form_type
        }))],
        "current_form_type": final_form_type,
        "identified_forms": identified_forms,
        "next": next_agent
    }

def get_field_info_from_schema(field_path: str, schema: dict) -> tuple[str, str | None]:
    """
    Get field type and sequence from schema using field path.
    Example field_path: "financial_fields.state_tax_withheld".
    """
    try:
        # Split the path into parts
        path_parts = field_path.split('.')
        final_path_parts = []

        for part in path_parts:
            # Use regex to remove brackets and their content
            result = re.sub(r'\[\d+\]', '', part)
            final_path_parts.append(result)
        
        path_parts = final_path_parts
        current = schema

        # Helper function to resolve $ref references
        def resolve_ref(ref_path: str, root_schema: dict):
            ref_parts = ref_path.lstrip('#/').split('/')
            resolved = root_schema
            for part in ref_parts:
                resolved = resolved.get(part, {})
            return resolved

        # Traverse the schema structure
        for part in path_parts:
            if 'properties' in current:
                current = current['properties'].get(part, {})
            elif '$ref' in current:
                # Resolve reference if found
                current = resolve_ref(current['$ref'], schema)
                if 'properties' in current:
                    current = current['properties'].get(part, {})
            elif "items" in current and current["items"] and '$ref' in current["items"]:
                # Resolve reference if found
                current = resolve_ref(current["items"]['$ref'], schema)
                if 'properties' in current:
                    current = current['properties'].get(part, {})
            else:
                current = current.get(part, {})

        # If the current field is a $ref, resolve it
        if '$ref' in current:
            current = resolve_ref(current['$ref'], schema)

        # Extract the field type and sequence
        field_type = current.get('type', None)
        if 'properties' in current and 'type' in current['properties']:
            field_type = current['properties']['type'].get('default', field_type)

        sequence = None
        if 'properties' in current and 'sequence' in current['properties']:
            sequence = current['properties']['sequence'].get('default', None)

        return field_type, sequence

    except (KeyError, TypeError) as e:
        import traceback
        traceback.print_exc()
        logging.error(f"Error processing field path '{field_path}': {e}")
        return "text", None


def get_default_value_dict() -> dict:
    """Get a dictionary of default values for all field types"""
    return {
        "text": "",
        "currency": "0.00",
        "checkbox": False,
        "year": None,
        "date": None,
        "float": 0.0,
        "percentage": 0.0,
        "contact": None,
        "zipcode": None,
        "einfield": None
    }

def get_default_value_for_type(field_type: str) -> Any:
    """Get the default value based on field type"""
    defaults = get_default_value_dict()
    return defaults.get(field_type, "")

def convert_to_us_format(date_str: str):
    """
    Converts a given date string to US format (MM-DD-YYYY).
    
    Handles:
    - If data given is `Various` return 
    - US format (MM-DD-YYYY) (returns as is if valid)
    - ISO format (YYYY-MM-DD)
    - European format (DD-MM-YYYY or DD/MM/YYYY) (converts to US format)
    - Various separators ('/', '-', spaces)
    - Extra spaces
    - Invalid dates (raises ValueError)
    
    :param date_str: Input date as a string
    :return: Date in US format (MM-DD-YYYY)
    """
    try:
        default_val_dict = get_default_value_dict()

        if date_str.lower() == "various":
            return date_str 

        if not date_str or not isinstance(date_str, str):
            raise ValueError("Invalid date input. Must be a non-empty string.")
        
        # Normalize separators (replace '/', '.', ',', ';' and spaces with '-')
        date_str = re.sub(r'[\/\s.,;]+', '-', date_str.strip())
        
        # Check if in ISO format YYYY-MM-DD
        match_iso = re.match(r'^(\d{4})-(\d{1,2})-(\d{1,2})$', date_str)
        if match_iso:
            year, month, day = map(int, match_iso.groups())
            return validate_and_format_date(month, day, year)
        
        # Check if in US format MM-DD-YYYY first and validate
        match_us = re.match(r'^(\d{1,2})-(\d{1,2})-(\d{4})$', date_str)
        if match_us:
            month, day, year = map(int, match_us.groups())
            if 1 <= month <= 12:
                return validate_and_format_date(month, day, year)
        
        # Check if in European format DD-MM-YYYY
        match_eu = re.match(r'^(\d{1,2})-(\d{1,2})-(\d{4})$', date_str)
        if match_eu:
            day, month, year = map(int, match_eu.groups())
            return validate_and_format_date(month, day, year)
        
        return date_str if date_str else default_val_dict['date']
    
    except Exception as e:
        raise ValueError(f"Error processing date: {str(e)}")

# Function to clean and validate value
def clean_value(value):
    """
    Recursively cleans a value to handle data inconsistencies.
    
    - None: returned as is.
    - Boolean: returned as is.
    - Integers and floats: converted to a string formatted to two decimal places.
    - Numeric strings: converted to a float and then formatted to two decimal places.
    - Non-numeric strings: stripped of extra whitespace.
    - Lists: each element is cleaned recursively.
    - Dictionaries: each value is cleaned recursively (keys are assumed to be consistent).
    - Other types: returned unchanged.
    """
    if value is None:
        return value
    if isinstance(value, bool):
        return value
    if isinstance(value, (int, float)):
        return f"{float(value):.2f}"
    if isinstance(value, str):
        stripped = value.strip()
        # Check if it's a valid integer (handles negatives too)
        if re.fullmatch(r'-?\d+', stripped):
            return stripped
        # Try to convert to a float; if it fails, treat as a non-numeric string.
        try:
            return f"{float(value):.2f}"
        except ValueError:
            return value.strip()
    if isinstance(value, list):
        return [clean_value(item) for item in value]
    if isinstance(value, dict):
        return {key: clean_value(val) for key, val in value.items()}
    # For any other data type, return as is.
    return value

def clean_dict(entry):
    """
    Applies the clean_value function to every field in the dictionary.
    """
    # Check if entry ois json
    if not isinstance(entry, dict):
        try:
            if isinstance(entry,str) and entry.find("{") != -1:
                entry = json.loads(entry)
            else:
                return entry
        except json.JSONDecodeError:
            # If it fails, return the original string
            return entry
    return {key: clean_value(val) for key, val in entry.items()}

def validate_and_format_date(month: int, day: int, year: int) -> str:
    """
    Validates the given date components and returns a properly formatted US date.
    
    :param month: Month as integer
    :param day: Day as integer
    :param year: Year as integer
    :return: Validated date in MM-DD-YYYY format
    """
    try:
        valid_date = datetime(year, month, day)  # Raises ValueError if invalid
        return valid_date.strftime("%m-%d-%Y")
    except ValueError:
        # raise ValueError("Invalid date values. Please provide a valid date.")
        return "00-00-0000"
      
def validate_and_format_field(value: Any, field_type: str, sequence: int = None) -> Dict[str, Any]:
    default_val_dict = get_default_value_dict()
    
    # If value is already a dict with a 'value' and 'type', just ensure 'sequence' is present

    if isinstance(value, dict) and 'value' in value:
        # Ensure sequence is set
        if 'sequence' not in value:
            value['sequence'] = sequence
        if 'type' not in value or not value["type"] or (value["type"] != field_type):
            value["type"] = field_type

    elif not isinstance(value, dict):
        # Ensure sequence is set
        temp = {
            "value": value,
            "sequence": sequence,
            "type": field_type
        }
        value = temp

    if isinstance(value, dict) and 'value' in value:
        # Ensure sequence is set
        if 'sequence' not in value:
            value['sequence'] = sequence

        if 'type' not in value or not value["type"] or (value["type"] != field_type):
            value["type"] = field_type
        
        # Apply default values if needed
        if value['type'] == 'text':
            if (value['value'] is None or value['value'] == ''):
                value['value'] = default_val_dict['text']
            else:
                # Check if \n character is there then clean it
                value['value'] = re.sub(r'\n', ' ', value['value'])
        elif value['type'] == 'currency':
            # Format as currency
            if value['value'] is None or value['value'] == '':
                value['value'] = default_val_dict['currency']
            else:
                value['value'] = format_currency(clean_value(value['value']))
        elif value['type'] == 'checkbox':
            # Ensure boolean
            if not isinstance(value['value'], bool):
                value['value'] = bool(value['value'])
        elif value['type'] == 'year':
            if value['value'] is None or value['value'] == '':
                value['value'] = default_val_dict['year']
            else:
                value['value'] = math.ceil(float(clean_value(value['value'])))
        elif value['type'] == 'date':
            if value['value'] is None or value['value'] == '' or value['value'] == '0':
                value['value'] = default_val_dict['date']
            else:
                value['value'] = convert_to_us_format(value['value'])
        elif value['type'] == 'float':
            if value['value'] is None or value['value'] == '':
                value['value'] = default_val_dict['float']
            else:
                value['value'] = clean_value(value['value'])
        elif value['type'] == 'percentage':
            if value['value'] is None or value['value'] == '':
                value['value'] = default_val_dict['percentage']
            else:
                value['value'] = clean_value(value['value'])
        elif value['type'] == 'contact':
            if value['value'] is None or value['value'] == '':
                value['value'] = default_val_dict['contact']
            else:
                value['value'] = re.sub(r'\D', '', str(value['value']))
        elif value['type'] == 'zipcode':
            if value['value'] is None or value['value'] == '':
                value['value'] = default_val_dict['zipcode']
            else:
                value['value'] = re.sub(r'\D', '', str(value['value']))
                # Ensure 5-digit zip code if more than 5 digits are present in the value (e.g., 12345-6789) crop it to 5 digits
                if len(value['value']) > 5:
                    value['value'] = value['value'][:5]
        elif value['type'] == 'einfield':
            if value['value'] is None or value['value'] == '':
                value['value'] = default_val_dict['einfield']
            else:
                value['value'] = re.sub(r'\D', '', str(value['value']))
                # ensure value['value'] is 9 digits
                if len(value['value']) != 9:
                    value['value'] = default_val_dict['einfield']

        return value

    # If value is not a dict with 'value' and 'type' keys, then create the standard structure
    if value is None or (isinstance(value, str) and value.strip() == ""):
        value = get_default_value_for_type(field_type)
        
    # Apply any type-specific transformations for non-dict values
    if field_type == 'currency':
        value = format_currency(value)
    elif field_type == 'date' and value == '':
        value = get_default_value_for_type(field_type)
    elif field_type == 'checkbox':
        if isinstance(value, str):
            value = value.strip().lower() in ("true", "1", "yes")
        else:
            value = bool(value)

    # Return the properly structured field
    return {
        "value": value.get("value", None) if isinstance(value, dict) else value,
        "sequence": sequence,
        "type": field_type
    }

def merge_inner_dict(data):
    if isinstance(data, dict):
        for key, value in list(data.items()):
            # Check if value is a dictionary with a "value" key
            if isinstance(value, dict) and "value" in value:
                if isinstance(value["value"], dict) or isinstance(value["value"], list):
                    # If "value" is a dictionary or a list, replace the parent with it
                    data[key] = value["value"]
                    merge_inner_dict(data[key])
                else:
                    # Otherwise, merge the inner "value" into the parent
                    data[key] = value
            else:
                # Recursively handle nested dictionaries
                merge_inner_dict(value)
    elif isinstance(data, list):
        for item in data:
            merge_inner_dict(item)
    return data

def format_currency(val):
    # Utility function for currency formatting
    from decimal import Decimal, ROUND_HALF_UP
    try:
        val = str(val).replace(',', '')
        numeric_value = abs(Decimal(str(val))).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        return format(numeric_value, '.2f')
    except:
        import traceback
        traceback.print_exc()
        return "0.00"



def validation_agent(state: TaxAgentState):
    """
    Validation agent that formats OCR data to ensure all values have value, sequence, and type fields
    using form templates schema
    """
    processed_data = state.get("processed_data", {})
    print("Processed data before merge inner: ============>>>>>>", processed_data)
    processed_data = merge_inner_dict(processed_data)
    print("Processed data after merge inner: ============>>>>>>", processed_data)

    if state and not state["isSuccess"]:
        return {
            "messages": [AIMessage(content=json.dumps({
                "status": "error",
                "message":f"Failed to process the form validation agent: {state.get('error_message', 'Unknown error')}"
            }))],
            "next": "end"
        }
    

    # Required keys and their respective locations for validation
    required_keys = {
        "current_form_type": state,
        "document_id": processed_data.get("form_details"),
        "page_number": processed_data.get("form_details")
    }

    # Iterate through required keys to check for missing ones
    for key, location in required_keys.items():
        if key not in location or not location[key]:
            return {
                "messages": [AIMessage(content=json.dumps({
                    "status": "error",
                    "message": f"{key} is missing"
                }))],
                "processed_data": processed_data,
                "isSuccess":False,
                "error_message": f"{key} is missing",
                "next": "end"
            }

    form_type = state["current_form_type"]
    
    # Get the schema from FORM_TEMPLATES
    schema = FORM_TEMPLATES.get(form_type)
    if not schema:
        return {
            "messages": [AIMessage(content=json.dumps({
                "status": "error",
                "message": f"No schema found for form type: {form_type}"
            }))],
            "isSuccess": False,
            "error_message": f"No schema found for form type: {form_type}",
            "processed_data": processed_data,
            "next": "end"
        }
    
    if not isinstance(processed_data["form_details"], dict):
        return {
            "messages": [AIMessage(content=json.dumps({
                "status": "error",
                "message": "Invalid form_details format"
            }))],
            "isSuccess":False,
            "error_message": "Invalid form_details format",
            "processed_data": processed_data,
            "next": "end"
        }
    
    # Check if form_type is missing or its value is empty/None, and update it
    if (
        "form_type" not in processed_data["form_details"] or 
        not isinstance(processed_data["form_details"]["form_type"], dict) or
        not processed_data["form_details"]["form_type"].get("value")
    ):
        processed_data["form_details"]["form_type"] = {
            "value": state["current_form_type"],
            "sequence": None,
            "type": "text"
        }
    elif (
        processed_data["form_details"]["form_type"]["value"] not in [form_type.value for form_type in FormType]
    ):
        processed_data["form_details"]["form_type"] = {
            "value": state["current_form_type"],
            "sequence": None,
            "type": "text"
        }

    
    # Deep copy to avoid modifying original data
    formatted_data = deepcopy(processed_data)

    def process_nested_dict(data: Dict[str, Any], parent_path: str = "") -> Dict[str, Any]:
        """Recursively process nested dictionary to format fields, including arrays."""
        formatted = {}

        for key, value in data.items():
            current_path = f"{parent_path}.{key}" if parent_path else key

            # Check if the current dict is just an enum wrapper
            if isinstance(value, dict) and set(value.keys()) == {"enum"}:
                # Replace the enum wrapper with its inner dict value and continue processing it
                value = value["enum"]
            elif isinstance(value, dict) and set(value.keys()) == {"Value"}:
                # Replace the Value wrapper with its inner dict value and continue processing it
                value = value["Value"]
            elif isinstance(value, dict) and set(value.keys()) == {"properties"}:
                # Replace the value wrapper with its inner dict value and continue processing it
                value = value["properties"]

            # If `value` is a dict that does NOT have the standard {"value", "type"} structure:
            if isinstance(value, dict) and not ('value' in value or 'type' in value):
                # Recursively process nested dictionary
                formatted[key] = process_nested_dict(value, current_path)
            else:
                # Otherwise, value might be the standard structure or we need to handle arrays
                if isinstance(value, dict) and value.get('type') == 'array':
                    # Handle an array of items
                    array_data = value.get('value', [])
                    if isinstance(array_data, list):
                        processed_array = []
                        # Remove duplicates while preserving order
                        unique_data = list({json.dumps(item, sort_keys=False) for item in array_data})
                        unique_json_data = [json.loads(item) for item in unique_data]
                        array_data = unique_json_data
                        for idx, item in enumerate(array_data):
                            # Each `item` is presumably a dict with nested fields
                            if isinstance(item, dict):
                                processed_array.append(process_nested_dict(item, f"{current_path}[{idx}]"))
                            else:
                                # If it's not a dict, just wrap or handle as needed
                                processed_array.append(item)
                        
                        # Replace the 'value' in the dict with the processed array
                        formatted[key] = {
                            "type": value["type"],
                            "sequence": value.get("sequence"),
                            "value": processed_array
                        }
                    else:
                        # If somehow 'value' is not a list, you can decide how to handle that
                        formatted[key] = value
                elif isinstance(value, list):
                    # Handle an array of items
                    array_data = value
                    cleaned_data = [clean_dict(item) for item in array_data]
                    # Remove duplicates while preserving order
                    unique_data = list({json.dumps(item, sort_keys=False) for item in cleaned_data})
                    unique_json_data = [json.loads(item) for item in unique_data]
                    array_data = unique_json_data
                    if isinstance(array_data, list):
                        processed_array = []
                        for idx, item in enumerate(array_data):
                            # Each `item` is presumably a dict with nested fields
                            if isinstance(item, dict):
                                processed_array.append(process_nested_dict(item, f"{current_path}[{idx}]"))
                            else:
                                # If it's not a dict, just wrap or handle as needed
                                processed_array.append(item)
                        
                        # Replace the 'value' in the dict with the processed array
                        formatted[key] = {
                            "type": "array",
                            "sequence": None,
                            "value": processed_array
                        }
                    else:
                        # If somehow 'value' is not a list, you can decide how to handle that
                        formatted[key] = value
                else:
                    # Normal field (text, currency, date, etc.)
                    field_type, sequence = get_field_info_from_schema(current_path, schema)
                    formatted[key] = validate_and_format_field(value, field_type, sequence)

        return formatted
    
    next_form_type = ""
    
    # Process the entire data structure
    try:
        # Process the entire data structure
        formatted_data = process_nested_dict(formatted_data)

        check_this_field_for_multiple_data_in_same_form = FORM_CONFIG.get(form_type).check_this_field_for_multiple_data_in_same_form
        do_not_check_this_field_for_multiple_data_in_same_form_if_there_is_info_in_this_field = FORM_CONFIG.get(form_type).do_not_check_this_field_for_multiple_data_in_same_form_if_there_is_info_in_this_field
        
        data_not_exists_in_this_field = False
        if do_not_check_this_field_for_multiple_data_in_same_form_if_there_is_info_in_this_field:
            path_parts = do_not_check_this_field_for_multiple_data_in_same_form_if_there_is_info_in_this_field.split('.')

            temp_data = formatted_data

            # totals.interest_income
            # ["totals", "interest_income"]

            for index,part in enumerate(path_parts):
                if part in temp_data:
                    temp_data = temp_data[part]
                    if isinstance(temp_data, dict) and index == len(path_parts) - 1:
                        if "value" in temp_data:
                            if temp_data["value"]:
                                if temp_data["type"] == "currency":
                                    temp_data_in_int = int(float(clean_value(temp_data["value"])))
                                    data_not_exists_in_this_field = True if temp_data_in_int == 0 else False
                            else:
                                data_not_exists_in_this_field = True
                        break
                    continue
                else:
                    data_not_exists_in_this_field = True

        if check_this_field_for_multiple_data_in_same_form and check_this_field_for_multiple_data_in_same_form in formatted_data and len(formatted_data[check_this_field_for_multiple_data_in_same_form]["value"]) >1 and data_not_exists_in_this_field:
                no_of_items = len(formatted_data[check_this_field_for_multiple_data_in_same_form]["value"])
                # copy each field from formatted_data to new_formatted_data
                for i in range(no_of_items):
                    new_formatted_data = {}
                    for key, value in formatted_data.items():
                        if key == check_this_field_for_multiple_data_in_same_form:
                            new_formatted_data[key] = {
                                "type": "array",
                                "sequence": None,
                                "value": [value["value"][i]]
                            }
                        else:
                            new_formatted_data[key] = value
                    
                    # Add formatted data to accumulated_data
                    state["accumulated_data"].append(new_formatted_data)
        else:
            # Add formatted data to accumulated_data
            state["accumulated_data"].append(formatted_data)

        
        print("Accumulated data: ============>>>>>>", state["accumulated_data"])


        # Check if we've processed all identified forms
        identified_forms = state.get("identified_forms", [])
        current_form_index = identified_forms.index(state["current_form_type"])
        next_form_type = state["current_form_type"]

        if current_form_index < len(identified_forms) - 1:
            # More forms to process
            next_form_type = identified_forms[current_form_index + 1]
            # next_agent = FORM_AGENT_MAPPING.get(next_form_type, "end")
            next_agent = "form_type_finalizer"
            
            # Clear processed_data for next form
            state["processed_data"] = {}
            state["current_form_type"] = next_form_type
            
            status = "success"
            message = f"Moving to next form type: {next_form_type}"

        else:
            # All forms processed
            next_agent = "end"
            status = "success"
            message = "All forms processed successfully"
        
        return {
        "messages": [AIMessage(content=json.dumps({
            "status": status,
            "message": message
        }))],
        "processed_data": {},
        "current_form_type":next_form_type,
        "accumulated_data": state["accumulated_data"],
        "next": next_agent
    }

    except Exception as e:
        status = "error"
        message = f"Error during validation: {str(e)}"
        next_agent = "end"

        import traceback
        traceback.print_exc()

        return {
        "messages": [AIMessage(content=json.dumps({
            "status": status,
            "message": message
        }))],
        "processed_data": {},
        "current_form_type":next_form_type,
        "accumulated_data": state["accumulated_data"],
        "next": next_agent,
        "isSuccess": False,
        "error_message": message
    }



# Create processing agents for each form type
w2_agent = create_form_agent(FormType.FORM_W_2.value)
w2g_agent = create_form_agent(FormType.FORM_W_2G.value)
form_1099g_agent = create_form_agent(FormType.FORM_1099_G.value)
form_1099b_agent = create_form_agent(FormType.FORM_1099_B.value)
form_1099int_agent = create_form_agent(FormType.FORM_1099_INT.value)
form_1099r_agent = create_form_agent(FormType.FORM_1099_R.value)
form_1099div_agent = create_form_agent(FormType.FORM_1099_DIV.value)
form_1099misc_agent = create_form_agent(FormType.FORM_1099_MISC.value)
form_1099nec_agent = create_form_agent(FormType.FORM_1099_NEC.value)
form_1099ssa_agent = create_form_agent(FormType.FORM_1099_SSA.value)
form_k1_1041_agent = create_form_agent(FormType.FORM_K_1_1041.value)
form_k1_1065_agent = create_form_agent(FormType.FORM_K_1_1065.value)
form_k1_1120s_agent = create_form_agent(FormType.FORM_K_1_1120_S.value)
form_1095a_agent = create_form_agent(FormType.FORM_1095_A.value)
form_1098e_agent = create_form_agent(FormType.FORM_1098_E.value)
form_1098t_agent = create_form_agent(FormType.FORM_1098_T.value)
form_1098_agent = create_form_agent(FormType.FORM_1098.value)

form_1099patr_agent = create_form_agent(FormType.FORM_1099_PATR.value)
form_1099oid_agent = create_form_agent(FormType.FORM_1099_OID.value)
form_1099ltc_agent = create_form_agent(FormType.FORM_1099_LTC.value)
form_1099s_agent = create_form_agent(FormType.FORM_1099_S.value)
# form_5498_agent = create_form_agent(FormType.FORM_5498.value)
form_5498sa_agent = create_form_agent(FormType.FORM_5498_SA.value)
form_property_tax_agent = create_form_agent(FormType.FORM_PROPERTY_TAX.value)
form_1099c_agent = create_form_agent(FormType.FORM_1099_C.value)
form_crypto_tax_agent = create_form_agent(FormType.FORM_CRYPTO_TAX.value)
form_crypto_tax_simple_agent = create_form_agent(FormType.FORM_CRYPTO_TAX_SIMPLE.value)
form_crypto_tax_coinbase_agent = create_form_agent(FormType.FORM_CRYPTO_TAX_COINBASE.value)
form_1099sa_agent = create_form_agent(FormType.FORM_1099_SA.value)
form_rrb_1099r_agent = create_form_agent(FormType.FORM_RRB_1099_R.value)
form_rrb_1099_agent = create_form_agent(FormType.FORM_RRB_1099.value)

def create_tax_form_workflow() -> StateGraph:
    """Create the tax form processing workflow graph with form type finalization"""
    builder = StateGraph(TaxAgentState)

    # Add nodes
    builder.add_node("form_identifier", form_identifier)
    builder.add_node("form_type_finalizer", form_type_finalizer)
    builder.add_node(FORM_AGENT_MAPPING[FormType.FORM_W_2.value], w2_agent)
    builder.add_node(FORM_AGENT_MAPPING[FormType.FORM_W_2G.value], w2g_agent)
    builder.add_node(
        FORM_AGENT_MAPPING[FormType.FORM_1099_INT.value], form_1099int_agent)
    builder.add_node(
        FORM_AGENT_MAPPING[FormType.FORM_1099_R.value], form_1099r_agent)
    builder.add_node(
        FORM_AGENT_MAPPING[FormType.FORM_1099_DIV.value], form_1099div_agent)
    builder.add_node(
        FORM_AGENT_MAPPING[FormType.FORM_1099_B.value], form_1099b_agent)
    builder.add_node(
        FORM_AGENT_MAPPING[FormType.FORM_1099_G.value], form_1099g_agent)
    builder.add_node(
        FORM_AGENT_MAPPING[FormType.FORM_1099_MISC.value], form_1099misc_agent)
    builder.add_node(
        FORM_AGENT_MAPPING[FormType.FORM_1099_NEC.value], form_1099nec_agent)
    builder.add_node(
        FORM_AGENT_MAPPING[FormType.FORM_1099_SSA.value], form_1099ssa_agent)
    builder.add_node(
        FORM_AGENT_MAPPING[FormType.FORM_K_1_1041.value], form_k1_1041_agent)
    builder.add_node(
        FORM_AGENT_MAPPING[FormType.FORM_K_1_1065.value], form_k1_1065_agent)
    builder.add_node(
        FORM_AGENT_MAPPING[FormType.FORM_K_1_1120_S.value], form_k1_1120s_agent)
    builder.add_node(
        FORM_AGENT_MAPPING[FormType.FORM_1095_A.value], form_1095a_agent)
    builder.add_node(
        FORM_AGENT_MAPPING[FormType.FORM_1098_E.value], form_1098e_agent)
    builder.add_node(
        FORM_AGENT_MAPPING[FormType.FORM_1098_T.value], form_1098t_agent)
    builder.add_node(
        FORM_AGENT_MAPPING[FormType.FORM_1098.value], form_1098_agent)
    
    builder.add_node(FORM_AGENT_MAPPING[FormType.FORM_1099_PATR.value], form_1099patr_agent)
    builder.add_node(FORM_AGENT_MAPPING[FormType.FORM_1099_OID.value], form_1099oid_agent)
    builder.add_node(FORM_AGENT_MAPPING[FormType.FORM_1099_LTC.value], form_1099ltc_agent)
    builder.add_node(FORM_AGENT_MAPPING[FormType.FORM_1099_S.value], form_1099s_agent)
    # builder.add_node(FORM_AGENT_MAPPING[FormType.FORM_5498.value], form_5498_agent)
    builder.add_node(FORM_AGENT_MAPPING[FormType.FORM_5498_SA.value], form_5498sa_agent)
    builder.add_node(FORM_AGENT_MAPPING[FormType.FORM_PROPERTY_TAX.value], form_property_tax_agent)
    builder.add_node(FORM_AGENT_MAPPING[FormType.FORM_1099_C.value], form_1099c_agent)
    builder.add_node(FORM_AGENT_MAPPING[FormType.FORM_CRYPTO_TAX.value], form_crypto_tax_agent)
    builder.add_node(FORM_AGENT_MAPPING[FormType.FORM_CRYPTO_TAX_SIMPLE.value], form_crypto_tax_simple_agent)
    builder.add_node(FORM_AGENT_MAPPING[FormType.FORM_CRYPTO_TAX_COINBASE.value], form_crypto_tax_coinbase_agent)
    builder.add_node(FORM_AGENT_MAPPING[FormType.FORM_1099_SA.value], form_1099sa_agent)
    builder.add_node(FORM_AGENT_MAPPING[FormType.FORM_RRB_1099_R.value], form_rrb_1099r_agent)
    builder.add_node(FORM_AGENT_MAPPING[FormType.FORM_RRB_1099.value], form_rrb_1099_agent)

    builder.add_node("validation_agent", validation_agent)
    builder.add_node("ocr_validation_agent", ocr_validation_agent)

    # Add edges
    builder.add_edge(START, "form_identifier")
    builder.add_edge("form_identifier", "form_type_finalizer")

    # Add conditional edges from form_type_finalizer to processing agents
    builder.add_conditional_edges(
        "form_type_finalizer",
        lambda state: state["next"],
        {
            FORM_AGENT_MAPPING[FormType.FORM_W_2.value]: FORM_AGENT_MAPPING[FormType.FORM_W_2.value],
            FORM_AGENT_MAPPING[FormType.FORM_W_2G.value]: FORM_AGENT_MAPPING[FormType.FORM_W_2G.value],
            FORM_AGENT_MAPPING[FormType.FORM_1099_INT.value]: FORM_AGENT_MAPPING[FormType.FORM_1099_INT.value],
            FORM_AGENT_MAPPING[FormType.FORM_1099_R.value]: FORM_AGENT_MAPPING[FormType.FORM_1099_R.value],
            FORM_AGENT_MAPPING[FormType.FORM_1099_DIV.value]: FORM_AGENT_MAPPING[FormType.FORM_1099_DIV.value],
            FORM_AGENT_MAPPING[FormType.FORM_1099_B.value]: FORM_AGENT_MAPPING[FormType.FORM_1099_B.value],
            FORM_AGENT_MAPPING[FormType.FORM_1099_G.value]: FORM_AGENT_MAPPING[FormType.FORM_1099_G.value],
            FORM_AGENT_MAPPING[FormType.FORM_1099_MISC.value]: FORM_AGENT_MAPPING[FormType.FORM_1099_MISC.value],
            FORM_AGENT_MAPPING[FormType.FORM_1099_NEC.value]: FORM_AGENT_MAPPING[FormType.FORM_1099_NEC.value],
            FORM_AGENT_MAPPING[FormType.FORM_1099_SSA.value]: FORM_AGENT_MAPPING[FormType.FORM_1099_SSA.value],
            FORM_AGENT_MAPPING[FormType.FORM_K_1_1041.value]: FORM_AGENT_MAPPING[FormType.FORM_K_1_1041.value],
            FORM_AGENT_MAPPING[FormType.FORM_K_1_1065.value]: FORM_AGENT_MAPPING[FormType.FORM_K_1_1065.value],
            FORM_AGENT_MAPPING[FormType.FORM_K_1_1120_S.value]: FORM_AGENT_MAPPING[FormType.FORM_K_1_1120_S.value],
            FORM_AGENT_MAPPING[FormType.FORM_1095_A.value]: FORM_AGENT_MAPPING[FormType.FORM_1095_A.value],
            FORM_AGENT_MAPPING[FormType.FORM_1098_E.value]: FORM_AGENT_MAPPING[FormType.FORM_1098_E.value],
            FORM_AGENT_MAPPING[FormType.FORM_1098_T.value]: FORM_AGENT_MAPPING[FormType.FORM_1098_T.value],
            FORM_AGENT_MAPPING[FormType.FORM_1098.value]: FORM_AGENT_MAPPING[FormType.FORM_1098.value],

            FORM_AGENT_MAPPING[FormType.FORM_1099_PATR.value]: FORM_AGENT_MAPPING[FormType.FORM_1099_PATR.value],
            FORM_AGENT_MAPPING[FormType.FORM_1099_OID.value]: FORM_AGENT_MAPPING[FormType.FORM_1099_OID.value],
            FORM_AGENT_MAPPING[FormType.FORM_1099_LTC.value]: FORM_AGENT_MAPPING[FormType.FORM_1099_LTC.value],
            FORM_AGENT_MAPPING[FormType.FORM_1099_S.value]: FORM_AGENT_MAPPING[FormType.FORM_1099_S.value],
            # FORM_AGENT_MAPPING[FormType.FORM_5498.value]: FORM_AGENT_MAPPING[FormType.FORM_5498.value],
            FORM_AGENT_MAPPING[FormType.FORM_5498_SA.value]: FORM_AGENT_MAPPING[FormType.FORM_5498_SA.value],
            FORM_AGENT_MAPPING[FormType.FORM_PROPERTY_TAX.value]: FORM_AGENT_MAPPING[FormType.FORM_PROPERTY_TAX.value],
            FORM_AGENT_MAPPING[FormType.FORM_1099_C.value]: FORM_AGENT_MAPPING[FormType.FORM_1099_C.value],
            FORM_AGENT_MAPPING[FormType.FORM_CRYPTO_TAX.value]: FORM_AGENT_MAPPING[FormType.FORM_CRYPTO_TAX.value],
            FORM_AGENT_MAPPING[FormType.FORM_CRYPTO_TAX_SIMPLE.value]: FORM_AGENT_MAPPING[FormType.FORM_CRYPTO_TAX_SIMPLE.value],
            FORM_AGENT_MAPPING[FormType.FORM_CRYPTO_TAX_COINBASE.value]: FORM_AGENT_MAPPING[FormType.FORM_CRYPTO_TAX_COINBASE.value],
            FORM_AGENT_MAPPING[FormType.FORM_1099_SA.value]: FORM_AGENT_MAPPING[FormType.FORM_1099_SA.value],
            FORM_AGENT_MAPPING[FormType.FORM_RRB_1099_R.value]: FORM_AGENT_MAPPING[FormType.FORM_RRB_1099_R.value],
            FORM_AGENT_MAPPING[FormType.FORM_RRB_1099.value]: FORM_AGENT_MAPPING[FormType.FORM_RRB_1099.value],
            "end": END
        }
    )

    # Add edges from processing agents to validation agent
    # builder.add_edge(
    #     FORM_AGENT_MAPPING[FormType.FORM_W_2.value], "validation_agent")
    # builder.add_edge(
    #     FORM_AGENT_MAPPING[FormType.FORM_W_2G.value], "validation_agent")
    # builder.add_edge(
    #     FORM_AGENT_MAPPING[FormType.FORM_1099_INT.value], "validation_agent")
    # builder.add_edge(
    #     FORM_AGENT_MAPPING[FormType.FORM_1099_R.value], "validation_agent")
    # builder.add_edge(
    #     FORM_AGENT_MAPPING[FormType.FORM_1099_DIV.value], "validation_agent")
    # builder.add_edge(
    #     FORM_AGENT_MAPPING[FormType.FORM_1099_B.value], "validation_agent")
    # builder.add_edge(
    #     FORM_AGENT_MAPPING[FormType.FORM_1099_G.value], "validation_agent")
    # builder.add_edge(
    #     FORM_AGENT_MAPPING[FormType.FORM_1099_MISC.value], "validation_agent")
    # builder.add_edge(
    #     FORM_AGENT_MAPPING[FormType.FORM_1099_NEC.value], "validation_agent")
    # builder.add_edge(
    #     FORM_AGENT_MAPPING[FormType.FORM_1099_SSA.value], "validation_agent")
    # builder.add_edge(
    #     FORM_AGENT_MAPPING[FormType.FORM_K_1_1041.value], "validation_agent")
    # builder.add_edge(
    #     FORM_AGENT_MAPPING[FormType.FORM_K_1_1065.value], "validation_agent")
    # builder.add_edge(
    #     FORM_AGENT_MAPPING[FormType.FORM_K_1_1120_S.value], "validation_agent")
    # builder.add_edge(
    #     FORM_AGENT_MAPPING[FormType.FORM_1095_A.value], "validation_agent")
    # builder.add_edge(
    #     FORM_AGENT_MAPPING[FormType.FORM_1098_E.value], "validation_agent")
    # builder.add_edge(
    #     FORM_AGENT_MAPPING[FormType.FORM_1098_T.value], "validation_agent")
    # builder.add_edge(
    #     FORM_AGENT_MAPPING[FormType.FORM_1098.value], "validation_agent")

    builder.add_edge(
        FORM_AGENT_MAPPING[FormType.FORM_W_2.value], "ocr_validation_agent")
    builder.add_edge(
        FORM_AGENT_MAPPING[FormType.FORM_W_2G.value], "ocr_validation_agent")
    builder.add_edge(
        FORM_AGENT_MAPPING[FormType.FORM_1099_INT.value], "ocr_validation_agent")
    builder.add_edge(
        FORM_AGENT_MAPPING[FormType.FORM_1099_R.value], "ocr_validation_agent")
    builder.add_edge(
        FORM_AGENT_MAPPING[FormType.FORM_1099_DIV.value], "ocr_validation_agent")
    builder.add_edge(
        FORM_AGENT_MAPPING[FormType.FORM_1099_B.value], "ocr_validation_agent")
    builder.add_edge(
        FORM_AGENT_MAPPING[FormType.FORM_1099_G.value], "ocr_validation_agent")
    builder.add_edge(
        FORM_AGENT_MAPPING[FormType.FORM_1099_MISC.value], "ocr_validation_agent")
    builder.add_edge(
        FORM_AGENT_MAPPING[FormType.FORM_1099_NEC.value], "ocr_validation_agent")
    builder.add_edge(
        FORM_AGENT_MAPPING[FormType.FORM_1099_SSA.value], "ocr_validation_agent")
    builder.add_edge(
        FORM_AGENT_MAPPING[FormType.FORM_K_1_1041.value], "ocr_validation_agent")
    builder.add_edge(
        FORM_AGENT_MAPPING[FormType.FORM_K_1_1065.value], "ocr_validation_agent")
    builder.add_edge(
        FORM_AGENT_MAPPING[FormType.FORM_K_1_1120_S.value], "ocr_validation_agent")
    builder.add_edge(
        FORM_AGENT_MAPPING[FormType.FORM_1095_A.value], "ocr_validation_agent")
    builder.add_edge(
        FORM_AGENT_MAPPING[FormType.FORM_1098_E.value], "ocr_validation_agent")
    builder.add_edge(
        FORM_AGENT_MAPPING[FormType.FORM_1098_T.value], "ocr_validation_agent")
    builder.add_edge(
        FORM_AGENT_MAPPING[FormType.FORM_1098.value], "ocr_validation_agent")
    
    builder.add_edge(FORM_AGENT_MAPPING[FormType.FORM_1099_PATR.value], "ocr_validation_agent")
    builder.add_edge(FORM_AGENT_MAPPING[FormType.FORM_1099_OID.value], "ocr_validation_agent")
    builder.add_edge(FORM_AGENT_MAPPING[FormType.FORM_1099_LTC.value], "ocr_validation_agent")
    builder.add_edge(FORM_AGENT_MAPPING[FormType.FORM_1099_S.value], "ocr_validation_agent")
    # builder.add_edge(FORM_AGENT_MAPPING[FormType.FORM_5498.value], "ocr_validation_agent")
    builder.add_edge(FORM_AGENT_MAPPING[FormType.FORM_5498_SA.value], "ocr_validation_agent")
    builder.add_edge(FORM_AGENT_MAPPING[FormType.FORM_PROPERTY_TAX.value], "ocr_validation_agent")
    builder.add_edge(FORM_AGENT_MAPPING[FormType.FORM_1099_C.value], "ocr_validation_agent")
    builder.add_edge(FORM_AGENT_MAPPING[FormType.FORM_CRYPTO_TAX.value], "ocr_validation_agent")
    builder.add_edge(FORM_AGENT_MAPPING[FormType.FORM_CRYPTO_TAX_SIMPLE.value], "ocr_validation_agent")
    builder.add_edge(FORM_AGENT_MAPPING[FormType.FORM_CRYPTO_TAX_COINBASE.value], "ocr_validation_agent")
    builder.add_edge(FORM_AGENT_MAPPING[FormType.FORM_1099_SA.value], "ocr_validation_agent")
    builder.add_edge(FORM_AGENT_MAPPING[FormType.FORM_RRB_1099_R.value], "ocr_validation_agent")
    builder.add_edge(FORM_AGENT_MAPPING[FormType.FORM_RRB_1099.value], "ocr_validation_agent")

    builder.add_edge("ocr_validation_agent", "validation_agent")


    builder.add_conditional_edges(
            "validation_agent",
            lambda state: state["next"],
            {
                'form_type_finalizer': 'form_type_finalizer',
                "end": END
            }
        )


    graph = builder.compile()
    # print(graph.get_graph().draw_mermaid())
    return graph


def process_tax_form(image_path: str,doc_id: str,page_no: str,page_id: str, predetermined_form_type: Optional[str] = None, page_specific_form_types: list = []) -> tuple[str, dict]:
    """Process a tax form using the workflow"""
    page=Page.objects(file=ObjectId(doc_id), page_index=(int(page_no)+1)).first()
    try:
        workflow = create_tax_form_workflow()
        model = get_llm_provider(DEFAULT_OCR_LLM,{"page_id":page_id})
        form_model = get_llm_provider(DEFAULT_FORM_TYPE_LLM,{"page_id":page_id})

        try:
            with open(image_path, "rb") as image_file:
                image_data = base64.b64encode(
                    image_file.read()).decode("utf-8")
        except Exception as e:
            import traceback
            traceback.print_exc()
            page.update(
                status=QueueProcessingStatus.ERROR.value,
                form_type = predetermined_form_type if isinstance(predetermined_form_type, list) else [predetermined_form_type], 
                message=f"Failed to read image for OpenAI processing: {e}")
            logging.error(f"Failed to read image for OpenAI processing: {e}")
            return [FormType.UNKNOWN.value], [{"error": "Failed to read image file"}], False

        # Initialize state with predetermined form type 
        print('form_type_finalizer at line 1803 in agent',page_specific_form_types)
        initial_state = {
            "messages": [AIMessage(content=json.dumps({"status": "init"}))],
            "admin_determined_form_type": page_specific_form_types if len(page_specific_form_types) > 0 else [FormType.UNKNOWN_BUT_IMPORTANT.value],
            "current_form_type": None,
            "predetermined_form_type": predetermined_form_type,  # Add predetermined form type
            "processed_data": {},
            "next": None,
            "image_data": image_data,
            "image_path": image_path,
            "document_id": doc_id,
            "page_id":page_id,
            "page_number": page_no,
            "identified_forms": [],
            "accumulated_data": [],
            "isSuccess":True,
            "error_message":'',
            "model":model,
            "form_model":form_model

        }

        # Run workflow
        final_state = workflow.invoke(initial_state)
        success_status = final_state.get("isSuccess", False)

        # to be removed
        print(1,final_state.get("t_forms", []))
        print(2,final_state.get("l_forms", []))

        t_form_type = final_state.get("t_forms", [])
        l_form_type = final_state.get("l_forms", [])

        if isinstance(t_form_type, str):
            t_form_type = [t_form_type]
        if isinstance(l_form_type, str):
            l_form_type = [l_form_type]
            
        page.update(
            t_form_type = t_form_type,
            l_form_type = l_form_type
        )

        if not success_status:
            page.update(
                status=QueueProcessingStatus.ERROR.value,
                form_type=predetermined_form_type if isinstance(predetermined_form_type, list) else [predetermined_form_type],
                message=f"Failed to process the form: {final_state.get('error_message', 'Unknown error')}")
            return [FormType.UNKNOWN.value], [{"error": "Failed to process the form"}], False
        
        return final_state["identified_forms"], final_state["accumulated_data"], True
    except Exception as e:
        import traceback
        traceback.print_exc()
        page.update(
                status=QueueProcessingStatus.ERROR.value,
                form_type=predetermined_form_type if isinstance(predetermined_form_type, list) else [predetermined_form_type], 
                message=f"An error occurred while processing the form: {str(e)}")
        return [FormType.UNKNOWN.value], [{"error": f"An error occurred while processing the form: {str(e)}"}], False


# # Example usage
# if __name__ == "__main__":
#     from src.models.config import init_db
#     init_db()

#     data_file = "data.json"

#     form_type, result,isSuccess = process_tax_form(
#         "image copy.png",
#         doc_id='6778defd5957a9db5bb13984',
#         page_no='0',
#         page_id="16g27373789",
#         predetermined_form_type=FormType.FORM_CRYPTO_TAX_COINBASE.value  # Optional parameter
#     )

#     form_type = []

#     for result_ in result:
#         form_type.append(result_["form_details"]["form_type"]["value"])

#     from worker import load_json_file
#     data = load_json_file(data_file)

#     for llm_form_type, result in zip(form_type, result):
#         data.setdefault(llm_form_type, []).append(result) #TODO check this why this twice

#         # write result to file
#         with open(data_file, "w") as f:
#             json.dump(data, f, indent=4)

# validation_agent(    {
#     "processed_data":  {
#   "employer_information": {
#     "employer_identification_number": {
#       "value": "*********",
#       "sequence": None,
#       "type": "einfield"
#     },
#     "employer_name": {
#       "value": "COMMUNITY HIGH SCHOOL DISTRICT 117",
#       "sequence": None,
#       "type": "text"
#     },
#     "employer_address": {
#       "value": "1625 Deep Lake Road",
#       "sequence": None,
#       "type": "text"
#     },
#     "employer_city": {
#       "value": "Lake Villa",
#       "sequence": None,
#       "type": "text"
#     },
#     "employer_state": {
#       "value": "IL",
#       "sequence": None,
#       "type": "text"
#     },
#     "employer_zip_code": {
#       "value": "60046",
#       "sequence": None,
#       "type": "zipcode"
#     }
#   },
#   "form_details": {
#     "form_type": {
#       "value": "Form W-2",
#       "sequence": None,
#       "type": "text"
#     },
#     "calendar_year": {
#       "value": 2024,
#       "sequence": None,
#       "type": "year"
#     },
#     "document_id": {
#       "value": "67d847782824cdc54434bc0d",
#       "sequence": None,
#       "type": "text"
#     },
#     "page_number": {
#       "value": "0",
#       "sequence": None,
#       "type": "text"
#     }
#   },
#   "financial_fields": {
#     "wages_tips_other_compensation": {
#       "value": "450.50",
#       "sequence": None,
#       "type": "currency"
#     },
#     "federal_income_tax_withheld": {
#       "value": "2.13",
#       "sequence": None,
#       "type": "currency"
#     },
#     "social_security_wages": {
#       "value": "0.00",
#       "sequence": None,
#       "type": "currency"
#     },
#     "social_security_tax_withheld": {
#       "value": "0.00",
#       "sequence": None,
#       "type": "currency"
#     },
#     "medicare_wages_and_tips": {
#       "value": "500.00",
#       "sequence": None,
#       "type": "currency"
#     },
#     "medicare_tax_withheld": {
#       "value": "7.25",
#       "sequence": None,
#       "type": "currency"
#     },
#     "social_security_tips": {
#       "value": "0.00",
#       "sequence": None,
#       "type": "currency"
#     },
#     "allocated_tips": {
#       "value": "0.00",
#       "sequence": None,
#       "type": "currency"
#     },
#     "dependent_care_benefits": {
#       "value": "0.00",
#       "sequence": None,
#       "type": "currency"
#     },
#     "nonqualified_plans": {
#       "value": "00",
#       "sequence": None,
#       "type": "text"
#     },
#     "statutory_employee": {
#       "value": False,
#       "sequence": None,
#       "type": "checkbox"
#     },
#     "retirement_plan": {
#       "value": True,
#       "sequence": None,
#       "type": "checkbox"
#     },
#     "third_party_sick_pay": {
#       "value": False,
#       "sequence": None,
#       "type": "checkbox"
#     },
#     "control_number": {
#       "value": "60371",
#       "sequence": None,
#       "type": "text"
#     }
#   },
#   "box_12_filling": {
#     "type": "array",
#     "sequence": None,
#     "value": []
#   },
#   "other_information": {
#     "type": "array",
#     "sequence": None,
#     "value": []
#   },
#   'state_and_local_information': [{
#         'state': 'IL',
#         'employers_state_ID_number': '0805800',
#         'state_wages_tips': '450.50',
#         'state_income_tax': '20.24',
#         'local_wages_tips': '00',
#         'local_income_tax': '00',
#         'locality_name': None
#     }, {
#         'state': 'IL',
#         'employers_state_ID_number': '0805800',
#         'state_wages_tips': '450.50',
#         'state_income_tax': '20.24',
#         'local_wages_tips': '.00',
#         'local_income_tax': '.00',
#         'locality_name': None
#     }],
#   "additional_information": {}
# },
#     "current_form_type": FormType.FORM_W_2.value,
#     "isSuccess": True,
#     "accumulated_data": [],
#     "identified_forms": [
#         FormType.FORM_W_2.value
#     ]
# })

    # print(form_type,len(result),isSuccess)

    # if form_type == FormType.UNKNOWN.value or not result:
    #     print("Failed to process the tax form.")
    #     exit(1)

    # # Load existing data if file exists, else initialize an empty dictionary
    # if os.path.exists(data_file):
    #     with open(data_file, 'r') as f:
    #         data = json.load(f)
    # else:
    #     data = {}

    # # Ensure the data structure for the form type is a list
    # if form_type in data:
    #     data[form_type].append(result)
    # else:
    #     data[form_type] = [result]

    # # Save the updated data back to the file
    # with open(data_file, 'w') as f:
    #     json.dump(data, f, indent=2)

    # print(f"Data for form type '{form_type}' has been saved to '{data_file}'.")

from redis import Redis
from rq import Queue
from src.constants.env_constant import env_var

class RedisProvider:
    def __init__(self):
        self.connection = Redis(
            host=env_var['REDIS_HOST'],
            port=env_var['REDIS_PORT'],
            db=0,
            password=env_var['REDIS_PASSWORD']
        )
        self.queue = Queue(env_var["QUEUE_NAME"], connection=self.connection)
        self.priority_queue = Queue(env_var["PRIORITY_QUEUE_NAME"], connection=self.connection) # @namit add this to env TODO

    def enqueue_task(self, worker_path: str, job_message: dict, job_timeout: int):
        """
        Enqueue a task using the worker path, job message, and timeout.
        """
        return self.queue.enqueue(worker_path, job_message, job_timeout=job_timeout)
    
    def enqueue_priority_task(self, worker_path: str, job_message: dict, job_timeout: int):
        """
        Enqueue a task using the worker path, job message, and timeout.
        """
        return self.priority_queue.enqueue(worker_path, job_message, job_timeout=job_timeout)

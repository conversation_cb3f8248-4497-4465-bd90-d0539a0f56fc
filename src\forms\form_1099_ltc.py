from pydantic import BaseModel, Field
from src.forms.base_form import TextField, YearField, CurrencyField, CheckboxField, FormDetails, ZipCodeField, EINField, DateField


class PayerInformation(BaseModel):
    payer_name: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_street_address: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_city: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_state: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_zip_code: ZipCodeField = Field(
        default_factory=lambda: ZipCodeField(type="zipcode"))
    payer_tin: EINField = Field(default_factory=lambda: EINField(type="einfield"))


class FinancialFields(BaseModel):
    gross_long_term_care_benefits_paid: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    accelerated_death_benefits_paid: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    insured_name: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    insured_street_address: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    insured_city: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    insured_state: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    insured_zip_code_or_foreign_postal_code: ZipCodeField = Field(
        default_factory=lambda: ZipCodeField(type="zipcode"))
    per_diem: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    reimbursed_amount: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    chronically_ill: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    terminally_ill:CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    qualified_contract:CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    date_certifed:DateField = Field(
        default_factory=lambda: DateField(type="date"))


class AdditionalFields(BaseModel):
    corrected: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    omb_number: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    calendar_year: YearField = Field(
        default_factory=lambda: YearField(type="year"))


# Main Pydantic model for Form 1099-LTC
class Form1099LTC(BaseModel):
    payer_information: PayerInformation = Field(
        default_factory=PayerInformation)
    form_details: FormDetails = Field(default_factory=FormDetails)
    financial_fields: FinancialFields = Field(default_factory=FinancialFields)
    additional_fields: AdditionalFields = Field(
        default_factory=AdditionalFields)

import mongoengine as me
from typing import Optional, Any
from src.models.base_model import BaseDocument
from src.models.users import User

class Form(BaseDocument):
    form_name = me.StringField(required=True)
    description = me.StringField(required=False)
    preference = me.IntField(required=False)

    def to_dict(self, exclude_fields: Optional[list] = []) -> dict:
        document = super().to_dict(exclude_fields)
        document["created_at"] = self.created_at.isoformat() if self.created_at else None
        return document

    meta = {
        'collection': 'forms'
    }

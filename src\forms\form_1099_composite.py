from pydantic import BaseModel, Field
from typing import List
from src.forms.base_form import FormDetails, TextField, YearField, CurrencyField, DateField, CheckboxField

class SummaryProceeds(BaseModel):
    term: TextField = Field(default_factory=lambda: TextField(type="text"))
    form_8949_type: TextField = Field(default_factory=lambda: TextField(type="text"))
    proceeds: CurrencyField = Field(default_factory=lambda: CurrencyField(type="currency"))
    cost_basis: CurrencyField = Field(default_factory=lambda: CurrencyField(type="currency"))
    market_discount: CurrencyField = Field(default_factory=lambda: CurrencyField(type="currency"))
    wash_sale_loss_disallowed: CurrencyField = Field(default_factory=lambda: CurrencyField(type="currency"))
    net_gain_or_loss: CurrencyField = Field(default_factory=lambda: CurrencyField(type="currency"))

class DividendsAndDistributions(BaseModel):
    total_ordinary_dividends: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="1a", type="currency"))
    qualified_dividends: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="1b", type="currency"))
    total_capital_gain_distributions: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="2a", type="currency"))
    unrecaptured_section_1250_gain: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="2b", type="currency"))
    section_1202_gain: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="2c", type="currency"))
    collectibles_gain: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="2d", type="currency"))
    section_897_ordinary_dividends: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="2e", type="currency"))
    section_897_capital_gain: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="2f", type="currency"))
    nondividend_distributions: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="3", type="currency"))
    federal_income_tax_withheld: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="4", type="currency"))
    section_199a_dividends: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="5", type="currency"))
    investment_expenses: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="6", type="currency"))
    foreign_tax_paid: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="7", type="currency"))
    foreign_country_or_us_possession: TextField = Field(default_factory=lambda: TextField(sequence="8", type="text"))
    cash_liquidation_distributions: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="9", type="currency"))
    non_cash_liquidation_distributions: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="10", type="currency"))
    exempt_interest_dividends: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="12", type="currency"))
    specified_private_activity_bond_interest_dividends: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="13", type="currency"))

class MiscellaneousIncome(BaseModel):
    royalties: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="2", type="currency"))
    other_income: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="3", type="currency"))
    federal_income_tax_withheld: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="4", type="currency"))
    substitute_payments: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="8", type="currency"))

class Section1256Contracts(BaseModel):
    profit_or_loss_closed_contracts: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="8", type="currency"))
    unrealized_profit_loss_2022: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="9", type="currency"))
    unrealized_profit_loss_2023: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="10", type="currency"))
    aggregate_profit_loss_contracts: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="11", type="currency"))

class InterestIncome(BaseModel):
    interest_income: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="1", type="currency"))
    early_withdrawal_penalty: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="2", type="currency"))
    interest_on_us_savings_bonds: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="3", type="currency"))
    federal_income_tax_withheld: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="4", type="currency"))
    investment_expenses: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="5", type="currency"))
    foreign_tax_paid: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="6", type="currency"))
    foreign_country_or_us_possession: TextField = Field(default_factory=lambda: TextField(sequence="7", type="text"))
    tax_exempt_interest: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="8", type="currency"))
    specified_private_activity_bond_interest: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="9", type="currency"))

class Form1099Composite(BaseModel):
    form_details: FormDetails = Field(default_factory=FormDetails)
    summary_proceeds: List[SummaryProceeds] = Field(default_factory=list)
    dividends_and_distributions: DividendsAndDistributions = Field(default_factory=DividendsAndDistributions)
    miscellaneous_income: MiscellaneousIncome = Field(default_factory=MiscellaneousIncome)
    section_1256_contracts: Section1256Contracts = Field(default_factory=Section1256Contracts)
    interest_income: InterestIncome = Field(default_factory=InterestIncome)

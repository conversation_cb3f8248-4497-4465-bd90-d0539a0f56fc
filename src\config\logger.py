import logging
import os

# Mapping log level strings to logging levels
log_level_mapping = {
    'DEBUG': logging.DEBUG,
    'INFO': logging.INFO,
    'WARNING': logging.WARNING,
    'ERROR': logging.ERROR,
    'CRITICAL': logging.CRITICAL
}

# Read the desired log level from environment variables (default to DEBUG)
log_level_str = os.getenv("LOG_LEVEL", "INFO").upper()
log_level = log_level_mapping.get(log_level_str, logging.DEBUG)

# Create a global logger
logger = logging.getLogger()
logger.setLevel(log_level)

# Formatter for log messages
formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")

# Console handler (prints all messages)
console_handler = logging.StreamHandler()
console_handler.setLevel(log_level)
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)

# Helper to create file handlers for a given level and filename.
def create_file_handler(filename: str, level: int) -> logging.Handler:
    handler = logging.FileHandler(os.path.join(os.getcwd(), filename))
    handler.setLevel(level)
    handler.setFormatter(formatter)
    return handler

# File handlers for each log level.
# Note: Since handlers with a lower level (e.g. DEBUG) will capture higher-level messages too,
# these files may contain duplicate entries. This is expected if you want a separate file per level.
debug_handler = create_file_handler("debug.log", logging.DEBUG)
info_handler = create_file_handler("info.log", logging.INFO)
warning_handler = create_file_handler("warning.log", logging.WARNING)
error_handler = create_file_handler("error.log", logging.ERROR)
critical_handler = create_file_handler("critical.log", logging.CRITICAL)

# Add file handlers to the global logger
logger.addHandler(debug_handler)
logger.addHandler(info_handler)
logger.addHandler(warning_handler)
logger.addHandler(error_handler)
logger.addHandler(critical_handler)

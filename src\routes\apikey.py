import math
from typing import Optional
from bson import ObjectId
from src.dto.output.CommonResponseModel import CommonResponseModel
from fastapi.responses import JSONResponse
from src.dto.input.ApiKeyDTOs import (
    APIKeyCreate,
    APIKeyUpdate
)
from src.models.api<PERSON><PERSON> import APIKey
from src.constants.enum import Tag
from src.config.logger import logging
from src.middleware.rbac_middleware import check_permission
from src.auth.auth_handler import (
    user_jwt_required
)
from fastapi import APIRouter, Query, Depends, status
from fastapi import Request

router = APIRouter(prefix="/v1/api-key")

@router.post("/create", tags=[Tag.API_KEYS.value], status_code=status.HTTP_201_CREATED)
@check_permission("admin::create")
async def create_api_key(request: Request,payload_body: APIKeyCreate, user: dict = Depends(user_jwt_required)):
    """
    Create a new API key.

    Roles with access:
    - Admin: Can create any API key.
    Create a new API key.
    """
    try:
        # Check if the API key already exists in the database
        existing_api_key = APIKey.get(key=payload_body.key)
        if existing_api_key:
            return CommonResponseModel(
                message="API key already exists",
                data=None,
                status_code=status.HTTP_400_BAD_REQUEST
            )
        
        # Create a new API key object
        api_key = APIKey.create(
            key=payload_body.key,
            name=payload_body.name,
            description=payload_body.description,
            is_active=payload_body.is_active
        )

        response = CommonResponseModel(success=True, message="API key created successfully.", data=api_key.to_dict())
        return JSONResponse(status_code=status.HTTP_201_CREATED, content=response.dict())
    
    except Exception as e:
        logging.error(f"Error creating API key: {e}")
        response = CommonResponseModel(
            message="Error creating API key. Please try again later."
        )
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=response.dict()
        )

@router.patch("/update/{api_key_id}", tags=[Tag.API_KEYS.value], status_code=status.HTTP_200_OK)
@check_permission("admin::update")
async def update_api_key(request: Request,api_key_id: str, payload_body: APIKeyUpdate, user: dict = Depends(user_jwt_required)):
    """
    Update an existing API key.

    Roles with access:
    - Admin: Can update any API key.
    Update an existing API key.
    """
    try:

        # Check if the API key exists in the database
        existing_api_key = APIKey.get(id=ObjectId(api_key_id))
        if not existing_api_key:
            return CommonResponseModel(
                message="API key not found",
                data=None,
                status_code=status.HTTP_404_NOT_FOUND
            )
        
        update_payload = {k: v for k,
                    v in payload_body.dict().items() if v is not None}
        
        # Update the language
        flag = APIKey.update_one({"id": ObjectId(api_key_id)}, update_payload)

        if not flag:
            response = CommonResponseModel(message="Failed to update API key")
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content=response.dict()
            )

        updated_api_key = APIKey.get(id=ObjectId(api_key_id))
        response = CommonResponseModel(success=True, message="API key updated successfully.", data=updated_api_key.to_dict())
        return JSONResponse(status_code=status.HTTP_200_OK, content=response.dict())

    except Exception as e:
        logging.error(f"Error updating API key: {e}")
        response = CommonResponseModel(
            message="Error updating API key. Please try again later."
        )
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=response.dict()
        )

@router.get("/api-keys", tags=[Tag.API_KEYS.value], status_code=status.HTTP_200_OK)
@check_permission("admin::read")
async def get_api_keys(
    request: Request,
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    api_key_id: Optional[str] = Query(None, description="Search by API Key Id"),
    name: Optional[str] = Query(None, description="Search by API Key Name"),
):
    """
    Get all API keys.

    Roles with access:
    - Admin: Can view all API keys.
    Get all API keys.
    """
    try:
        filters = {}

        # API Key ID Filter
        if api_key_id:
            filters["_id"] = ObjectId(api_key_id)
        
        # API Key Name Filter
        if name:
            filters["name"] = {"$regex": name, "$options": "i"}
        
        api_keys = APIKey.get_all(skip, limit, filters)

        total_count = APIKey.get_count(filters)

        meta = {
            "api_keys": [key.to_dict() for key in api_keys],
            "count": len(api_keys),
            "total_count": total_count,
            "total_pages": math.ceil(total_count / limit)
        }

        response = CommonResponseModel(success=True, message="API keys retrieved successfully.", data=meta)
        return JSONResponse(status_code=status.HTTP_200_OK, content=response.dict())

    except Exception as e:
        logging.error(f"Error getting API keys: {e}")
        response = CommonResponseModel(
            message="Error getting API keys. Please try again later."
        )
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=response.dict()
        )
    
@router.delete("/delete/{api_key_id}", tags=[Tag.API_KEYS.value], status_code=status.HTTP_200_OK)
@check_permission("admin::delete")
async def delete_api_key(request: Request,api_key_id: str, user: dict = Depends(user_jwt_required)):
    """
    Delete an existing API key.

    Roles with access:
    - Admin: Can delete any API key.
    Delete an existing API key.
    """
    try:
        # Check if the API key exists in the database
        existing_api_key = APIKey.get(id=ObjectId(api_key_id))
        if not existing_api_key:
            return CommonResponseModel(
                message="API key not found",
                data=None,
                status_code=status.HTTP_404_NOT_FOUND
            )

        # Delete the API key
        flag = APIKey.delete(id=ObjectId(api_key_id))

        if not flag:
            response = CommonResponseModel(message="Failed to delete API key")
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content=response.dict()
            )

        response = CommonResponseModel(success=True, message="API key deleted successfully.")
        return JSONResponse(status_code=status.HTTP_200_OK, content=response.dict())

    except Exception as e:
        logging.error(f"Error deleting API key: {e}")
        response = CommonResponseModel(
            message="Error deleting API key. Please try again later."
        )
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=response.dict()
        )

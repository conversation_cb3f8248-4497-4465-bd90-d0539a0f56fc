from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from src.dto.output.CommonResponseModel import CommonResponseModel
from src.auth.auth_handler import decode_jwt
from fastapi.responses import JSONResponse
from src.config.logger import logging
from fastapi import Request, status


class JWTBearer(HTTPBearer):
    """
    A custom HTTP Bearer authentication class that extends FastAPI HTTPBearer
    for JWT token verification.
    """
    def __init__(self, auto_error: bool = True):
        """
        Initialize JWTBearer with an optional auto_error flag.

        Args:
            auto_error (bool): Whether to automatically raise HTTPException
                               on authentication errors. Defaults to True.
        """
        super(J<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__(auto_error=auto_error)

    async def __call__(self, request: Request):
        """
        Extract and validate the JWT token from the request.

        Args:
            request (Request): The FastAPI request object.

        Returns:
            str: The valid JWT token if authentication is successful.

        Raises:
            HTTPException: If authentication fails or the token is invalid.
        """
        credentials: HTTPAuthorizationCredentials = await super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__call__(request)
        if credentials:
            if credentials.scheme != "Bearer":
                response = CommonResponseModel(
                    message="Invalid authentication scheme"
                )
                return JSONResponse(
                    status_code=status.HTTP_403_FORBIDDEN,
                    content=response.dict()
                )
            if not self.verify_jwt(credentials.credentials):
                response = CommonResponseModel(
                    message="Invalid token or expired token"
                )
                return JSONResponse(
                    status_code=status.HTTP_403_FORBIDDEN,
                    content=response.dict()
                )
            return credentials.credentials
        else:
            response = CommonResponseModel(
                message="Invalid authorization code"
            )
            return JSONResponse(
                status_code=status.HTTP_403_FORBIDDEN,
                content=response.dict()
            )

    @staticmethod
    def verify_jwt(token: str) -> bool:
        """
        Verify the validity of the JWT token.

        Args:
            token (str): The JWT token to be verified.

        Returns:
            bool: True if the token is valid, False otherwise.
        """
        try:
            # Attempt to decode the JWT token
            payload = decode_jwt(token)
            # If decoding is successful, the token is considered valid
            return payload is not None
        except Exception as e:
            # Log any exceptions that occur during decoding
            logging.error(f"Token verification failed: {e}")
            return False

import logging
from src.constants.env_constant import env_var
from google.cloud import storage
from datetime import timedelta
import base64
import json

class GoogleBucket:
    def __init__(self):
        # Initialize a storage client
        self.client = storage.Client.from_service_account_info(
            json.loads(
                base64.b64decode(env_var['GOOGLE_SERVICE_ACCOUNT_JSON_ENCODED']).decode('utf-8')
            )
        )

    def generate_pre_signed_url(
        self,
        blob_name: str,
        bucket_name: str,
        content_type: str,
        method_type: str = "GET",
        expiration_time: int = 300
    ) -> str:
        bucket = self.client.bucket(bucket_name)
        blob = bucket.blob(blob_name)

        if method_type != "GET":
            return blob.generate_signed_url(
                version="v4",
                expiration=timedelta(seconds=expiration_time),
                method=method_type,
                content_type=content_type
            )
        else:
            return blob.generate_signed_url(
                version="v4",
                expiration=timedelta(seconds=expiration_time),
                method=method_type
            )

    def delete_file(self, bucket_name: str, blob_name: str) -> bool:
        logging.info(blob_name)
        bucket = self.client.bucket(bucket_name)

        bucket.blob(blob_name).delete()
        logging.info(f"File {blob_name} deleted from bucket {bucket_name}.")
        return True
    
    def delete_folder(self, bucket_name: str, folder_prefix: str) -> bool:
        """
        Delete all blobs in a bucket that have names starting with the folder_prefix.
        :param bucket_name: The name of the bucket.
        :param folder_prefix: The prefix representing the folder (e.g., "111-11-1111/2025/input/")
        :return: True if deletion is successful, False otherwise.
        """
        bucket = self.client.bucket(bucket_name)
        blobs = bucket.list_blobs(prefix=folder_prefix)
        
        deleted_any = False
        for blob in blobs:
            blob.delete()
            logging.info(f"Deleted blob: {blob.name}")
            deleted_any = True
        
        if deleted_any:
            logging.info(f"All blobs with prefix '{folder_prefix}' have been deleted from bucket '{bucket_name}'.")
        else:
            logging.error(f"No blobs found with prefix '{folder_prefix}' in bucket '{bucket_name}'.")
        
        return deleted_any

from pydantic import BaseModel, Field
from src.forms.base_form import EINField, TextField, YearField, CurrencyField, CheckboxField, FormDetails, DateField, ZipCodeField
from typing import List


class PayerInformation(BaseModel):
    payer_name: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_street_address: TextField = Field(
        default_factory=lambda: TextField(type="text"), description="The payer's street address as listed on the form without city, state, zipcode.")
    payer_city: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_state: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_zip_code: ZipCodeField = Field(
        default_factory=lambda: ZipCodeField(type="zipcode"))
    # payer_telephone_number: ContactField = Field(
    #     default_factory=lambda: ContactField(type="contact"))
    payer_tin: EINField = Field(
        default_factory=lambda: EINField(type="einfield"))


class FinancialFields(BaseModel):
    rents: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="1", type="currency"))
    royalties: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="2", type="currency"))
    other_income: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="3", type="currency"))
    federal_income_tax_withheld: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="4", type="currency"))
    fishing_boat_proceeds: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="5", type="currency"))
    medical_and_healthcare_payments: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="6", type="currency"))
    payers_made_direct_sales_of_5000_or_more_of_consumer_products_to_a_buyer_recipient_for_resale: CheckboxField = Field(
        default_factory=lambda: CheckboxField(sequence="7", type="checkbox"))
    substitute_payments_in_lieu_of_dividends_or_interst: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="8", type="currency"))
    crop_insurance_proceeds: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="9", type="currency"))
    gross_proceeds_paid_to_an_attorney: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="10", type="currency"))
    fish_purchased_for_resale: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="11", type="currency"))
    section_409A_deferrals: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="12", type="currency"))
    excess_golden_parachute_payments: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="14", type="currency"))
    non_qualified_deferred_compensation: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="15", type="currency"))


class StateTaxInformation(BaseModel):
    state_tax_withheld: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    st: TextField = Field(default_factory=lambda: TextField(type="text"))
    payers_state_number: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    state_income: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    local_income: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    local_tax_withheld: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    locality_name: TextField = Field(
        default_factory=lambda: TextField(type="text"))


class AdditionalFields(BaseModel):
    corrected: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    fatca_filing_requirement: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    omb_number: TextField = Field(
        default_factory=lambda: TextField(type="text"))


class Form1099MISC(BaseModel):
    payer_information: PayerInformation = Field(
        default_factory=PayerInformation)
    form_details: FormDetails = Field(default_factory=FormDetails)
    financial_fields: FinancialFields = Field(default_factory=FinancialFields)
    state_information: List[StateTaxInformation] = Field(default_factory=list)
    additional_fields: AdditionalFields = Field(
        default_factory=AdditionalFields)

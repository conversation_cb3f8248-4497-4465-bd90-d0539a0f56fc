from postmarker.core import PostmarkClient
from jinja2 import Environment, FileSystemLoader
from src.constants.env_constant import env_var

env = Environment(loader=FileSystemLoader("src/templates"))

class PostmarkMailService:
    def __init__(self):
        self.client = PostmarkClient(server_token=str(env_var["POSTMARK_SERVER_TOKEN"]))
        self.sender = str(env_var["POSTMARK_MAIL_FROM"])

    async def send_mail(self, subject: str, recipients: list[str], template_name: str, context: dict) -> bool:
        """
        Sends an HTML email via Postmark.

        Args:
            subject (str): Email subject line.
            recipients (list[str]): List of recipient email addresses.
            template_name (str): Name of your Jinja2 template file (e.g., "welcome.html").
            context (dict): Variables to render into the template.

        Returns:
            bool: True if Postmark API reports success, False otherwise.
        """
        template = env.get_template(template_name)
        html_body = template.render(**context)

        try:
            response = self.client.emails.send(
                From=self.sender,
                To=",".join(recipients),
                Subject=subject,
                HtmlBody=html_body,
            )
            # Postmark returns HTTP 200 on success
            return response.get("ErrorCode") == 0
        except Exception as exc:
            print(f"Postmark send failed: {exc}")
            return False

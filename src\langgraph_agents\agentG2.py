import json
import logging
from typing import Literal
from langgraph.graph import StateGraph, MessagesState, START, END
from langchain_core.messages import AIMessage
from src.constants.enum import FormType
from copy import deepcopy
from src.constants.env_constant import env_var

class DeduplicationState(MessagesState):
    next: Literal["deduplication_agent", "end"]
    processed_data: dict
    deduplication_results: dict
    file_path:str

def deduplicate_forms(state: DeduplicationState):
    """
    Deduplicates tax forms by comparing all fields.

    Parameters:
      - state: A dictionary (DeduplicationState) that must include:
          * "file_path": Path to the JSON file containing the forms.
          * "processed_data": Initially empty; will be populated with deduplicated data.
          * "deduplication_results": Initially empty; will be updated with summary counts.
    
    Process:
      - Loads data from the file specified by state["file_path"].
      - Iterates over each form type in the data.
      - For any form type with more than one record, compares each form against already recorded unique forms using `compare_forms`.
      - If a duplicate is found (based on field values), it increments a duplicate count and skips adding that form.
      - For form types with one or zero records, it simply records the count without processing.
      - Updates the state with the deduplicated data and results, and sets the next agent to "merge_agent".
    
    What It Checks and Ignores:
      - Checks: It examines every form's content (primarily the "value" field in each key) to decide duplicates.
      - Ignores: Forms with only one record are not processed further. Additionally, in field comparisons, keys such as "document_id", "page_number", "sequence", and "type" are ignored (handled within compare_forms).
    """
    try:
        file_path = state["file_path"]
        # Load the data file
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        deduplication_results = {
            "forms_processed": {},
            "duplicates_removed": {},
            "total_duplicates": 0
        }
        
        # Process each form type
        for form_type, forms in data.items():
            if len(forms) <= 1:
                # Skip if there's only one or no record
                deduplication_results["forms_processed"][form_type] = len(forms)
                continue
                
            # Keep track of unique forms and duplicates
            unique_forms = []
            duplicates_found = 0
            
            for form in forms:
                is_duplicate = False
                
                # Compare with each unique form
                for unique_form in unique_forms:
                    if compare_forms(form, unique_form):
                        duplicates_found += 1
                        is_duplicate = True
                        break
                
                if not is_duplicate:
                    unique_forms.append(form)
            
            # Update the data with deduplicated forms
            data[form_type] = unique_forms
            
            # Record results
            deduplication_results["forms_processed"][form_type] = len(forms)
            deduplication_results["duplicates_removed"][form_type] = duplicates_found
            deduplication_results["total_duplicates"] += duplicates_found
        
        return {
            "messages": [AIMessage(content=json.dumps({
                "status": "success",
                "deduplication_results": deduplication_results
            }))],
            "processed_data": data,
            "deduplication_results": deduplication_results,
            "next": "merge_agent"
        }
        
    except Exception as e:
        return {
            "messages": [AIMessage(content=json.dumps({
                "status": "error",
                "message": str(e)
            }))],
            "processed_data": {},
            "deduplication_results": {"error": str(e)},
            "next": "end"
        }


def merge_documents(state: DeduplicationState):
    """
    Merges pages of documents for specific form types into single consolidated documents.

    Parameters:
      - state: A DeduplicationState dictionary which includes:
          * "processed_data": Dictionary with deduplicated form data.
          * "file_path": Path to the file for saving updated data.
    
    Process:
      - Defines a list of form types (using FormType enum) that need merging.
      - For each form type:
          * If no documents exist, writes the state back to file and continues.
          * Groups pages by document_id and sorts pages by page_number (converted to integer for proper sorting).
          * Calls `combine_pages` to merge sorted pages for each document_id.
          * Updates the state's processed_data with the merged document.
          * Saves the updated data back to file.
      - Returns updated state with next agent set to "end" upon success.
    
    What It Checks and Ignores:
      - Checks: Ensures that for each document group a valid "document_id" exists and that page numbers can be sorted.
      - Ignores: Skips pages without a valid "document_id" and ignores documents for form types not in the specified merge_doc_types list.
    """
    from copy import deepcopy
    import json

    merge_doc_types = [FormType.FORM_K_1_1041.value, FormType.FORM_K_1_1065.value, FormType.FORM_K_1_1120_S.value]


    try:
        all_documents = state["processed_data"]
        documents = deepcopy(all_documents)
        file_path = state["file_path"]

        # Iterate through all document types in merge_doc_types
        for doc_type in merge_doc_types:
            target_documents = documents.get(doc_type)

            if not target_documents or len(target_documents) == 0:
                # If no documents for this type, save state and continue to next type
                try:
                    with open(file_path, 'w') as f:
                        json.dump(state["processed_data"], f, indent=2)
                except Exception as e:
                    raise RuntimeError(f"Failed to write empty documents to file for {doc_type}: {e}")
                continue

            # Helper function to extract document_id and page_number from a form_details object
            def extract_ids(page):
                try:
                    doc_id = page.get("form_details", {}).get("document_id", {}).get("value")
                    page_number = page.get("form_details", {}).get("page_number", {}).get("value")
                    # Ensure page_number is an integer for sorting
                    try:
                        page_number = int(page_number)
                    except (ValueError, TypeError):
                        page_number = float("inf")  # Treat non-numeric page_number as infinite (unsorted last)
                    return doc_id, page_number
                except Exception as e:
                    raise ValueError(f"Error extracting IDs: {e}")

            # Group pages by document_id
            grouped_documents = {}
            try:
                for page in target_documents:
                    doc_id, page_number = extract_ids(page)
                    if not doc_id:
                        continue  # Skip pages without a valid document_id
                    if doc_id not in grouped_documents:
                        grouped_documents[doc_id] = []
                    grouped_documents[doc_id].append((page, page_number))
            except Exception as e:
                raise RuntimeError(f"Error grouping documents for {doc_type}: {e}")

            # Merge pages within each document_id group
            final_documents = []
            try:
                for doc_id, pages_with_numbers in grouped_documents.items():
                    # Sort pages by page_number
                    pages_with_numbers.sort(key=lambda x: x[1])
                    sorted_pages = [page for page, _ in pages_with_numbers]

                    # Combine pages using combine_pages
                    combined_object = combine_pages(sorted_pages)
                    final_documents.append(combined_object)
            except Exception as e:
                raise RuntimeError(f"Error merging documents for {doc_type}: {e}")

            # Save the final documents back to the state and file
            try:
                state["processed_data"][doc_type] = final_documents
                with open(file_path, 'w') as f:
                    json.dump(state["processed_data"], f, indent=2)
            except Exception as e:
                raise RuntimeError(f"Failed to save final documents to file for {doc_type}: {e}")

        return {
            "messages": [AIMessage(content=json.dumps({
                "status": "success",
                "processed_data": state["processed_data"]
            }))],
            "processed_data": state["processed_data"],
            "next": "end"
        }
    except KeyError as e:
        return {
            "messages": [AIMessage(content=json.dumps({
                "status": "error",
                "message": f"Key error in state object: {e}"
            }))],
            "processed_data": state.get("processed_data", {}),
            "next": "end"
        }
    except Exception as e:
        return {
            "messages": [AIMessage(content=json.dumps({
                "status": "error",
                "message": f"An unexpected error occurred: {e}"
            }))],
            "processed_data": state.get("processed_data", {}),
            "next": "end"
        }

def merge_documents_on_same_page(state: DeduplicationState):
    """
    Merges pages that appear on the same page number for a specific form type.

    Parameters:
      - state: DeduplicationState containing:
          * "processed_data": The deduplicated form data.
          * "file_path": The file path where data should be saved.
    
    Process:
      - Focuses only on the form type 'Form W-2' (as specified in merge_doc_types).
      - For each document:
          * Groups pages by document_id and further groups pages by page_number.
          * If multiple pages share the same page number, they are merged via combine_pages.
          * Otherwise, pages are kept as is.
      - Updates the processed_data in the state (saving to file is commented out).
      - Returns updated state with the next step set to "merge_agent".
    
    What It Checks and Ignores:
      - Checks: It verifies that pages have a valid "document_id" and a page number that can be converted for sorting.
      - Ignores: Pages without a valid "document_id" are skipped; the function is tailored only for 'Form W-2' and does not process other types.
    """
    from copy import deepcopy
    import json

    merge_doc_types = [FormType.FORM_W_2.value]

    try:
        all_documents = state["processed_data"]
        documents = deepcopy(all_documents)
        file_path = state["file_path"]

        # Iterate through all document types in merge_doc_types
        for doc_type in merge_doc_types:
            target_documents = documents.get(doc_type)

            if not target_documents or len(target_documents) == 0:
                # If no documents for this type, save state and continue to next type
                try:
                    with open(file_path, 'w') as f:
                        json.dump(state["processed_data"], f, indent=2)
                except Exception as e:
                    raise RuntimeError(f"Failed to write empty documents to file for {doc_type}: {e}")
                continue

            # Helper function to extract document_id and page_number from a form_details object
            def extract_ids(page):
                try:
                    doc_id = page.get("form_details", {}).get("document_id", {}).get("value")
                    page_number = page.get("form_details", {}).get("page_number", {}).get("value")
                    # Ensure page_number is an integer for sorting
                    try:
                        page_number = int(page_number)
                    except (ValueError, TypeError):
                        page_number = float("inf")  # Treat non-numeric page_number as infinite (unsorted last)
                    return doc_id, page_number
                except Exception as e:
                    raise ValueError(f"Error extracting IDs: {e}")

            # Group pages by document_id
            grouped_documents = {}
            try:
                for page in target_documents:
                    doc_id, page_number = extract_ids(page)
                    if not doc_id:
                        continue  # Skip pages without a valid document_id
                    if doc_id not in grouped_documents:
                        grouped_documents[doc_id] = []
                    grouped_documents[doc_id].append((page, page_number))
            except Exception as e:
                raise RuntimeError(f"Error grouping documents for {doc_type}: {e}")

            # Merge pages within each document_id group
            final_documents = []
            try:
                for doc_id, pages_with_numbers in grouped_documents.items():
                    # Sort pages by page_number
                    pages_with_numbers.sort(key=lambda x: x[1])
                    
                    # Group pages by page_number
                    pages_by_number = {}
                    for page, page_number in pages_with_numbers:
                        if page_number not in pages_by_number:
                            pages_by_number[page_number] = []
                        pages_by_number[page_number].append(page)
                    
                    # Process pages grouped by page_number
                    for page_number, pages in pages_by_number.items():
                        if len(pages) > 1:
                            # If multiple pages share the same page number, merge them
                            combined_object = combine_pages(pages)
                            final_documents.append(combined_object)
                        else:
                            # If only one page exists for this page number, add it as is
                            final_documents.append(pages[0])
            except Exception as e:
                raise RuntimeError(f"Error merging documents for {doc_type}: {e}")

            # Save the final documents back to the state and file
            try:
                state["processed_data"][doc_type] = final_documents
                # with open(file_path, 'w') as f:
                #     json.dump(state["processed_data"], f, indent=2)
            except Exception as e:
                raise RuntimeError(f"Failed to save final documents to file for {doc_type}: {e}")

        return {
            "messages": [AIMessage(content=json.dumps({
                "status": "success",
                "processed_data": state["processed_data"]
            }))],
            "processed_data": state["processed_data"],
            "next": "merge_agent"
        }
    except KeyError as e:
        return {
            "messages": [AIMessage(content=json.dumps({
                "status": "error",
                "message": f"Key error in state object: {e}"
            }))],
            "processed_data": state.get("processed_data", {}),
            "next": "end"
        }
    except Exception as e:
        return {
            "messages": [AIMessage(content=json.dumps({
                "status": "error",
                "message": f"An unexpected error occurred: {e}"
            }))],
            "processed_data": state.get("processed_data", {}),
            "next": "end"
        }  

# append page number over here
def combine_pages(pages):
    """
    Combines multiple page dictionaries into a single document.

    Parameters:
      - pages: A list of page dictionaries representing different versions of the same document.
    
    Process:
      - Sets up default values for various field types.
      - Defines a helper function `is_default_field` to determine if a field's value matches a default.
      - Defines `analyze_section` to walk through a section recursively, counting the total fields, OCR (non-default) fields, 
        and default fields.
      - Iterates over all top-level keys (sections) found in the pages.
      - For each section, compares all versions and selects the version with the highest count of OCR fields;
        if none are OCR (i.e. all fields are default), it takes the first encountered version.
      - Returns a merged document that combines the best version of each section.
    
    What It Checks and Ignores:
      - Checks: Looks at the "value" for each field and compares it to defaults based on the field "type".
      - Ignores: If all versions of a section have only default values, the function ignores differences and uses the first version.
    """
    from copy import deepcopy

    # Define default values for each field type
    defaults = {
            "text": ["", None],
            "currency": ["0.00", 0.0, "0.0", None],
            "checkbox": [False, None],
            "year": None,
            "date": None,
            "float": [0.0, None],
            "percentage": [0.0, None],
            "list":[[]]
        }

    # Function to determine if a field has default value
    def is_default_field(field_dict):
        field_type = field_dict.get("type")
        value = field_dict.get("value")
        default_value = defaults.get(field_type)

        if isinstance(default_value, list):
            return value in default_value
        else:
            return value == default_value

    # Function to analyze a section and count OCR and default fields
    def analyze_section(section):
        """
        Walks through a section recursively to count:
          - Total fields.
          - OCR fields (non-default values).
          - Default fields.
          - Counts by data type.
        Returns a dictionary with the analysis result and a status ("ocr" if any OCR fields exist,
        otherwise "default").
        """

        result = {
            "status": "",
            "total_fields": 0,
            "ocr_fields": 0,
            "default_fields": 0,
            "data_types": {}
        }

        def walk(node):
            if isinstance(node, dict):
                if "value" in node and "type" in node:
                    # It's a field
                    result["total_fields"] += 1
                    field_type = node["type"]
                    if not is_default_field(node):
                        result["ocr_fields"] += 1
                        result["data_types"][field_type] = result["data_types"].get(field_type, 0) + 1
                    else:
                        result["default_fields"] += 1
                else:
                    # It's a nested section, recurse
                    for key, value in node.items():
                        walk(value)
            elif isinstance(node, list):
                for item in node:
                    walk(item)

        walk(section)
        result["status"] = "default" if result["ocr_fields"] == 0 else "ocr"
        return result

    # Initialize the final object
    final_object = {}

    # Collect all page_ids from all pages
    all_page_ids = []
    for page in pages:
        if "form_details" in page and "page_id" in page["form_details"]:
            page_id = page["form_details"]["page_id"].get("value", [])
            if isinstance(page_id, list):
                all_page_ids.extend(page_id)
            else:
                all_page_ids.append(page_id)

    # Get all unique top-level keys (sections) from all pages
    top_level_keys = set()
    for page in pages:
        top_level_keys.update(page.keys())

    # Process each section
    for key in top_level_keys:
        section_versions = []
        for page in pages:
            if key in page:
                section_data = page[key]
                # Analyze the section
                analysis = analyze_section(section_data)
                section_versions.append({
                    'data': section_data,
                    'ocr_fields': analysis['ocr_fields'],
                    'total_fields': analysis['total_fields']
                })
            else:
                # Section not present in this page
                pass  # Considered as zero OCR fields

        if not section_versions:
            continue  # No versions of this section found

        # Find the version with the highest number of OCR fields
        max_ocr_fields = max(sv['ocr_fields'] for sv in section_versions)
        if max_ocr_fields == 0:
            # All versions are default; keep default values
            final_object[key] = section_versions[0]['data']
        else:
            # Select the version with the highest OCR fields
            for sv in section_versions:
                if sv['ocr_fields'] == max_ocr_fields:
                    final_object[key] = sv['data']
                    break

    # Special handling for form_details section - merge page_ids
    if "form_details" in final_object and all_page_ids:
        # If page_id doesn't exist, create it
        if "page_id" not in final_object["form_details"]:
            final_object["form_details"]["page_id"] = {
                "value": [],
                "sequence": None,
                "type": "text"
            }
        
        # Remove duplicates and update the page_id field
        unique_page_ids = list(dict.fromkeys(all_page_ids))  # Preserves order while removing duplicates
        final_object["form_details"]["page_id"]["value"] = unique_page_ids

    return final_object

def compare_forms(form1: dict, form2: dict, path: str = "root", ignore_keys: list = None) -> bool:
    """
    Recursively compares two form dictionaries based on their 'value' fields.

    Parameters:
      - form1: The first form dictionary.
      - form2: The second form dictionary.
      - path: (Optional) A string representing the current recursion path (for logging purposes).
      - ignore_keys: (Optional) List of keys to ignore during comparison.
          * Default ignores: ["document_id", "page_number", "sequence", "type"].
    
    Process:
      - If both inputs are dictionaries, checks if they are "value field" dicts (containing "value", "sequence", "type").
        - If yes, compares their "value" fields.
        - Otherwise, compares their keys (ignoring the specified ones) and recurses for each key.
      - For lists/tuples, ensures they are of equal length and compares each element recursively.
      - When encountering a field (a dict containing "value", "sequence", "type"), only the "value" is compared.
      - For numeric comparisons, uses an approximate equality check.
    
    What It Checks and Ignores:
      - Checks: Primarily focuses on comparing the "value" entries in both forms.
      - Ignores: Keys specified in the ignore_keys list (by default "document_id", "page_number", "sequence", "type").
    """
    if ignore_keys is None:
        ignore_keys = ["document_id", "page_number","page_id","form_id", "sequence", "type"]  # Updated default keys to ignore
    
    logging.info(f"\nComparing at path: {path}")
    
    # Check if both are dictionaries
    if isinstance(form1, dict) and isinstance(form2, dict):
        # Check if they are "value field" dicts (have "value", "sequence", "type")
        if (all(k in form1 for k in ["value", "sequence", "type"]) and \
           all(k in form2 for k in ["value", "sequence", "type"])):
            # Compare their 'value' fields
            logging.info(f"Comparing field values at {path}:")
            logging.info(f"Value 1: {form1['value']}")
            logging.info(f"Value 2: {form2['value']}")
            # Recursively compare the 'value' field to handle possible nested structures
            return compare_forms(form1['value'], form2['value'], f"{path}.value", ignore_keys)
        else:
            # Compare keys excluding ignore_keys
            keys1 = set(k for k in form1.keys() if k not in ignore_keys)
            keys2 = set(k for k in form2.keys() if k not in ignore_keys)
            
            if keys1 != keys2:
                logging.info(f"❌ Different keys at {path}")
                logging.info(f"Keys only in form1: {keys1 - keys2}")
                logging.info(f"Keys only in form2: {keys2 - keys1}")
                return False
            
            for key in keys1:
                result = compare_forms(form1[key], form2[key], f"{path}.{key}", ignore_keys)
                if not result:
                    logging.info(f"❌ Mismatch in values for key: {key} at {path}")
                    return False
            return True

    # Check if both are lists or tuples
    elif isinstance(form1, (list, tuple)) and isinstance(form2, (list, tuple)):
        if len(form1) != len(form2):
            logging.info(f"❌ Different lengths at {path}")
            return False
        
        for i, (f1, f2) in enumerate(zip(form1, form2)):
            result = compare_forms(f1, f2, f"{path}[{i}]", ignore_keys)
            if not result:
                logging.info(f"❌ Mismatch in list/tuple at index {i}")
                return False
        return True
    
    # Handle other types (primitives)
    else:
        logging.info(f"Comparing values at {path}:")
        logging.info(f"Value 1: {form1}")
        logging.info(f"Value 2: {form2}")
        
        if isinstance(form1, (int, float)) and isinstance(form2, (int, float)):
            result = abs(form1 - form2) < 1e-10
            logging.info(f"{'✅' if result else '❌'} Numeric comparison result: {result}")
            return result
        
        result = form1 == form2
        logging.info(f"{'✅' if result else '❌'} Direct comparison result: {result}")
        return result


def remove_default_forms(state: DeduplicationState):
    """
    Removes forms from the processed data that contain only default values in all fields.

    Parameters:
      - state: DeduplicationState dictionary including:
          * "processed_data": The deduplicated form data.
          * "file_path": File path to save the filtered documents.
    
    Process:
      - Defines default values for various field types.
      - Uses a helper function `is_default_field` to check if a field's value is default.
      - Uses `analyze_form` to recursively walk through each form and count the total fields versus fields with default values.
      - If all fields in a form are default, the form is removed.
      - Updates the processed data by removing form types that become empty after filtering.
      - Saves the filtered data to file.
      - Returns the updated state and a summary count of removed forms.
    
    What It Checks and Ignores:
      - Checks: Every field in a form (except those nested under "form_details") to decide if it holds a non-default value.
      - Ignores: It deliberately skips the "form_details" section when analyzing for default values.
    """
    try:
        all_documents = state["processed_data"]
        documents = deepcopy(all_documents)
        file_path = state["file_path"]

        # default values 
        defaults = {
            "text": ["", None],
            "currency": ["0.00", 0.0, "0.0", None],
            "checkbox": [False, None],
            "year": None,
            "date": None,
            "float": [0.0, None],
            "percentage": [0.0, None],
            "list": [[]],
            "array": [[]]  
        }

        def is_default_field(field_dict):
            """Check if a field has default value"""
            field_type = field_dict.get("type")
            value = field_dict.get("value")
            default_value = defaults.get(field_type)

            if isinstance(default_value, list):
                result = value in default_value
            else:
                result = value == default_value
            return result

        def analyze_form(form):
            """Analyze a form to determine if all fields are default values"""
            total_fields = 0
            default_fields = 0

            def walk(node, path=""):
                nonlocal total_fields, default_fields
                if isinstance(node, dict):
                    if "value" in node and "type" in node:
                        # Skip form_details fields
                        if "form_details" not in path:
                            total_fields += 1
                            if is_default_field(node):
                                default_fields += 1
                            
                    else:
                        for key, value in node.items():
                            walk(value, f"{path}.{key}" if path else key)
                elif isinstance(node, list):
                    for i, item in enumerate(node):
                        walk(item, f"{path}[{i}]")

            walk(form)
            return total_fields > 0 and total_fields == default_fields

        forms_removed = {}
        form_types_to_remove=[]
        for form_type, forms in documents.items():
            initial_count = len(forms)
            filtered_forms = [form for form in forms if not analyze_form(form)]
            final_count = len(filtered_forms)
            if final_count == 0:
                form_types_to_remove.append(form_type)
            else:
                documents[form_type] = filtered_forms
            
            forms_removed[form_type] = initial_count - final_count
        for form_type in form_types_to_remove:
            del documents[form_type]
        try:
            with open(file_path, 'w') as f:
                json.dump(documents, f, indent=2)
        except Exception as e:
            raise RuntimeError(f"Failed to save filtered documents to file: {e}")

        return {
            "messages": [AIMessage(content=json.dumps({
                "status": "success",
                "forms_removed": forms_removed
            }))],
            "processed_data": documents,
            "next": "merge_1099_b_transaction"
        }

    except Exception as e:
        return {
            "messages": [AIMessage(content=json.dumps({
                "status": "error",
                "message": f"An unexpected error occurred: {e}"
            }))],
            "processed_data": state.get("processed_data", {}),
            "next": "end"
        }

def merge_1099b_forms(state: DeduplicationState):
    """
    Merges consecutive 1099B forms by moving transactions from matching transaction_details elements
    on a lower page to the corresponding element on the next (higher) page, then removes the lower element.
    
    Only consecutive page numbers (e.g. 8 and 9, 9 and 10) are compared. Pairs like 9 and 11 or 11 and 15 are skipped.
    
    Process:
      - Retrieves all 1099B pages from state["processed_data"][FormType.FORM_1099_B.value].
      - Sorts the pages by page number (defaulting to 0 if missing).
      - For each pair of consecutive pages (only if the page numbers differ by 1):
          * Iterates over each transaction_details element in the lower page.
          * For each element, extracts the key fields: type_of_term, type_of_tax_slots, type_of_part, type_of_box, and cusip_number.
          * Searches for a matching transaction_details element in the higher page.
          * If found, appends all transactions from the lower element into the higher element’s transactions list and marks the lower element for removal.
      - After processing, the lower page’s merged transaction_details elements are removed.
      - Finally, state["processed_data"] is updated with the modified pages.
    """
    import json
    logging.info("Starting merge_1099b_forms agent with consecutive page comparison logic.")

    form_key = FormType.FORM_1099_B.value
    file_path=state["file_path"]

    if form_key not in state["processed_data"]:
        logging.info(f"No 1099B forms available under key: {form_key}.")
        return {
            "messages": [AIMessage(content=json.dumps({
                "status": "success",
                "message": "No 1099B forms available to merge."
            }))],
            "next": "end"
        }

    pages = state["processed_data"][form_key]
    logging.info(f"Found {len(pages)} 1099B pages.")

    # print("processed data ======================>>>>>>>>>>>>>>>>", state["processed_data"])
    # Helper to extract page number (default to 0 if missing)
    def extract_page_number(page):
        try:
            num = page.get("form_details", {}).get("page_number", {}).get("value")
            if num is None:
                return 0
            return int(num)
        except Exception as e:
            logging.error(f"Error extracting page number: {e} for page: {page}")
            return 0

    pages.sort(key=extract_page_number)
    logging.info("Pages sorted successfully based on page number.")
    try:
        # Process each pair of consecutive pages (only if the page numbers differ by 1)
        for i in range(len(pages) - 1):
            lower_page = pages[i]
            higher_page = pages[i + 1]
            lower_page_num = extract_page_number(lower_page)
            higher_page_num = extract_page_number(higher_page)
            
            # Only process if pages are consecutive.
            if higher_page_num != lower_page_num + 1:
                logging.info(f"Skipping non-consecutive pages: {lower_page_num} and {higher_page_num}.")
                continue

            logging.info(f"Processing consecutive pages {lower_page_num} and {higher_page_num}.")

            # Retrieve transaction_details arrays from both pages (default to empty list if missing)
            lower_tx_list = lower_page.get("transaction_details", {}).get("value", [])
            higher_tx_list = higher_page.get("transaction_details", {}).get("value", [])
            logging.info(f"Lower page has {len(lower_tx_list)} transaction_details; Higher page has {len(higher_tx_list)} transaction_details.")

            indices_to_remove = []

            # Iterate over each transaction_details element in the lower page
            for idx, lower_tx in enumerate(lower_tx_list):
                # Build key tuple from lower transaction_details element
                lower_key = (
                    lower_tx.get("type_of_term", {}).get("value"),
                    lower_tx.get("type_of_tax_slots", {}).get("value"),
                    lower_tx.get("type_of_part", {}).get("value"),
                    lower_tx.get("type_of_box", {}).get("value"),
                    lower_tx.get("cusip_number", {}).get("value")
                )
                logging.debug(f"Lower page transaction_details index {idx} key: {lower_key}")

                # Look for a matching element in the higher page
                found_match = False
                for htx in higher_tx_list:
                    higher_key = (
                        htx.get("type_of_term", {}).get("value"),
                        htx.get("type_of_tax_slots", {}).get("value"),
                        htx.get("type_of_part", {}).get("value"),
                        htx.get("type_of_box", {}).get("value"),
                        htx.get("cusip_number", {}).get("value")
                    )
                    if lower_key == higher_key:
                        logging.info(f"Match found for lower index {idx} on higher page with key {higher_key}.")
                        # Retrieve transactions from both lower and higher elements (default to empty list)
                        lower_transactions = lower_tx.get("transactions", {}).get("value", [])
                        higher_transactions = htx.get("transactions", {}).get("value", [])
                        logging.info(f"Appending {len(lower_transactions)} transactions from lower element to higher element (currently {len(higher_transactions)}).")
                        # Append lower transactions to higher transactions
                        merged_transactions = higher_transactions + lower_transactions
                        # Update higher element's transactions
                        htx["transactions"] = {
                            "type": "array",
                            "sequence": None,
                            "value": merged_transactions
                        }
                        higher_page["form_details"]["page_id"]["value"] = higher_page["form_details"]["page_id"]["value"] + lower_page["form_details"]["page_id"]["value"]
                        found_match = True
                        break  # One match per lower element is sufficient.
                if found_match:
                    indices_to_remove.append(idx)

            # Remove merged transaction_details elements from the lower page
            if indices_to_remove:
                logging.info(f"Removing {len(indices_to_remove)} merged transaction_details elements from lower page (page {lower_page_num}).")
                lower_tx_list = [tx for idx, tx in enumerate(lower_tx_list) if idx not in indices_to_remove]
                lower_page["transaction_details"] = {
                    "type": "array",
                    "sequence": None,
                    "value": lower_tx_list
                }
            else:
                logging.info(f"No matching transaction_details found on lower page (page {lower_page_num}).")

        # Update state["processed_data"] with the modified 1099B pages
        state["processed_data"][form_key] = pages
        logging.info("Custom merge of 1099B forms completed successfully. State updated accordingly.")

        # print("processed data <<<<<<<<<<<<<<<<<<<<<<<<<<======================", state["processed_data"])
        with open(file_path, 'w') as f:
            json.dump(state["processed_data"], f, indent=2)

        return {
            "messages": [AIMessage(content=json.dumps({
                "status": "success",
                "message": "1099B forms merged successfully based on matching keys and consecutive pages."
            }))],
            "processed_data": state["processed_data"],
            "next": "end"
        }
    
    except Exception as e:
        return {
            "messages": [AIMessage(content=json.dumps({
                "status": "failed",
                "message": "No 1099B forms available to merge."
            }))],
            "next": "end"
        }


def create_deduplication_workflow() -> StateGraph:
    """Create the deduplication workflow graph"""
    builder = StateGraph(DeduplicationState)
    
    # Add nodes
    builder.add_node("deduplication_agent", deduplicate_forms)
    builder.add_node("merge_agent", merge_documents)
    builder.add_node("remove_default_forms_agent", remove_default_forms)
    builder.add_node("merge_1099_b_transaction", merge_1099b_forms)
    # builder.add_node("merge_documents_on_same_page", merge_documents_on_same_page)
    
    # Add edges
    builder.add_edge(START, "deduplication_agent")
    # builder.add_edge("deduplication_agent","merge_documents_on_same_page")
    # builder.add_edge("merge_documents_on_same_page","merge_agent")
    builder.add_edge("deduplication_agent","merge_agent")
    builder.add_edge("merge_agent", "remove_default_forms_agent")
    builder.add_edge("remove_default_forms_agent", 'merge_1099_b_transaction')
    
    # builder.add_edge("merge_documents_on_same_page", END)
    builder.add_edge("merge_agent", END)
    builder.add_edge("remove_default_forms_agent",END)
    builder.add_edge("merge_1099_b_transaction",END)
    
    graph = builder.compile()
    # print(graph.get_graph().draw_mermaid())

    return graph

def run_deduplication(data_file_path):
    """Run the deduplication workflow"""
    try:
        
        workflow = create_deduplication_workflow()
        # Initialize state
        initial_state = {
            "messages": [AIMessage(content=json.dumps({"status": "init"}))],
            "processed_data": {},
            "deduplication_results": {},
            "file_path": data_file_path,
            "next": None
        }
        
        # Run workflow
        final_state = workflow.invoke(initial_state)
        merging_details = final_state["deduplication_results"]
        print(merging_details)
        return final_state["processed_data"]
        
    except Exception as e:
        return {"error": f"An error occurred during deduplication: {str(e)}"}

# if __name__ == "__main__":
#     input_file_path="test.json"
#     with open(input_file_path, 'r') as f:
#             processed_data = json.load(f)
#     results = run_deduplication(input_file_path)
#     # print(json.dumps(results, indent=2))
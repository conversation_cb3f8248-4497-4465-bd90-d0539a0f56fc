# constants.py

from src.constants.enum import LLM_PROVIDER
from src.constants.env_constant import env_var
import os
import logging
# Load environment variables from a .env file if present
from dotenv import load_dotenv

load_dotenv()

# API Configuration
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', 'your-default-api-key')
OPENAI_MODEL = 'gpt-4o'  # Update as per available models

# Paths
NON_MASKED_FOLDER = 'non_masked_folder'
OUTPUTS_FOLDER = 'outputs'

# OCR Configuration
PYTESSERACT_CONFIG = '--oem 3 --psm 6 -l eng'

# Image Processing Parameters
ADAPTIVE_THRESH_BLOCK_SIZE = 15
ADAPTIVE_THRESH_C = -2
VERTICAL_KERNEL_RATIO = 100
MIN_CELL_WIDTH = 40
MIN_CELL_HEIGHT = 20
MAX_CELL_WIDTH_RATIO = 0.9
MAX_CELL_HEIGHT_RATIO = 0.9
BORDER_SIZE = 20

# Regex Patterns
EIN_PATTERN = r'\b\d{2}[- ]?\d{7}\b'
SEQUENCE_NUMBER_PATTERN = r'^\s*\d*[a-zA-Z]?\d*\s*$'

# Output Paths
DEFAULT_OUTPUT_PATH_TEMPLATE = 'outputs/{folder_name}/{file_id}/{file_id}_{page_number}_{form_type}.png'
PROCESSING_FOLDER_TEMPLATE = os.path.join(
    env_var['BASE_DOWNLOAD_DIR'], "{ssn}_{year}")
MASKED_FILES_OUTPUT_FOLDER_TEMPLATE = "{social_security_number}_{financial_year}"

#LLM config
DEFAULT_FORM_TYPE_LLM = LLM_PROVIDER.OPENAI_GPT_4O
DEFAULT_DRAKE_FORM_OCR_LLM = LLM_PROVIDER.GEMINI_1_5_PRO
DEFAULT_OCR_LLM =LLM_PROVIDER.GEMINI_1_5_PRO




BASE_PROMPT = """
                    Extract table data and checkbox statuses from the provided image. 
                    For checkboxes, categorize them under checkboxes with their enabled
                    /disabled status in boolean. Organize table data into valid JSON 
                    format as key-value pairs, focusing solely on values without any 
                    extra text or sequence numbers. Maintain a clean, professional 
                    structure in your output.
                
                    Note:
                        1. Ensure all the checkboxes status should be there if it is in 
                        the image i can be in table or it can be without table.
                        2. Checkbox fields have boxes in it.
                        3. Keys apart from checkboxes for which values are not there assign empty values in them.
                """

STATE_CODE_LIST=['AA','AE','AK','AL','AR','AS','AZ','CA','CO','CT','DC','DE','FL','FM','GA','GU','HI','IA','ID','IL','IN','KS','KY','LA','MA','MD','ME','MH','MI','MN','MO','MP','MS','MT','NC','ND','NE','NH','NJ','NM','NV','NY','OH','OK','OR','PA','PR','PW','RI','SC','SD','TN','TX','UT','VA','VI','VT','WA','WI','WV','WY']
TYPE_OF_GAIN_OR_LOSS_CODE_LIST=['S','L']
FOREIGN_COUNTRY_LIST=['VAR','RIC','AF','AL','AG','AQ','AN','AO','AV','AY','AC','AR','AM','AA','AT','AS','AU','AJ',
                          'BF','BA','BQ','BG','BB','BO','BE','BH','BN','BD','BT','BL','BK','BC','BV','BR','IO','VI','BX','BU','UV','BM','BY',
                          'CB','CJ','CT','CD','CI','CH','KT','IP','CK','CO','CN','CF','CG','CW','CR','CS','IV','HR','CU','UC','CY','EZ',
                          'DA','DX','DJ','DO','DR','TT','EC','EG','ES','EK','ER','EN','ET',
                          'FK','FO','FM','FJ','FI','FR','FS','FP',
                          'GB','GA','GG','GM','GH','GI','GR','GL','GR','GL','GJ','GQ','GT','GK','GV','PU','GY',
                          'HA','HM','VT','HO','HK','HQ','HU',
                          'IC','IN','ID','IR','IZ','EI','IS','IT',
                          'JM','JN','JA','DQ','JE','JQ','JO',
                          'KZ','KE','KQ','KR','KN','KS','KV','KU','KG',
                          'LA','LG','LE','LT','LI','LY','LS','LH','LU',
                          'MC','MK','MA','MI','MY','MV','ML','MT','IM','RM','MR','MP','MF','MX','MQ','MD','MN','MG','MJ','MH','MO','MZ',
                          'WA','NR','BQ','NP','NL','NC','NZ','NU','NG','NI','NE','NF','CQ','NO',
                          'MU','OC','PK','PS','LQ','PM','PP','PF','PA','PE','RP','PC','PL','PO','RQ',
                          'QA',
                          'RO','RS','RW',
                          'TB','RN','WS','SM','TP','SA','SG','RI','SE','SL','SN','NN','LO','SI','BP','SO','SF','SX','SP','PG','CE','SH','SC','ST','SB','VC','SU','NS','SV','WZ','SW','SZ','SY',
                          'TW','TI','TZ','TH','TO','TL','TN','TD','TS','TU','TX','TK','TV',
                          'UG','UP','AE','UK','UY','UZ',
                          'NH','VE','VM','VQ',
                          'WQ','WF','WI',
                          'YM',
                          'ZA','ZI'
                          ]
DIST_CODE_LIST=['1','2','3','4','5','6','7','8','9',
               'A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z'
               ]
UNRECAPTURED_CODE_LIST=['A','B','C','F','D','E','G','H','I']
CREDITS_RECAPTURE_CODE_LIST=['A','B','CA','CB','F','G','H','IA','IB','JA','JB','K','L','M','N','O','Q','Z']
FORM_K1P_OTHER_CODE_LIST=['A','B','C','E','F','E1','G','H','IA','IB','IC','ID','IE','IF','IG','IH','II']
OTHER_DEDUCTIONS_CODE_LIST=['A','B','C','D','E','F','G','H','I','JA','JB','JC','JD','K','L','M','N','O','RA','RB','S','V','WA','WB','WC','WD','WE','WF','X']
CREDITS_CODE_LIST=['A','B','C','D','H','I','J','K','L','MA','MB','N','O','PA','PB','PC','PD','PF','PG','PH','PI','PJ','PK','PL','PM','PN','PO','PP','PQ','PR','PS','PT','PU','PV','PW','PX','PY','PZ']
OTHER_INFORMATION_CODE_LIST=['A','AE','AF','B','F','G','IA','IB','IC','ID','O','P','Q','S']
OTHER_INCOME_CODE_LIST=['A','B','C','F','G','HA','HB','HC','HD','HE','HF','HG','HH']
OTHER_INFORMATION_1120S_CODE_LIST=['A','B','E','F','HA','HB','HC','HD','M','N','O','Q','AA','AB','AC','AD']
W2_CODE_VALUE_LIST=['A','B','C','D','E','F','G','H','J','K','L','M','N','P','Q','R','S','T','V','W','Y','Z',
                     'AA','BB','DD','EE','FF','GG','HH']
INCOMPLETE_FORMS=[]
CAUTION_FILE_PATH=r'src\resources\caution.png'
INVALID_FIELD_PATH=r'src\resources\Cross.png'

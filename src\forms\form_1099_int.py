from pydantic import BaseModel, Field
from typing import List, Optional
from src.forms.base_form import TextField, YearField, CurrencyField, DateField, CheckboxField, FormDetails, ZipCodeField, EINField


class PayerInformation(BaseModel):
    payer_name: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_street_address: TextField = Field(
        default_factory=lambda: TextField(type="text"), description="The payer's street address as listed on the form without city, state, zipcode.")
    payer_city: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_state: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_zip_code: ZipCodeField = Field(
        default_factory=lambda: ZipCodeField(type="zipcode"))
    # payer_telephone_number: ContactField = Field(
    #     default_factory=lambda: ContactField(type="contact"))
    payer_tin: EINField = Field(
        default_factory=lambda: EINField(type="einfield"))
    payer_rtn_optional: Optional[TextField] = Field(
        default_factory=lambda: TextField(type="text"))


class FinancialFields(BaseModel):
    interest_income: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="1", type="currency"))
    early_withdrawal_penalty: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="2", type="currency"))
    interest_on_us_savings_bonds_and_treasury_obligations: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="3", type="currency"))
    federal_income_tax_withheld: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="4", type="currency"))
    investment_expenses: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="5", type="currency"))
    foreign_tax_paid: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="6", type="currency"))
    foreign_country_or_us_possession: TextField = Field(
        default_factory=lambda: TextField(sequence="7", type="text"))
    tax_exempt_interest: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="8", type="currency"))
    specified_private_activity_bond_interest: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="9", type="currency"))
    market_discount: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="10", type="currency"))
    bond_premium: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="11", type="currency"))
    bond_premium_on_treasury_obligations: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="12", type="currency"))
    bond_premium_on_tax_exempt_bond: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="13", type="currency"))
    tax_exempt_and_tax_credit_bond_cusip_no: TextField = Field(
        default_factory=lambda: TextField(sequence="14", type="text"))


class StateTaxInformation(BaseModel):
    state: TextField = Field(
        default_factory=lambda: TextField(sequence="15", type="text"))
    state_identification_number: TextField = Field(default_factory=lambda: TextField(type="text"))
    state_tax_withheld: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="17", type="currency"))


class AdditionalFields(BaseModel):
    corrected: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    fatca_filing_requirement: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    omb_number: TextField = Field(
        default_factory=lambda: TextField(type="text"))

class Totals(BaseModel):
    interest_income: CurrencyField = Field(default_factory=lambda: CurrencyField(type="currency"))
    federal_income_tax_withheld: CurrencyField = Field(default_factory=lambda: CurrencyField(type="currency"))

# Main Pydantic model for Form 1099-INT
class Form1099INT(BaseModel):
    payer_information: PayerInformation = Field(
        default_factory=PayerInformation)
    form_details: FormDetails = Field(default_factory=FormDetails)
    # financial_fields: FinancialFields = Field(default_factory=FinancialFields)
    financial_fields: List[FinancialFields] = Field(default_factory=list, description='List of FinancialFields object')
    totals:Totals = Field(default_factory=Totals)
    state_tax_information: List[StateTaxInformation] = Field(
        default_factory=list)
    additional_fields: AdditionalFields = Field(
        default_factory=AdditionalFields)

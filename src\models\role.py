import json
import os


class Role:
    def __init__(self):
        self.role_json_path = os.path.join(os.getcwd() + "/static/json/roles.json")
        with open(self.role_json_path) as f:
            self.roles = json.load(f)['roles']

    def get_role_by_name(self, name: str):
        return next((role for role in self.roles if role['name'] == name), None)

    def get_roles(self):
        return self.roles

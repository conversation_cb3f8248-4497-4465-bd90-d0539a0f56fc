from pydantic import BaseModel, Field
from src.forms.base_form import <PERSON><PERSON><PERSON>cyField, FormDetails,TextField

class FinancialFields(BaseModel):
    total_1st_installment_payment_or_amount_due: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    total_2nd_installment_payment_or_amount_due: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))

class OtherInformation(BaseModel):
    property_location: TextField = Field(
        default_factory=lambda: TextField(type="text"))
class FormPropertyTax(BaseModel):
    form_details: FormDetails = Field(default_factory=FormDetails)
    financial_fields: FinancialFields = Field(default_factory=FinancialFields)
    other_informations: OtherInformation = Field(default_factory=OtherInformation)

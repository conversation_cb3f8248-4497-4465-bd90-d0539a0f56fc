from typing import Optional, Union, Any  # Import necessary typing tools for type annotations
from pydantic import BaseModel,validator  # Import BaseModel from Pydantic for data validation and serialization


class CommonResponseModel(BaseModel):
    """
    A model for standardizing API responses.

    Attributes:
    - success: Indicates whether the API call was successful (default: False).
    - message: An optional message providing additional details about the response.
    - data: Optional field for including the main payload of the response.
            It can be of any type or a union of multiple types.
    - meta: Optional field for including metadata about the response, such as pagination details.
            It can be of any type or a union of multiple types.
    """
    success: bool = False  # Boolean flag indicating the success of the API call
    message: Optional[str] = None  # Optional message string, default is None
    data: Optional[Union[Any, Any]] = None  # Optional field for response data, flexible in type
    meta: Optional[Union[Any, Any]] = None  # Optional field for metadata, flexible in type

    @validator('data')
    def validate_data(cls, v):
        if not v:
            return None
        return v

    def to_dict(self):
        """
        Convert the CommonResponseModel instance to a dictionary.

        Returns:
            dict: A dictionary representation of the response model.
        """
        return {
            'success': self.success,
            'message': self.message,
            'data': self.data,
            'meta': self.meta
        }

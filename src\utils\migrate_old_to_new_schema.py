#!/usr/bin/env python3
import os
import argparse
from datetime import datetime
from pymongo import MongoClient, InsertOne
from bson import ObjectId
from src.constants.env_constant import env_var

BATCH_SIZE = 1000

def bulk_insert_documents(src_cursor, transform_fn, dst_collection, batch_size=BATCH_SIZE, recreate_indexes_fn=None):
    # Drop indexes if a function is provided (useful for heavy insertions)
    if recreate_indexes_fn:
        dst_collection.drop_indexes()
    ops = []
    count = 0
    for doc in src_cursor:
        new_doc = transform_fn(doc)
        ops.append(InsertOne(new_doc))
        count += 1
        if count % batch_size == 0:
            dst_collection.bulk_write(ops, ordered=False)
            ops.clear()
    if ops:
        dst_collection.bulk_write(ops, ordered=False)
    if recreate_indexes_fn:
        recreate_indexes_fn(dst_collection)
    print(f"Inserted {count} documents into collection '{dst_collection.name}' in batches of {batch_size}")

# Transformation functions for each collection

def transform_admin_to_user(admin):
    # Using admin's username (if any) else email as name.
    return {
        "_id": admin["_id"],
        "name": admin.get("username") or admin["email"],
        "email": admin["email"],
        "phone_number": None,         # Not available from admin doc
        "password": admin["password"],
        "profile_image": None,
        "roles": ["user"],            # new schema default role; adjust if needed
        "invite_status": "pending",   # default per new schema
        "invited_at": admin.get("created_at") or datetime.utcnow(),
        "invite_expires_at": None,
        "is_active": True,           # Keeping same as your original migration
        "last_updated_by": None,
        "is_two_factor_auth": True,
        "two_factor_auth_type": "EMAIL",
        "max_clients_upload_at_a_time": env_var["DEFAULT_MAX_CLIENTS_UPLOAD_AT_A_TIME"],
        "max_priority_clients_to_process_at_a_time": env_var["DEFAULT_MAX_PRIORITY_CLIENTS_TO_PROCESS_AT_A_TIME"],
        "created_at": admin.get("created_at"),
        "updated_at": admin.get("updated_at")
    }

def transform_old_user_to_client(old_user, default_admin_id):
    return {
        "_id": old_user["_id"],
        "name": old_user["name"],
        "social_security_number": old_user["social_security_number"],
        "queue_status": old_user.get("queue_status", "queued"),
        "message": old_user.get("message"),
        "financial_year": old_user["financial_year"],
        "output_path": old_user.get("output_path"),
        "drake_status": old_user.get("drake_status", "waiting"),
        "user": ObjectId(default_admin_id),  # Reference to a default User (admin) migrated above
        "last_updated_by": None,
        # New schema defaults
        "is_review_required": False,
        "is_reviewed": True,
        "is_aborted": False,
        "is_priority": False,
        "created_at": old_user.get("created_at"),
        "updated_at": old_user.get("updated_at")
    }

def transform_file(file):
    return {
        "_id": file["_id"],
        "file_name": file["file_name"],
        "input_file_path": file["input_file_path"],
        "masked_file_path": file["masked_file_path"],
        "message": file.get("message"),
        "status": file.get("status", "queued"),
        "client": file["user"],  # Assumes file["user"] is the old client _id
        "form_types_per_page": file.get("form_types_per_page"),
        "last_updated_by": None,
        "created_at": file.get("created_at"),
        "updated_at": file.get("updated_at")
    }

def transform_page(page):
    return {
        "_id": page["_id"],
        "page_index": page["page_index"],
        "form_type": page["form_type"],
        "t_form_type": page.get("t_form_type"),
        "l_form_type": page.get("l_form_type"),
        "message": page["message"],
        "status": page.get("status", "queued"),
        "file": page["file"],  # Reference remains the same
        "last_updated_by": None,
        "created_at": page.get("created_at"),
        "updated_at": page.get("updated_at")
    }

# Index recreation function for pages (example)
def recreate_pages_indexes(collection):
    collection.create_index([("file", 1)])
    collection.create_index([("status", 1)])

def rename_db(source_db_name, target_db_name, mongo_uri):
    client = MongoClient(mongo_uri)
    src_db = client[source_db_name]
    target_db = client[target_db_name]

    # Iterate through all collections in the source db
    for collection_name in src_db.list_collection_names():
        source_collection = src_db[collection_name]
        target_collection = target_db[collection_name]
        # Copy all documents from source collection to target collection
        docs = list(source_collection.find({}))
        if docs:
            target_collection.insert_many(docs)
        print(f"Copied collection '{collection_name}'")
    
    # Optionally drop the source database after successful migration
    client.drop_database(source_db_name)
    print(f"Database renamed from '{source_db_name}' to '{target_db_name}'")


if __name__ == "__main__":
    source_db = "monotelo-phase2"
    target_db = "monotelo-phase2-migrated"
    mongo_uri = env_var["MONGO_URI"]

    client = MongoClient(mongo_uri)
    src = client[source_db]
    dst = client[target_db]

    # 1️⃣ Migrate admins to new Users collection in bulk.
    # Capture a default admin _id to use as reference for clients.
    admin_cursor = src.admins.find()
    admin_docs = list(admin_cursor)
    default_admin_id = None
    if admin_docs:
        default_admin_id = admin_docs[0]["_id"]
    else:
        raise Exception("No admin documents found in source db to reference for client migration.")

    def transform_admin(admin):
        return transform_admin_to_user(admin)
    bulk_insert_documents(iter(admin_docs), transform_admin, dst.users)

    # 2️⃣ Migrate old Users to new Clients collection in bulk.
    old_user_cursor = src.users.find()
    bulk_insert_documents(old_user_cursor,
                          lambda doc: transform_old_user_to_client(doc, default_admin_id),
                          dst.clients)

    # 3️⃣ Migrate Files collection in bulk.
    files_cursor = src.files.find()
    bulk_insert_documents(files_cursor, transform_file, dst.files)

    # 4️⃣ Migrate Pages collection in bulk with index recreation.
    pages_cursor = src.pages.find()
    bulk_insert_documents(pages_cursor, transform_page, dst.pages, batch_size=BATCH_SIZE, recreate_indexes_fn=recreate_pages_indexes)

    print(f"✅ Bulk migration complete from `{source_db}` to `{target_db}`")

    # client.drop_database(source_db)

    # rename_db(source_db_name=target_db, target_db_name=source_db, mongo_uri=mongo_uri)

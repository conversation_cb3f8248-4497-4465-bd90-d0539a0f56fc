from typing import Optional
from src.models.users import User
from src.models.files import File
from src.constants.enum import QueueProcessingStatus
from src.models.base_model import BaseDocument
import mongoengine as me


class Page(BaseDocument):
    page_index = me.IntField(required=True)
    form_type = me.ListField(me.StringField(), required=True)
    t_form_type = me.ListField(me.StringField(), required=False)
    l_form_type = me.ListField(me.StringField(), required=False)
    message = me.StringField(required=True)
    status = me.StringField(
        choices=[status.value for status in QueueProcessingStatus],
        default=QueueProcessingStatus.QUEUED.value
    )
    # Reference to the File model
    file = me.ReferenceField(File, reverse_delete_rule=me.CASCADE, required=True)

    # Maintain user who updated the document last
    last_updated_by = me.ReferenceField(User, required=False)

    # Maintain a dictoray of form ids with their filling status and error messages
    forms_status = me.DictField(required=False, default={})

    def to_dict(self, exclude_fields: Optional[list] = []) -> dict:
        document = super().to_dict(exclude_fields)
        document["status"] = self.status
        # Include file ID in output
        document["file_id"] = str(self.file["id"]) if self.file else None
        return document

    meta = {
        'collection': 'pages'
    }

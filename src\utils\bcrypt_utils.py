import bcrypt

def get_hash_text(text: str) -> str:
    # Generate a salt and hash the password
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(text.encode('utf-8'), salt)
    return hashed.decode('utf-8')

def get_random_salt() -> str:
    # Generate a random salt
    return bcrypt.gensalt().decode('utf-8')

def verify_text(text: str, hashed: str) -> bool:
    # Check if the provided password matches the hashed password
    return bcrypt.checkpw(text.encode('utf-8'), hashed.encode('utf-8'))

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any

class FormCreateRequest(BaseModel):
    form_name: str = Field(..., description="Name of the form")
    description: Optional[str] = Field(None, description="Description of the form")

class FormUpdateRequest(BaseModel):
    form_id: str = Field(..., description="ID of the form to update")
    form_name: Optional[str] = Field(None, description="Updated form name")
    description: Optional[str] = Field(None, description="Updated description")

class FormFilterRequest(BaseModel):
    form_name: Optional[str] = Field(None, description="Filter forms by name")

from pydantic import BaseModel, Field, validator
from typing import Optional

class APIKeyDTO(BaseModel):
    key: str = Field(..., min_length=32, max_length=64)
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    is_active: bool = Field(True)

    @validator('key')
    def validate_key(cls, v):
        if not v.isalnum():
            raise ValueError('API key must be alphanumeric')
        return v

class APIKeyCreate(APIKeyDTO):
    pass

class APIKeyUpdate(BaseModel):
    key: Optional[str] = Field(None, min_length=32, max_length=64)
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    is_active: Optional[bool] = None

    @validator('key')
    def validate_key(cls, v):
        if v is None:
            return v
        if not v.isalnum():
            raise ValueError('API key must be alphanumeric')
        return v
from typing import Optional
from datetime import datetime
from src.constants.enum import InviteStatus, TwoFactorAuthTypes, UserRole
from src.models.base_model import BaseDocument
from src.constants.env_constant import env_var
import mongoengine as me

class SoftDeleteQuerySet(me.QuerySet):
    def delete(self, *args, **kwargs):
        # Soft‑delete all matched documents
        return super().update(is_active=False, updated_at=datetime.utcnow())

    def hard_delete(self, *args, **kwargs):
        # Permanently remove
        return super().delete(*args, **kwargs)

    def update(self, **kwargs):
        # Prevent updating deleted docs via queryset
        if self._query.get("is_active") is False:
            raise me.ValidationError("Cannot update a in-active User")
        return super().update(**kwargs)

class User(BaseDocument):
    name = me.StringField(required=True)
    email = me.StringField(required=True, unique=True)
    phone_number = me.StringField(required=False)
    password = me.StringField(required=True)
    profile_image = me.StringField(required=False)

    drake_user_id = me.StringField(required=False)

    roles = me.ListField(
        me.StringField(choices=[role.value for role in UserRole]),
        default=lambda: [UserRole.USER.value]
    )

    invite_status = me.StringField(
        default=InviteStatus.PENDING.value,
        choices=[status.value for status in InviteStatus]
    )

    invited_at = me.DateTimeField(default=datetime.utcnow)
    invite_expires_at = me.DateTimeField()

    is_active = me.BooleanField(default=True, required=True)
    last_updated_by = me.ReferenceField("self")

    is_two_factor_auth = me.BooleanField(default=True)
    two_factor_auth_type = me.StringField(default=TwoFactorAuthTypes.EMAIL.value,
        choices=[type.value for type in TwoFactorAuthTypes]
    )

    max_clients_upload_at_a_time = me.IntField(default=env_var["DEFAULT_MAX_CLIENTS_UPLOAD_AT_A_TIME"])
    max_priority_clients_to_process_at_a_time = me.IntField(default=env_var["DEFAULT_MAX_PRIORITY_CLIENTS_TO_PROCESS_AT_A_TIME"])

    meta = {
        "collection": "users",
        # "queryset_class": SoftDeleteQuerySet,
        "indexes": [{"fields": ["email"], "unique": True}],
    }

    # def save(self, *args, **kwargs):
    #     self.updated_at = datetime.utcnow()
    #     return super().save(*args, **kwargs)

    # def delete(self, *args, **kwargs):
    #     if not self.is_active:
    #         raise me.ValidationError("User is already inactive")
    #     self.is_active = False
    #     self.updated_at = datetime.utcnow()
    #     return super().save(*args, **kwargs)

    # def hard_delete(self, *args, **kwargs):
    #     return super().delete(*args, **kwargs)

    def to_dict(self, exclude_fields: Optional[list] = []) -> dict:
        return super().to_dict(exclude_fields)

from enum import Enum
from pydantic import BaseModel, Field
from typing import List, Optional
from src.forms.base_form import TextField, YearField, CurrencyField, CheckboxField, FormDetails, ZipCodeField, EINField, DateField


class PlanTypeEnum(str, Enum):
    ira = "ira"
    sep = "sep"
    simple = "simple"
    roth_ira = "roth_ira"

class TypeOfPlan(BaseModel):
    plan_type: PlanTypeEnum = Field(...)
    
class TrusteeOrIssuerInformation(BaseModel):
    trustee_or_issuer_name: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    trustee_or_issuer_street_address: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    trustee_or_issuer_city: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    trustee_or_issuer_state: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    trustee_or_issuer_zip_code: ZipCodeField = Field(
        default_factory=lambda: ZipCodeField(type="zipcode"))
    trustee_or_issuer_tin: EINField = Field(default_factory=lambda: EINField(type="einfield"))

class FinancialFields(BaseModel):

    ira_contributions: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    rollover_contributions: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    roth_ira_conversion_amount: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    recharacterized_contributions: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    fmv_of_account: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    life_insurance_cost_included_in_box_1: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    sep_contributions: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    simple_contributions: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    roth_ira_contributions: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    rmd_date: CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    rmd_amount:CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    postponed_or_late_contributions:CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    type_of_plan: TypeOfPlan = Field(default_factory=TypeOfPlan)
    repayments:CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    fmv_of_certain_specified_assets:CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    earnings:CurrencyField = Field(
        default_factory= lambda:CurrencyField(type="currency")
    )
    box_14_code: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    rmd_date: DateField= Field(
        default_factory= lambda:DateField(type="date")
    )
    ira:CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    sep: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    simple: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    roth_ira: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    checked_if_required_minimum_distributions: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    


class Box13Information(BaseModel):
    year: YearField = Field(
        default_factory=lambda: YearField(type="Year"))
    code: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    
class Box15Information(BaseModel):
    code: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    

class AdditionalFields(BaseModel):
    corrected: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    omb_number: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    calendar_year: YearField = Field(
        default_factory=lambda: YearField(type="year"))


# Main Pydantic model for Form 1099-LTC
class Form5498(BaseModel):
    trustee_or_issuer_information: TrusteeOrIssuerInformation = Field(
        default_factory=TrusteeOrIssuerInformation)
    form_details: FormDetails = Field(default_factory=FormDetails)
    financial_fields: FinancialFields = Field(default_factory=FinancialFields)
    box_13_information: List[Box13Information] = Field(default_factory=list)
    box_15_information: List[Box15Information] = Field(default_factory=list)
    additional_fields: AdditionalFields = Field(
        default_factory=AdditionalFields)

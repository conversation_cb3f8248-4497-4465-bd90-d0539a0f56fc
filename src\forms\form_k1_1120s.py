from pydantic import BaseModel, Field, condecimal
from typing import List, Optional
from src.forms.base_form import TextField, CurrencyField, CheckboxField, YearField, DateField, BooleanField, PercentageField, FloatField, FormDetails, ZipCodeField, EINField


class ShareholderInformation(BaseModel):
    current_year_allocation_percentage: PercentageField = Field(
        default_factory=lambda: PercentageField(
            sequence="Part II-G", type="percentage")
    )
    shareholder_number_of_share_beginning_of_tax_year: TextField = Field(
        default_factory=lambda: TextField(sequence="Part II-H", type="text"))
    shareholder_number_of_share_ending_of_tax_year: TextField = Field(
        default_factory=lambda: TextField(sequence="Part II-H", type="text"))
    loans_from_shareholder_beginning_of_tax_year: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="Part II-I", type="currency"))
    loans_from_shareholder_ending_of_tax_year: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="Part II-I", type="currency"))


class CorporationInformation(BaseModel):
    corporation_employer_identification_number: EINField = Field(default_factory=lambda: EINField(type="einfield"))
    corporation_name: TextField = Field(
        default_factory=lambda: TextField(sequence="Part I-B", type="text"))
    corporation_address: TextField = Field(
        default_factory=lambda: TextField(sequence="Part I-B", type="text"), description="The corporation's street address as listed on the form without city, state, zipcode.")
    corporation_city: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    corporation_state: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    corporation_zip: ZipCodeField = Field(
        default_factory=lambda: ZipCodeField(type="zipcode"))

    irs_center_where_corporation_filed_return: TextField = Field(
        default_factory=lambda: TextField(sequence="Part I-C", type="text"))
    corporation_total_shares_beginning_of_tax_year: TextField = Field(
        default_factory=lambda: TextField(sequence="Part I-D", type="text"))
    corporation_total_shares_ending_of_tax_year: TextField = Field(
        default_factory=lambda: TextField(sequence="Part I-D", type="text"))


class OtherIncome(BaseModel):
    other_income_code: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    other_income_amount: TextField = Field(
        default_factory=lambda: TextField(type="text"),description="If value is present inside bracket then provide the value inside bracket")


class OtherDeductions(BaseModel):
    other_deductions_code: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    other_deductions_amount: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))


class ItemsAffectingShareholderBasis(BaseModel):
    items_affecting_shareholder_basis_code: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    items_affecting_shareholder_basis_amount: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))


class OtherInformation(BaseModel):
    other_information_code: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    other_information_amount: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))


class ItemsAffectingShareholderBasis(BaseModel):
    items_affecting_shareholder_basis_code: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    items_affecting_shareholder_basis_amount: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))


class OtherInformation(BaseModel):
    other_information_code: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    other_information_amount: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))


class CreditInfo(BaseModel):
    credits_code: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    credits_amount: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))


class AlternativeMinimumTaxItems(BaseModel):
    alternative_minimum_tax_items_code: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    alternative_minimum_tax_items_amount: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))


class ShareholderIncomeDeductionCreditDetails(BaseModel):
    ordinary_business_income_loss: TextField = Field(
        default_factory=lambda: TextField(type="text"),description="If value is present inside bracket then provide the value inside bracket")
    net_rental_real_estate_income_loss: TextField = Field(
        default_factory=lambda: TextField(type="text"),description="If value is present inside bracket then provide the value inside bracket")
    other_net_rental_income_loss: TextField = Field(
        default_factory=lambda: TextField(type="text"),description="If value is present inside bracket then provide the value inside bracket")
    interest_income: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="Part III-4", type="currency"))
    ordinary_dividends: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="Part III-5a", type="currency"))
    qualified_dividends: CurrencyField = Field(
        default_factory=lambda: CurrencyField(
            sequence="Part III-5b", type="currency")
    )
    royalties: CurrencyField = Field(default_factory=lambda: CurrencyField(
        sequence="Part III-6", type="currency"))
    net_short_term_capital_gain_loss: TextField = Field(
        default_factory=lambda: TextField(type="text"),description="If value is present inside bracket then provide the value inside bracket")
    net_long_term_capital_gain_loss: TextField = Field(
        default_factory=lambda: TextField(type="text"),description="If value is present inside bracket then provide the value inside bracket")
    collectibles_28_percent_gain_loss: TextField = Field(
        default_factory=lambda: TextField(type="text"),description="If value is present inside bracket then provide the value inside bracket")
    unrecaptured_section_1250_gain: CurrencyField = Field(
        default_factory=lambda: CurrencyField(
            sequence="Part III-8c", type="currency")
    )
    net_section_1231_gain_loss: TextField = Field(
        default_factory=lambda: TextField(type="text"),description="If value is present inside bracket then provide the value inside bracket")
    other_income_loss: List[OtherIncome] = Field(default_factory=list)
    section_179_deduction: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="Part III-11", type="currency"), description="Only Field Under Section 179 Deduction"
    )
    other_deductions: List[OtherDeductions] = Field(
        default_factory=list, description="All Fields Under Other Deductions")
    credits: List[CreditInfo] = Field(default_factory=list)
    alternative_minimum_tax_items: List[AlternativeMinimumTaxItems] = Field(
        default_factory=list)
    items_affecting_shareholder_basis: List[ItemsAffectingShareholderBasis] = Field(
        default_factory=list)
    other_information: List[OtherInformation] = Field(
        default_factory=list, description="All Fields Under Other Information")
    schedule_k3_attached: CheckboxField = Field(
        default_factory=lambda: CheckboxField(
            sequence="Part III-14", type="checkbox")
    )
    more_than_one_activity_for_at_risk_purposes: CheckboxField = Field(
        default_factory=lambda: CheckboxField(
            sequence="Part III-18", type="checkbox")
    )
    more_than_one_activity_for_at_passive_activity_purposes: CheckboxField = Field(
        default_factory=lambda: CheckboxField(
            sequence="Part III-19", type="checkbox")
    )

# Main Pydantic model for Schedule K-1 (Form 1120-S)
class AdditionalFields(BaseModel):
    period_begin: YearField = Field(
        default_factory=lambda: YearField(type="year"))
    period_end: YearField = Field(
        default_factory=lambda: YearField(type="year"))
    check_final_k1: CheckboxField = Field(
        default_factory=lambda: CheckboxField(sequence="Final K-1", type="checkbox"))
    check_amended_k1: CheckboxField = Field(
        default_factory=lambda: CheckboxField(sequence="Amended K-1", type="checkbox"))
    

class FormK1_1120S(BaseModel):
    form_details: FormDetails = Field(default_factory=FormDetails)
    additional_fields: AdditionalFields = Field(default_factory=AdditionalFields)
    corporation_information: CorporationInformation = Field(
        default_factory=CorporationInformation)
    shareholder_information: ShareholderInformation = Field(
        default_factory=ShareholderInformation)
    shareholder_income_details: ShareholderIncomeDeductionCreditDetails = Field(
        default_factory=ShareholderIncomeDeductionCreditDetails)

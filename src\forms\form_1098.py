from typing import List
from pydantic import BaseModel, Field
from src.forms.base_form import EINField, TextField, DateField, CurrencyField, CheckboxField, FormDetails, ZipCodeField


class RecipientInformation(BaseModel):
    recipient_lender_name: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    recipient_lender_street_address: TextField = Field(
        default_factory=lambda: TextField(type="text"), description="The recipient’s street address as listed on the form without city, state, zipcode.")
    recipient_lender_city: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    recipient_lender_state: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    recipient_lender_zip_code: ZipCodeField = Field(
        default_factory=lambda: ZipCodeField(type="zipcode"))
    # recipient_lender_telephone_number: ContactField = Field(
    #     default_factory=lambda: ContactField(type="contact"))
    recipient_lender_tin: EINField = Field(
        default_factory=lambda: EINField(type="einfield"))


class MortgageDetails(BaseModel):
    mortgage_interest_received: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="1", type="currency"))
    outstanding_mortgage_principal: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="2", type="currency"))
    mortgage_origination_date: DateField = Field(
        default_factory=lambda: DateField(type="date", sequence="3"))
    refund_of_overpaid_interest: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="4", type="currency"))
    mortgage_insurance_premiums: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="5", type="currency"))
    points_paid: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="6", type="currency"))
    property_address_same_as_payer: CheckboxField = Field(default_factory=lambda: CheckboxField(
        type="checkbox", sequence="7"), description="Extract if the checkbox is marked (checked/selected/filled) or empty/blank/unchecked. Return true if checked, false if unchecked. Do not make assumptions - only mark true if you can clearly see the checkbox is filled/marked/checked in the image.")
    property_address: TextField = Field(
        default_factory=lambda: TextField(type="text", sequence="8"), description="Address as listed on the form without city, state, zipcode.")
    property_address_city: TextField = Field(
        default_factory=lambda: TextField(type="text", sequence="8"))
    property_address_state: TextField = Field(
        default_factory=lambda: TextField(type="text", sequence="8"))
    property_address_zip: ZipCodeField = Field(
        default_factory=lambda: ZipCodeField(type="zipcode", sequence="8"))
    number_of_properties: TextField = Field(
        default_factory=lambda: TextField(type="text", sequence="9"))
    mortgage_acquisition_date: DateField = Field(
        default_factory=lambda: DateField(type="date", sequence="11"))


class OtherProperties(BaseModel):
    other_text: TextField = Field(default_factory=lambda: TextField(
        type="text"), description="collect data from box named *Other*/ only. Do not collect data from any other boxes")
    other_amount: CurrencyField = Field(default_factory=lambda: CurrencyField(
        type="currency"), description="collect data from box named *Other*/ only. Do not collect data from any other boxes")


class AdditionalFields(BaseModel):
    corrected: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    omb_number: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    real_estate_taxes_paid: CurrencyField = Field(default_factory=lambda: CurrencyField(
        type="currency"), description="find amount of property taxes paid on this page and fill this field")
    other: List[OtherProperties] = Field(
        default_factory=list, description="collect data from box named *Other*/ only. Do not collect data from any other boxes")
    primary_residence: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox", sequence="7"))


# Main Pydantic model for Form 1098
class Form1098(BaseModel):
    form_details: FormDetails = Field(default_factory=FormDetails)
    recipient_information: RecipientInformation = Field(
        default_factory=RecipientInformation)
    mortgage_details: MortgageDetails = Field(default_factory=MortgageDetails)
    additional_fields: AdditionalFields = Field(
        default_factory=AdditionalFields)

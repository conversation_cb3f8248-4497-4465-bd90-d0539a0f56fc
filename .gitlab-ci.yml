stages:
  #  - sonarqube-check
  - build_and_deploy
  - build_and_deploy_uat
  - build_and_deploy_prod
  # - notify_build
  # - deploy

build_and_deploy:
  stage: build_and_deploy
  image: ubuntu:latest
  script:
    - apt-get update && apt-get install -y openssh-client
    - mkdir /root/.ssh
    - echo "$SSH_PRIVATE_KEY_DEV" | tr -d '\r' > /root/.ssh/id_rsa
    - chmod 400 /root/.ssh/id_rsa
    - ssh-keyscan -t rsa ************** >> ~/.ssh/known_hosts
    - scp -i "/root/.ssh/id_rsa" -o StrictHostKeyChecking=no ./deploy_script.sh ranjith093@**************:./
    - ssh -i "/root/.ssh/id_rsa" -o StrictHostKeyChecking=no ranjith093@************** "bash -s" < ./deploy_script.sh "${CI_PROJECT_NAME}" "${CI_COMMIT_SHORT_SHA}" "$CI_COMMIT_REF_NAME"

  only:
    - dev

build_and_deploy_uat:
  stage: build_and_deploy_uat
  image: google/cloud-sdk:latest
  before_script:
    # Install required packages
    - apt-get update && apt-get install -y openssh-client
    - mkdir -p ~/.ssh

    # Set up GCP authentication with cloud-platform scope
    - echo "$GCP_SERVICE_ACCOUNT_KEY" > /tmp/gcp-key.json
    - gcloud auth activate-service-account --key-file=/tmp/gcp-key.json
    - gcloud config set project natural-region-446420-b6

    # Ensure deployment script is executable
    - ls -l deploy_script.sh 
    - chmod +x deploy_script.sh

  script:
    # Debug: Log branch name
    - echo "CI_COMMIT_REF_NAME=$CI_COMMIT_REF_NAME"

    # Upload the deployment script to GCS
    - gsutil cp deploy_script.sh gs://hatchala-deployment-scripts/deploy_script.sh

    # Use IAP TCP forwarding to SSH to bastion server
    - >
      gcloud compute ssh ranjith093@hatchala-bastion-server --tunnel-through-iap --zone=us-central1-c --command="
        gsutil cp gs://hatchala-deployment-scripts/deploy_script.sh ./deploy_script.sh
        chmod +x ./deploy_script.sh
        gcloud compute ssh ranjith093@monotelo-uat --tunnel-through-iap --zone=us-central1-c --command='
          gsutil cp gs://hatchala-deployment-scripts/deploy_script.sh ./deploy_script.sh
          chmod +x ./deploy_script.sh
          echo "CI_PROJECT_NAME=${CI_PROJECT_NAME}"
          echo "CI_COMMIT_SHORT_SHA=${CI_COMMIT_SHORT_SHA}"
          echo "CI_COMMIT_REF_NAME=${CI_COMMIT_REF_NAME}"
          # ./deploy_script_uat.sh \"\${CI_PROJECT_NAME}\" \"\${CI_COMMIT_SHORT_SHA}\" \"\${CI_COMMIT_REF_NAME}\"
          ./deploy_script.sh "${CI_PROJECT_NAME}" "${CI_COMMIT_SHORT_SHA}" "${CI_COMMIT_REF_NAME}"
        '
      "
  only:
    - uat

# build_and_deploy_uat:
#   stage: build_and_deploy_uat
#   image: ubuntu:latest
#   script:
#     - apt-get update && apt-get install -y openssh-client
#     - mkdir /root/.ssh
#     - echo "$SSH_PRIVATE_KEY_DEV" | tr -d '\r' > /root/.ssh/id_rsa
#     - chmod 400 /root/.ssh/id_rsa
#     - ssh-keyscan -t rsa ************* >> ~/.ssh/known_hosts
#     - scp -i "/root/.ssh/id_rsa" -o StrictHostKeyChecking=no ./deploy_script.sh ranjith093@*************:./
#     - ssh -i "/root/.ssh/id_rsa" -o StrictHostKeyChecking=no ranjith093@************* "bash -s" < ./deploy_script.sh "${CI_PROJECT_NAME}" "${CI_COMMIT_SHORT_SHA}" "$CI_COMMIT_REF_NAME"

#   only:
#     - uat

build_and_deploy_prod:
  stage: build_and_deploy_prod
  image: ubuntu:latest
  tags:
    - self-hosted
  script:
    - docker build --build-arg SWA_TOKEN=${SWA_TOKEN} -t rapiddevops/monotelo:prod-$CI_COMMIT_SHORT_SHA -f Dockerfile.prod .
    - docker push rapiddevops/monotelo:prod-$CI_COMMIT_SHORT_SHA
  only:
    - prod

#!/usr/bin/env python3
import os
import sys
import logging
import argparse
from pdf2image import convert_from_path
from PIL import Image
from main import preprocess_and_extract_cells, identify_form_type
import cv2

# Configure logging
logging.basicConfig(
    filename='document_processing.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def convert_pdf_to_images(pdf_path, output_dir):
    try:
        logging.info(f"Starting PDF conversion for {pdf_path}")
        # Convert PDF pages to images
        pages = convert_from_path(pdf_path)
        logging.info(f"PDF has {len(pages)} page(s)")
        for i, page in enumerate(pages, start=1):
            image_filename = os.path.join(output_dir, f'page_{i}.png')
            page.save(image_filename, 'PNG')
            # Preprocess and extract cells for the current page
            cell_images, cell_coords = preprocess_and_extract_cells(image_filename)
            # Identify form type for the current page
            form_type = identify_form_type(cv2.imread(image_filename), cell_images, cell_coords)
            logging.info(f"Form Type for page {i} of {pdf_path} is {form_type}")
        logging.info("PDF conversion completed successfully.")
    except Exception as e:
        logging.error(f"Error during PDF conversion: {e}")
        sys.exit(1)

def process_image(image_path, output_dir):
    try:
        logging.info(f"Processing image {image_path}")
        img = Image.open(image_path)
        # If needed, you can save or process the image further.
        image_filename = os.path.join(output_dir, os.path.basename(image_path))
        img.save(image_filename)
        # Preprocess and extract cells for the current page
        cell_images, cell_coords = preprocess_and_extract_cells(image_filename)
        # Identify form type for the current page
        form_type = identify_form_type(cv2.imread(image_filename), cell_images, cell_coords)
        logging.info(f"Form Type for image of {image_path} is {form_type}")
    except Exception as e:
        logging.error(f"Error processing image {image_path}: {e}")
        sys.exit(1)

def main():
    parser = argparse.ArgumentParser(description="Process a document (PDF or image) and convert PDF pages to images.")
    # parser.add_argument("document_path", help="Path to the PDF or image file")
    # args = parser.parse_args()

    base_path = "inputs/Phase 2"

    folder_name = "Property Taxes"

    # get files in this folder
    files = os.listdir(f"{base_path}/{folder_name}")

    for file in files:

        document_path = f"{base_path}/{folder_name}/{file}"

        if not os.path.isfile(document_path):
            logging.error(f"File not found: {document_path}")
            sys.exit("Error: File not found.")

        # Create output directory based on document name
        base_name = os.path.splitext(os.path.basename(document_path))[0]
        output_dir = os.path.join(os.getcwd(), f"{base_name}_output")
        os.makedirs(output_dir, exist_ok=True)
        logging.info(f"Output directory set to {output_dir}")

        # Check file extension
        file_ext = os.path.splitext(document_path)[1].lower()
        if file_ext == '.pdf':
            convert_pdf_to_images(document_path, output_dir)
        elif file_ext in ['.png', '.jpg', '.jpeg', '.bmp', '.tiff']:
            process_image(document_path, output_dir)
        else:
            logging.error(f"Unsupported file type: {file_ext}")
            sys.exit(f"Error: Unsupported file type {file_ext}")

if __name__ == "__main__":
    main()

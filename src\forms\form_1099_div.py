from pydantic import BaseModel, Field
from src.forms.base_form import TextField, YearField, CurrencyField, DateField, CheckboxField, FormDetails, ZipCodeField, EINField
from typing import List


class PayerInformation(BaseModel):
    payer_name: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_street_address: TextField = Field(
        default_factory=lambda: TextField(type="text"), description="The payer's street address as listed on the form without city, state, zipcode.")
    payer_city: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_state: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    payer_zip_code: ZipCodeField = Field(
        default_factory=lambda: ZipCodeField(type="zipcode"))
    # payer_telephone_number: ContactField = Field(
    #     default_factory=lambda: ContactField(type="contact"))
    payer_tin: EINField = Field(
        default_factory=lambda: EINField(type="einfield"))


class FinancialFields(BaseModel):
    total_ordinary_dividends: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="1a", type="currency"))
    qualified_dividends: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="1b", type="currency"))
    total_capital_gain_distr: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="2a", type="currency"))
    unrecap_section_1250_gain: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="2b", type="currency"))
    section_1202_type: TextField = Field(
        default_factory=lambda: TextField(sequence="2c", type="text"))
    section_1202_gain: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="2c", type="currency"))
    collectibles_28_percent_gain: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="2d", type="currency"))
    section_897_ordinary_dividends: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="2e", type="currency"))
    section_897_capital_gain: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="2f", type="currency"))
    nondividend_distributions: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="3", type="currency"))
    federal_income_tax_withheld: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="4", type="currency"))
    section_199A_dividends: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="5", type="currency"))
    investment_expenses: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="6", type="currency"))
    foreign_tax_paid: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="7", type="currency"))
    foreign_country_or_us_possession: TextField = Field(
        default_factory=lambda: TextField(sequence=8, type="text"))
    cash_liquidation_distributions: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="9", type="currency"))
    non_cash_liquidation_distributions: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="10", type="currency"))
    exempt_interest_dividends: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="12", type="currency"))
    specified_private_activity_bond_interest_dividends: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence="13", type="currency"))


class StateTaxInformation(BaseModel):
    state: TextField = Field(
        default_factory=lambda: TextField(sequence=14, type="text"))
    state_identification_number: TextField = Field(default_factory=lambda: TextField( type="text"))
    state_tax_withheld: CurrencyField = Field(
        default_factory=lambda: CurrencyField(sequence=16, type="currency"))


class AdditionalFields(BaseModel):
    void: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    corrected: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    fatca_filing_requirement: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox", sequence="11"))
    second_tin_not: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    omb_number: TextField = Field(
        default_factory=lambda: TextField(type="text"))


# Main Pydantic model for Form 1099-INT
class Form1099DIV(BaseModel):
    payer_information: PayerInformation = Field(
        default_factory=PayerInformation)
    form_details: FormDetails = Field(default_factory=FormDetails)
    financial_fields: FinancialFields = Field(default_factory=FinancialFields)
    state_tax_information: List[StateTaxInformation] = Field(
        default_factory=list)
    additional_fields: AdditionalFields = Field(
        default_factory=AdditionalFields)

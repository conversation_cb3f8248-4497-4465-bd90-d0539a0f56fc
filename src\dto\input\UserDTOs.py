from typing import Optional
from pydantic import BaseModel, EmailStr, Field
from src.constants.enum import TwoFactorAuthTypes

class SignupRequest(BaseModel):
    """Schema for user signup."""
    name: str = <PERSON>(..., description="Name of the user")
    email: EmailStr = Field(..., description="Email of the user")
    password: str = Field(..., description="Password of the user")

class LoginRequest(BaseModel):
    """Schema for user login."""
    email: EmailStr = Field(..., description="Email of the user")
    password: str = Field(..., description="Password of the user")
    two_factor_auth_type : str = Field(TwoFactorAuthTypes.EMAIL.value, description="Two factor authentication type")
    
class LoginRequest2FA(BaseModel):
    """Schema for user login 2FA."""
    email: EmailStr = Field(..., description="Email of the user")
    otp: str = Field(..., description="OTP of the user")

class ForgotPasswordRequest(BaseModel):
    """Schema for forgot password functionality using OTP."""
    email: EmailStr = Field(..., description="Email of the user")

class ForgotPasswordVerifyOTPRequest(BaseModel):
    """Schema for forgot password functionality check OTP is valid."""
    otp: str = Field(..., description="OTP received for password reset")
    email: EmailStr = Field(..., description="Email of the user")

class ResetPasswordRequest(BaseModel):
    """Schema for resetting password using OTP."""
    email: EmailStr = Field(..., description="Email of the user")
    otp: str = Field(..., description="OTP received for password reset")
    new_password: str = Field(..., description="New password for the user")

class UpdatePasswordRequest(BaseModel):
    """Schema for updating user password."""
    old_password: str = Field(..., description="Old password of the user")
    new_password: str = Field(..., description="New password for the user")

class UpdateProfileRequest(BaseModel):
    """Schema for updating user profile."""
    user_id: Optional[str] = Field(None, description="User ID of the user")
    drake_user_id: Optional[str] = Field(None, description="This id is the foregin key between hatchala and drake user")
    name: Optional[str] = Field(None, description="Name of the user")
    phone_number: Optional[str] = Field(None, description="Phone number of the user")
    profile_image_link: Optional[str] = Field(None, description="Profile image link of the user")
    role: Optional[str] = Field(None, description="Role of the user")
    toggle_is_two_factor_auth_enabled: Optional[bool] = Field(None, description="Flag to enable/disable two factor authentication")
    two_factor_auth_type: Optional[str] = Field(None, description="Two factor authentication type")
    toggle_is_active: Optional[bool] = Field(None, description="Flag to active/inactive user")
    max_clients_upload_at_a_time: Optional[int] = Field(None, description="Maximum number of clients that can be uploaded at a time")
    max_priority_clients_to_process_at_a_time: Optional[int] = Field(None, description="Maximum number of priority clients that can be processed at a time")

class InviteRequest(BaseModel):
    names: list[str]
    emails: list[EmailStr]

class AcceptInviteRequest(BaseModel):
    invite_token: str
    password: str

class UpdateProfilePic(BaseModel):
    content_type: str
    image_extension: str

    class Config:
        schema_extra = {
            "example": {
                "content_type": "image/png",
                "image_extension": ".png",
            }
        }


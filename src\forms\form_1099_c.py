from pydantic import BaseModel, Field
from typing import List, Optional
from src.forms.base_form import TextField, YearField, CurrencyField, CheckboxField, FormDetails, ZipCodeField, EINField, DateField


class CreditorInformation(BaseModel):
    creditor_name: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    creditor_street_address: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    creditor_city: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    creditor_state: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    creditor_zip_code: ZipCodeField = Field(
        default_factory=lambda: ZipCodeField(type="zipcode"))
    creditor_tin_or_federal_dentification_number: EINField = Field(default_factory=lambda: EINField(type="einfield"))

class FinancialFields(BaseModel):
    date_of_identifiable_event: DateField = Field(
        default_factory=lambda: DateField(type="date"))
    amount_of_debit_dischanged: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    interest_if_included_in_box_2:  CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    debt_description: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    checked_if_the_debtor_was_personally_liable_for_repayment_of_the_debt: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    identifiable_code: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    fair_market_value_of_property: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))


class AdditionalFields(BaseModel):
    corrected: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    omb_number: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    calendar_year: YearField = Field(
        default_factory=lambda: YearField(type="year"))


# Main Pydantic model for Form 1099 C
class Form1099C(BaseModel):
    creditor_information: CreditorInformation = Field(
        default_factory=CreditorInformation)
    form_details: FormDetails = Field(default_factory=FormDetails)
    financial_fields: FinancialFields = Field(default_factory=FinancialFields)
    additional_fields: AdditionalFields = Field(
        default_factory=AdditionalFields)

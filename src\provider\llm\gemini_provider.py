import base64
import os
import requests
import json

class GoogleGenerativeAIProvider:
    def __init__(self, api_key: str, model: str):
        self.api_key = api_key
        self.api_url = (
            f"https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent?key={api_key}"
        )

    def generate_content(self, image_path: str, prompt: str, retry_count: int = 3):
        """
        Send a request to the Google Generative Language API to process an image with a prompt,
        with optional retries if the call fails.

        Args:
            image_path (str): Path to the image to process.
            prompt (str): Text prompt for the model.
            retry_count (int): Number of times to retry if the API call fails.

        Returns:
            dict: The JSON response from the API if successful, else None.
        """
        # Determine MIME type based on file extension
        extension = os.path.splitext(image_path)[1].lower()
        if extension in [".jpg", ".jpeg"]:
            mime_type = "image/jpeg"
        elif extension == ".png":
            mime_type = "image/png"
        else:
            # # Fallback if not jpg/jpeg/png
            # mime_type = "application/octet-stream"
            print(f"Unsupported image format: {extension}")
            return {
                "response": None,
                "error": f"Unsupported image format: {extension}",
                "success": False
            }

        # Read and encode the image
        try:
            with open(image_path, "rb") as image_file:
                encoded_image = base64.b64encode(image_file.read()).decode("utf-8")
        except Exception as e:
            print(f"Error reading or encoding the image: {e}")
            return {
                "response": None,
                "error": f"Error reading or encoding the image: {e}",
                "success": False
            }
        print('provided extension', extension)
        # Prepare the payload
        payload = {
            "contents": [
                {
                    "parts": [
                        {"text": prompt},
                        {
                            "inline_data": {
                                "mime_type": mime_type,
                                "data": encoded_image
                            }
                        },
                    ]
                }
            ]
        }

        # Send the request, retrying if needed
        headers = {"Content-Type": "application/json"}
        payload_json = json.dumps(payload)
        size_in_mb = len(payload_json.encode('utf-8')) / (1024*1024)
        print(f"Payload size: {size_in_mb} MB")
        
        for attempt in range(retry_count):
            try:
                response = requests.post(
                    self.api_url,
                    headers=headers,
                    data=json.dumps(payload)
                )
                
                if response.status_code == 200:
                    return {
                        "response": response.json(),
                        "success": True,
                        "error":''
                    }
                else:
                    print(
                        f"Attempt {attempt + 1} failed with status code {response.status_code}: {response.text}"
                    )
                    
            except requests.exceptions.RequestException as e:
                print(f"Attempt {attempt + 1} encountered an error: {e}")
            
            if attempt < retry_count - 1:
                print("Retrying...")
        
        # If all retries failed
        print("All retry attempts have failed.")
        return {
                "response": None,
                "error": f"All retry attempts have failed during ocr",
                "success": False
            }

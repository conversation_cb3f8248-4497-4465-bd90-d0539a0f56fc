from langchain_openai import Chat<PERSON><PERSON><PERSON><PERSON>
# from langchain_google_genai import ChatGoogleGenerativeAI
# import google.generativeai as genai
from src.provider.llm.Qwen2_5_VL_OR import OpenRouterQwenVLProvider
from src.provider.llm.Qwen2_5_VL import QwenV<PERSON>rovider
from src.constants.env_constant import env_var
from src.constants.enum import LLM_PROVIDER
from src.provider.llm.gemini_provider import GoogleGenerativeAIProvider
from src.provider.llm.vertex_ai_provider import GoogleVertexAIProvider

open_ai_api = env_var["OPENAI_API_KEY"]
gemini_api = env_var["GEMINI_API_KEY"]


# Provider initialization function
def get_llm_provider(provider: LLM_PROVIDER,metadata, **kwargs):
    """
    Initialize and return the LLM provider based on the selected provider type.

    Args:
        provider (LLM_PROVIDER): The LLM provider to initialize.
        **kwargs: Additional parameters for the LLM initialization.

    Returns:
        An initialized LLM instance.
    """
    if provider == LLM_PROVIDER.OPENAI_GPT_4O:
        return ChatOpenAI(
            model=provider.value,
            api_key=open_ai_api,
            temperature=kwargs.get("temperature", 0.5),
            max_tokens=kwargs.get("max_tokens", None),
            timeout=kwargs.get("timeout", None),
            max_retries=kwargs.get("max_retries", 2),
            metadata = metadata
            # **kwargs
        )
    elif provider == LLM_PROVIDER.GEMINI_1_5_PRO:
        return GoogleGenerativeAIProvider(api_key=gemini_api, model=provider.value)

        #vertex ai
        # return GoogleVertexAIProvider(project_id = env_var["PROJECT_ID"],model_id = provider.value)
    
    #     genai.configure(api_key=gemini_api)
    #     return genai.GenerativeModel('gemini-1.5-flash')
        # return ChatGoogleGenerativeAI(
        #     model=provider.value,
        #     api_key=gemini_api,
        #     temperature=kwargs.get("temperature", 0),
        #     max_tokens=kwargs.get("max_tokens", None),
        #     timeout=kwargs.get("timeout", None),
        #     max_retries=kwargs.get("max_retries", 2)
        # )

    elif provider == LLM_PROVIDER.OR_QWEN_2_5_VL:
        or_api = env_var["OR_TOKEN"]
        return OpenRouterQwenVLProvider(api_key=or_api)
    
    elif provider == LLM_PROVIDER.HF_QWEN_2_5_VL:
        hf_api = env_var["HF_TOKEN"]
        return QwenVLProvider(api_key=hf_api)

    else:
        raise ValueError(f"Unsupported LLM provider: {provider}")
from pydantic import BaseModel, Field
from src.forms.base_form import TextField,YearField, CurrencyField, CheckboxField, FormDetails, ZipCodeField, EINField

class RecipientInformation(BaseModel):
    recipient_lender_name: TextField = Field(default_factory=lambda: TextField(type="text"))
    recipient_lender_street_address: TextField = Field(default_factory=lambda: TextField(type="text"), description="The recipient’s street address as listed on the form without city, state, zipcode.")
    recipient_lender_city: TextField = Field(default_factory=lambda: TextField(type="text"))
    recipient_lender_state: TextField = Field(default_factory=lambda: TextField(type="text"))
    recipient_lender_zip_code: ZipCodeField = Field(default_factory=lambda: ZipCodeField(type="zipcode"))
    # recipient_lender_telephone_number: ContactField = Field(default_factory=lambda: ContactField(type="contact"))
    recipient_lender_tin: EINField = Field(default_factory=lambda: EINField(type="einfield"))

class FinancialFields(BaseModel):
    student_loan_interest_received_by_lender: CurrencyField = Field(default_factory=lambda: CurrencyField(sequence="1", type="currency"))


class AdditionalFields(BaseModel):
    corrected_if_checked: CheckboxField = Field(default_factory=lambda: CheckboxField(type="checkbox"))
    if_checked_box_1_does_not_include_loan_origination_fees_and_or_capitalized_interest_for_loans : CheckboxField = Field(default_factory=lambda: CheckboxField(type="checkbox", sequence="2"))
    omb_number: TextField = Field(default_factory=lambda: TextField(type="text"))


# Main Pydantic model for Form 1099-INT
class Form1098E(BaseModel):
    recipient_information: RecipientInformation = Field(default_factory=RecipientInformation)
    form_details: FormDetails = Field(default_factory=FormDetails)
    financial_fields: FinancialFields = Field(default_factory=FinancialFields)
    additional_fields: AdditionalFields = Field(default_factory=AdditionalFields)
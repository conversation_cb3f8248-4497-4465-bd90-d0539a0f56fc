from src.constants.env_constant import env_var
from src.provider.bucket import get_bucket_provider

# Initialize the bucket provider based on your environment variable
bucket_provider = get_bucket_provider(env_var["DEFAULT_BUCKET_PROVIDER"])

def generate_upload_presigned_url(social_security_number: str, financial_year: int, filename: str) -> str:
    """
    Generate a presigned URL for uploading a file.
    """
    blob_name = f"{social_security_number}/{financial_year}/input/{filename}"
    return bucket_provider.generate_pre_signed_url(
        blob_name=blob_name,
        bucket_name=env_var["GCS_BUCKET_NAME"],
        content_type=None,
        method_type="PUT"
    )

def generate_preview_presigned_url(file_path: str) -> str:
    """
    Generate a presigned URL for previewing a file.
    """
    return bucket_provider.generate_pre_signed_url(
        blob_name=file_path,
        bucket_name=env_var["GCS_BUCKET_NAME"],
        content_type=None,
        method_type="GET"
    )

from pydantic import BaseModel, Field
from src.forms.base_form import TextField,YearField, CurrencyField, CheckboxField, FormDetails, DateField


class FinancialFields(BaseModel):
    benefits_paid: CurrencyField=Field(default_factory=lambda:CurrencyField(sequence="3", type="currency"))
    benefits_repaid_to_ssa: CurrencyField=Field(default_factory=lambda:CurrencyField(sequence="4", type="currency"))
    net_benefits: CurrencyField=Field(default_factory=lambda:CurrencyField(sequence="5", type="currency"))
    description_of_amount_paid_by_check_or_direct_deposit: CurrencyField=Field(default_factory=lambda:CurrencyField(type="text"))
    description_of_amount_medicare_part_b_premiums_deducted_from_your_benefits: CurrencyField=Field(default_factory=lambda:CurrencyField(type="text"))
    description_of_amount_medicare_prescription_drug_premiums_deducted_from_your_benefits: CurrencyField=Field(default_factory=lambda:C<PERSON>rencyField(type="text"))
    description_of_amount_total_additions: CurrencyField=Field(default_factory=lambda:CurrencyField(type="text"))
    description_of_amount_benefits: CurrencyField=Field(default_factory=lambda:CurrencyField(type="text"))
    voluntary_federal_income_tax_withheld: CurrencyField=Field(default_factory=lambda:CurrencyField(sequence="6", type="currency"))


class Form1099SSA(BaseModel):
    form_details: FormDetails = Field(default_factory=FormDetails)
    financial_fields: FinancialFields = Field(default_factory=FinancialFields)

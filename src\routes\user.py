from src.provider.bucket import get_bucket_provider
from bson import ObjectId
from fastapi import APIRouter, Request, status as HTTPSSTATUS, Depends, Query
from datetime import datetime, timedelta
from fastapi.responses import JSONResponse
from typing import List, Optional
from math import ceil
import logging

import jwt
from pymongo import UpdateOne

# Importing models, constants, and dependencies from the project
from src.models.clients import Client
from src.dto.input.UserDTOs import AcceptInviteRequest, ForgotPasswordVerifyOTPRequest, InviteRequest, LoginRequest2FA, LoginRequest, ForgotPasswordRequest, ResetPasswordRequest, UpdatePasswordRequest, UpdateProfilePic, UpdateProfileRequest
from src.dto.output.CommonResponseModel import CommonResponseModel
from src.models.users import User
from src.models.otps import OTP
from src.constants.enum import InviteStatus, OTPType, Tag, TwoFactorAuthTypes, UserRole
from src.constants.env_constant import env_var

# Import Mail Provider
from src.provider.mail import get_mail_provider

# Import SMS Provider
from src.provider.sms import get_sms_provider

# Import the new helper functions
from src.middleware.rbac_middleware import check_permission
from src.auth.auth_handler import decode_jwt, user_jwt_required, generate_jwt_token, JWT_EXPIRATION_TIME_IN_HOURS, OTP_EXPIRATION_TIME_IN_MINUTES
from src.utils.bcrypt_utils import get_hash_text, verify_text

# Initialize the MailService class
mail_service = get_mail_provider(env_var["DEFAULT_MAIL_PROVIDER"])

# Initialize the SMSService class
sms_service = get_sms_provider(env_var["DEFAULT_SMS_PROVIDER"])

# Initialize bucket proider
bucket_provider = get_bucket_provider(env_var["DEFAULT_BUCKET_PROVIDER"])

# Initialize FastAPI router with prefix '/v1/user'
router = APIRouter(prefix="/v1/user")

JWT_SECRET = env_var.get("JWT_SECRET")
JWT_ALGORITHM = env_var.get("JWT_ALGORITHM", "HS256")


@router.post("/invite", tags=[Tag.USERS.value])
@check_permission("admin::create")
async def send_invites(request: Request, payload: InviteRequest, current_user: dict = Depends(user_jwt_required)):
    try:
        sent = []
        for index, email in enumerate(payload.emails):
            email = email.lower()
            name = payload.names[index]
            existing_user = User.get(email=email)

            # If user already exists and invite has been accepted, do nothing.
            if existing_user and (existing_user.invite_status == InviteStatus.ACCEPTED.value or existing_user.is_active):
                response = CommonResponseModel(
                    success=False, message=f"User with email {email} already exists.")
                return JSONResponse(
                    status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict()
                )

            # Prepare a new default password.
            default_password = get_hash_text(env_var["DEFAULT_PASSWORD"])
            token_payload = {"email": email, "name": name, "password": default_password}
            token = generate_jwt_token(
                token_payload, expires_delta=timedelta(hours=JWT_EXPIRATION_TIME_IN_HOURS)
            )
            
            # Compute new invite expiration datetime.
            new_invite_expiry = datetime.utcnow() + timedelta(hours=JWT_EXPIRATION_TIME_IN_HOURS)

            if existing_user:
                # User exists but has not accepted invite.
                # Check if the stored invite token is expired.
                if existing_user.invite_expires_at and existing_user.invite_expires_at < datetime.utcnow():
                    # Token expired: update with a new password, token expiration, and mark invite as pending.
                    existing_user.password = default_password
                    existing_user.invite_expires_at = new_invite_expiry
                    existing_user.invite_status = InviteStatus.PENDING.value
                    existing_user.invited_at = datetime.utcnow()
                    existing_user.save()
            else:
                # If user does not exist, create a new one with inactive status.
                existing_user = User.create(
                    name=name,
                    email=email,
                    password=default_password,
                    roles=[UserRole.USER.value],
                    is_active=False,
                    invite_status=InviteStatus.PENDING.value,
                    invite_expires_at=new_invite_expiry,
                    invited_at=datetime.utcnow()
                )
            
            # Build the invitation link using the token.
            link = f"{env_var['FRONTEND_URL']}/set-password?token={token}"
            subject = "You’re invited to join our platform"

            if await mail_service.send_mail(
                subject, 
                [email],
                template_name="invitation.html",
                context={
                    "invitee_name": name,
                    "invite_link": link,
                }
            ):
                sent.append(email)
            else:
                logging.error(f"Failed to send invite to {email}")

        if not sent:
            return JSONResponse(
                status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR,
                content=CommonResponseModel(
                    success=False, message="Failed to send any invites"
                ).dict()
            )

        return JSONResponse(
            status_code=HTTPSSTATUS.HTTP_200_OK,
            content=CommonResponseModel(
                success=True, message=f"Invites sent to: {', '.join(sent)}"
            ).dict()
        )

    except Exception as e:
        logging.error(f"[send_invites] unexpected error: {e}", exc_info=True)
        return JSONResponse(
            status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR,
            content=CommonResponseModel(
                success=False, message="Internal server error"
            ).dict()
        )


@router.post("/invite/accept", tags=[Tag.USERS.value])
async def accept_invite(request_payload: AcceptInviteRequest):
    try:
        # AcceptInviteRequest now includes invite_token, name, and password
        token = request_payload.invite_token
        payload = decode_jwt(token)
        email = payload.get("email")
        email = email.lower()
        if not email:
            response = CommonResponseModel(
                success=False, message="Invalid invite token")
            return JSONResponse(
                status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict()
            )

        user = User.get(email=email)
        if not user:
            response = CommonResponseModel(
                success=False, message="User does not exist")
            return JSONResponse(
                status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict()
            )
        else:
            if user.invite_status == InviteStatus.ACCEPTED.value:
                response = CommonResponseModel(
                    success=False, message="Invite already accepted")
                return JSONResponse(
                    status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict()
                )
        
        # Check if the invite token is expired using the stored expiration.
        if user.invite_expires_at < datetime.utcnow():
            response = CommonResponseModel(
                success=False, message="Invite link expired")
            return JSONResponse(
                status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict()
            )
        
        # Update user details with the provided password.
        user.password = get_hash_text(request_payload.password)
        user.is_active = True
        user.invite_status = InviteStatus.ACCEPTED.value
        user.save()

        # Send a welcome email.
        email_subject = "Welcome to the platform!"
        await mail_service.send_mail(
            subject=email_subject,
            recipients=[email],
            template_name="welcome.html",
            context={
                "user_name": user.name,
                "user_email": email,
                "user_role": UserRole.USER.value,
                "FRONTEND_URL": env_var["FRONTEND_URL"]
            }
        )

        return JSONResponse(
            status_code=HTTPSSTATUS.HTTP_201_CREATED,
            content=CommonResponseModel(
                success=True,
                message="Account created successfully",
                data=user.to_dict()
            ).dict()
        )

    except jwt.ExpiredSignatureError:
        return JSONResponse(
            status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST,
            content=CommonResponseModel(
                success=False, message="Invite link expired"
            ).dict()
        )

    except jwt.InvalidTokenError:
        return JSONResponse(
            status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST,
            content=CommonResponseModel(
                success=False, message="Invalid invite token"
            ).dict()
        )

    except Exception as e:
        logging.error(f"[accept_invite] unexpected error: {e}", exc_info=True)
        return JSONResponse(
            status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR,
            content=CommonResponseModel(
                success=False, message="Internal server error"
            ).dict()
        )


@router.post("/login", tags=[Tag.USERS.value])
async def login(payload: LoginRequest):
    """
    API endpoint for user login.
    Validates the user's credentials and returns a JWT token if successful.

    Roles with access:
    - User: Can log in to their account.
    - Admin: Can log in to their account.
    """
    try:
        user = User.get(email=payload.email)
        if not user or not verify_text(payload.password, user.password):
            response = CommonResponseModel(
                success=False, message="Invalid email or password.")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

        if not user.is_active:
            response = CommonResponseModel(
                success=False, message="User account is inactive.")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())
        
        if user.is_two_factor_auth:
            two_factor_otp = OTP.generate_otp(user.email, OTPType.TWO_FACTOR_AUTH.value, minutes=OTP_EXPIRATION_TIME_IN_MINUTES)
            email_subject = "Hatchala 2FA: Here’s Your One‑Time Passcode"
            response = CommonResponseModel(
                success=False, message="OTP not sent."
            )

            two_factor_auth_type = user.two_factor_auth_type

            if payload.two_factor_auth_type:
                two_factor_auth_type = payload.two_factor_auth_type


            if two_factor_auth_type == TwoFactorAuthTypes.EMAIL.value:
                await mail_service.send_mail(
                    subject=email_subject,
                    recipients=[user.email],
                    template_name="twoFactorLogin.html",
                    context={
                        "user_name": user["name"],
                        "otp_code": two_factor_otp,
                        "otp_validity": OTP_EXPIRATION_TIME_IN_MINUTES,
                    },  
                )

                response = CommonResponseModel(
                    success=True, message="OTP sent to your email address."
                )
            elif two_factor_auth_type == TwoFactorAuthTypes.SMS.value:
                if user.phone_number:
                    await sms_service.send_sms(
                        to=user.phone_number,
                        body=f"Your Hatchala Login OTP is {two_factor_otp}. It will expire in {OTP_EXPIRATION_TIME_IN_MINUTES} minutes."
                    )
                    response = CommonResponseModel(
                        success=True, message="OTP sent to your phone number."
                    )
                else:
                    response = CommonResponseModel(
                        success=False, message="Phone number not found."
                    )
                    return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())
            if response.success:
                return JSONResponse(status_code=HTTPSSTATUS.HTTP_200_OK, content=response.dict())
            else:
                return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())
        else:
            payload = {
                "id": str(user["id"]),
                "email": user.email,
                "name": user.name
            }
            token = generate_jwt_token(payload, expires_delta=timedelta(
                hours=JWT_EXPIRATION_TIME_IN_HOURS))
            response = CommonResponseModel(
                success=True, message="Login successful.", data={"token": token})
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_200_OK, content=response.dict())
    except Exception as e:
        logging.error(f"Error in login: {e}")
        import traceback
        traceback.print_exc()
        response = CommonResponseModel(
            success=False, message="Error in login.")
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())
    

@router.post("/login/verify-2fa", tags=[Tag.USERS.value])
async def verify_2fa(payload: LoginRequest2FA):
    """
    API endpoint for verifying 2FA OTP.
    Validates the OTP and its expiry before allowing the user to proceed with login.

    Roles with access:
    - User: Can verify 2FA OTP.
    - Admin: Can verify 2FA OTP.
    """
    try:
        user = User.get(email=payload.email)
        if not user:
            response = CommonResponseModel(
                success=False, message="Invalid email.")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

        if not user.is_active:
            response = CommonResponseModel(
                success=False, message="User account is inactive.")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

        response , otp_is_valid = OTP.verify_otp_and_delete(payload.email, payload.otp, OTPType.TWO_FACTOR_AUTH.value)

        if otp_is_valid:
            payload = {
                "id": str(user["id"]),
                "email": user.email,
                "name": user.name
            }
            token = generate_jwt_token(payload, expires_delta=timedelta(
                hours=JWT_EXPIRATION_TIME_IN_HOURS))
            response = CommonResponseModel(
                success=True, message="Login successful.", data={"token": token})
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_200_OK, content=response.dict())
        else:
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())
    except Exception as e:
        logging.error(f"Error in login: {e}")
        response = CommonResponseModel(
            success=False, message="Error in verify 2FA.")
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


@router.post("/forgot-password", tags=[Tag.USERS.value])
async def forgot_password(payload: ForgotPasswordRequest):
    """
    API endpoint for forgot password.
    Validates that the user exists, generates a 6-digit OTP (valid for 10 minutes),
    stores it on the user, and sends the OTP via email.

    Roles with access:
    - User: Can request a password reset.
    - Admin: Can request a password reset.
    """
    try:
        user = User.get(email=payload.email)
        if not user:
            response = CommonResponseModel(
                success=False, message="User with the provided email does not exist.")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_404_NOT_FOUND, content=response.dict())

        # Use helper functions for OTP generation and expiry
        otp = OTP.generate_otp(user.email, OTPType.FORGET_PASSWORD.value)

        email_subject = "Your Password Reset OTP"

        await mail_service.send_mail(
            subject=email_subject,
            recipients=[user.email],
            template_name="forgotPassword.html",
            context={
                "user_name": user["name"],
                "user_email": payload.email,
                "otp_code": otp,
                "otp_validity": OTP_EXPIRATION_TIME_IN_MINUTES,
            },  
        )

        response = CommonResponseModel(
            success=True, message="OTP sent to your email address.")
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_200_OK, content=response.dict())
    except Exception as e:
        logging.error(f"Error in forgot_password: {e}")
        response = CommonResponseModel(
            success=False, message="Error in sending OTP.")
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())

@router.post("/forgot-password/verify-otp", tags=[Tag.USERS.value])
async def verify_otp(payload: ForgotPasswordVerifyOTPRequest):
    """
    API endpoint for verifying an OTP.
    Validates the OTP and its expiry before allowing the user to proceed with password reset.

    Roles with access:
    - User: Can verify OTP for password reset.
    - Admin: Can verify OTP for password reset.
    """
    try:
        response , otp_is_valid = OTP.verify_otp(payload.email, payload.otp, OTPType.FORGET_PASSWORD.value)

        if otp_is_valid:
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_200_OK, content=response.dict())
        else:
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())
    except Exception as e:
        logging.error(f"Error in verify_otp: {e}")
        response = CommonResponseModel(
            success=False, message="Error in verifying OTP.")
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())

@router.post("/reset-password", tags=[Tag.USERS.value])
async def reset_password_with_otp(payload: ResetPasswordRequest):
    """
    API endpoint for resetting a user's password using an OTP.
    Validates the OTP and its expiry before updating the user's password.

    Roles with access:
    - User: Can reset their password using an OTP.
    - Admin: Can reset their password using an OTP.
    """
    try:
        user = User.get(email=payload.email)
        
        response , otp_is_valid = OTP.verify_otp_and_delete(payload.email, payload.otp, OTPType.FORGET_PASSWORD.value)

        if not otp_is_valid:
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

        # Update the user's password using the helper function
        hashed_pw = get_hash_text(payload.new_password)
        user.password = hashed_pw
        user.save()

        response = CommonResponseModel(
            success=True, message="Password reset successfully.")
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_200_OK, content=response.dict())
    except Exception as e:
        logging.error(f"Error in reset_password: {e}")
        response = CommonResponseModel(
            success=False, message="Error in resetting password.")
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


@router.post("/update-password", tags=[Tag.USERS.value])
async def update_password_with_old(payload: UpdatePasswordRequest, current_user: dict = Depends(user_jwt_required)):
    """
    API endpoint for updating a user's password using the old password.
    Validates the old password before updating to the new password.

    Roles with access:
    - User: Can update their password.
    - Admin: Can update their password.
    """
    try:
        user = User.get(id=current_user["id"])
        if not user:
            response = CommonResponseModel(
                success=False, message="User not found.")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_404_NOT_FOUND, content=response.dict())
        
        if not payload.old_password:
            response = CommonResponseModel(
                success=False, message="Old password is required.")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

        if not verify_text(payload.old_password, user.password):
            response = CommonResponseModel(
                success=False, message="Old password is incorrect.")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())
        
        if not payload.new_password:
            response = CommonResponseModel(
                success=False, message="New password is required.")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST, content=response.dict())

        hashed_pw = get_hash_text(payload.new_password)
        user.password = hashed_pw
        user.save()

        response = CommonResponseModel(
            success=True, message="Password updated successfully.")
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_200_OK, content=response.dict())
    except Exception as e:
        logging.error(f"Error updating password: {e}")
        response = CommonResponseModel(
            success=False, message="Error in updating password.")
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())

@router.post("/update-profile-pic", tags=[Tag.USERS.value])
def update_profile_pic(request_body: UpdateProfilePic, user: dict = Depends(user_jwt_required)):

    """
    API endpoint to update a user's profile picture.
    Generates a pre-signed URL for uploading the profile picture to Google Cloud Storage.

    Roles with access:
    - User: Can update their profile picture.
    - Admin: Can update their profile picture.
    """
    try:

        blob_name = f'users/profile_picture/{user["id"]}{request_body.image_extension}'
        content_type = request_body.content_type

        if content_type not in ["image/jpeg", "image/png", "image/jpg", "image/gif"]:
            response = CommonResponseModel(message="Invalid image type")
            return JSONResponse(
                status_code=HTTPSSTATUS.HTTP_400_BAD_REQUEST,
                content=response.dict()
            )

        url = bucket_provider.generate_pre_signed_url(
            blob_name=blob_name,
            bucket_name=env_var["PUBLIC_BUCKET_NAME"],
            content_type=content_type,
            method_type="PUT"
        )

        response = CommonResponseModel(
            success=True,
            message="Profile picture upload url generated successfully",
            data={"url": url}
        )

        return JSONResponse(
            status_code=HTTPSSTATUS.HTTP_200_OK,
            content=response.dict()
        )
    except Exception as e:
        logging.error(f"Error updating profile picture: {e}")
        response = CommonResponseModel(
            success=False,
            message="Error updating profile picture"
        )
        return JSONResponse(
            status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR,
            content=response.dict()
        )

@router.patch("/update-user", tags=[Tag.USERS.value])
async def update_user(payload: UpdateProfileRequest, current_user: dict = Depends(user_jwt_required)):
    """
    API endpoint to update a user's details.
    Supports updating the user's name.

    Roles with access:
    - User: Can update their profile details.
    - Admin: Can update their profile details.
    """
    try:
        isAdmin = False
        
        if UserRole.ADMIN.value in current_user["roles"]:
            isAdmin = True
            if payload.user_id:
                user_id = payload.user_id
            else:
                user_id = current_user["id"]
        else:
            user_id = current_user["id"]

        user = User.objects(id=user_id).first()
        if not user:
            response = CommonResponseModel(
                success=False, message="User not found.")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_404_NOT_FOUND, content=response.dict())

        if payload.name:
            user.name = payload.name
        if payload.phone_number:
            user.phone_number = payload.phone_number
        if payload.two_factor_auth_type:
            user.two_factor_auth_type = payload.two_factor_auth_type
        if payload.profile_image_link:
            if "profile_image" not in user:
                user["profile_image"] = ""
            if user.profile_image and user.profile_image != payload.profile_image_link:
                user.profile_image = payload.profile_image_link
                bucket_provider.delete_file(env_var["PUBLIC_BUCKET_NAME"], "/".join(user.profile_picture.split('/')[4:]))
        if payload.drake_user_id:
            user["drake_user_id"] = payload.drake_user_id

        
        if isAdmin:
            if payload.role:
                user.roles = [payload.role]
                if payload.role == UserRole.ADMIN.value:
                    user.max_clients_upload_at_a_time = env_var["DEFAULT_MAX_CLIENTS_UPLOAD_AT_A_TIME_ADMIN"]
                    user.max_priority_clients_to_process_at_a_time = env_var["DEFAULT_MAX_PRIORITY_CLIENTS_TO_PROCESS_AT_A_TIME_ADMIN"]
            if payload.toggle_is_active is not None:
                user.is_active = payload.toggle_is_active
            if payload.toggle_is_two_factor_auth_enabled is not None:
                user.is_two_factor_auth = payload.toggle_is_two_factor_auth_enabled
            if payload.max_clients_upload_at_a_time is not None:
                user.max_clients_upload_at_a_time = payload.max_clients_upload_at_a_time
            if payload.max_priority_clients_to_process_at_a_time is not None:
                user.max_priority_clients_to_process_at_a_time = payload.max_priority_clients_to_process_at_a_time

        
        # user.last_updated_by = ObjectId(current_user["id"])

        user.save()
        response = CommonResponseModel(
            success=True, message="User updated successfully.", data=user.to_dict())
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_200_OK, content=response.dict())
    except Exception as e:
        logging.error(f"Error updating user: {e}")
        response = CommonResponseModel(
            success=False, message="Error updating user.")
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


@router.delete("/delete-user/{user_id}/{new_owner_id}", tags=[Tag.USERS.value])
@check_permission("admin::delete")
async def delete_user(request: Request, user_id: str, new_owner_id, current_user: dict = Depends(user_jwt_required)):
    """
    API endpoint to delete a user by ID.

    Roles with access:
    - User: Cannot delete their own account.
    - Admin: Can delete any user account.
    """
    try:
        user = User.get(id=user_id)
        clients = Client.objects(user=ObjectId(user_id))

        #Bulk update
        updates = []
        for client in clients:
            updates.append(UpdateOne({'_id': client.id}, {'$set': {'user': ObjectId(new_owner_id), 'last_updated_by': ObjectId(new_owner_id)}}))

        if updates:
            Client._get_collection().bulk_write(updates, ordered=False)
            print(f"Updated {len(updates)} clients to reference new user '{new_owner_id}'")
        else:
            print("No clients found for update.")

        if not user:
            response = CommonResponseModel(
                success=False, message="User not found.")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_404_NOT_FOUND, content=response.dict())
        
        # Hard delete the user
        user.delete()
        response = CommonResponseModel(
            success=True, message="User deleted and clients transferred successfully.")
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_200_OK, content=response.dict())
    except Exception as e:
        logging.error(f"Error deleting user: {e}")
        response = CommonResponseModel(
            success=False, message="Error deleting user.")
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


@router.get("/get-user", tags=[Tag.USERS.value])
async def get_user(current_user: dict = Depends(user_jwt_required)):
    """
    API endpoint to retrieve a single user by their ID.

    Roles with access:
    - User: Can view their own account details.
    """
    try:
        # Check if the current user is an admin or the user they're trying to retrieve
        user_id = current_user["id"]

        user = User.get(id=user_id)

        if not user:
            response = CommonResponseModel(
                success=False, message="User not found.")
            return JSONResponse(status_code=HTTPSSTATUS.HTTP_404_NOT_FOUND, content=response.dict())
        response = CommonResponseModel(
            success=True, message="User retrieved successfully.", data=user.to_dict())
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_200_OK, content=response.dict())
    except Exception as e:
        logging.error(f"Error retrieving user: {e}")
        response = CommonResponseModel(
            success=False, message="Error retrieving user.")
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())


@router.get("/get-users", tags=[Tag.USERS.value])
@check_permission("admin::read")
async def get_users(request: Request,
                    page: int = Query(1, ge=1),
                    page_size: int = Query(10, ge=1),
                    user_ids: List[str] = Query(
                        None, description="List of user IDs to filter by"),
                    name: str = Query(None, description="Filter by user name"),
                    email: str = Query(None, description="Filter by user email"),
                    is_active: bool = Query(
                        None, description="Filter by user active/inactive status"),
                    is_two_factor_auth_enabled: bool = Query(
                        None, description="Filter by two-factor authentication status"),
                    role: str = Query(None, description="Filter by user role"),
                    sort_by: Optional[str] = Query(
                        "created_at", description="Field to sort by"),
                    order: Optional[str] = Query(
                        "asc", description="Sort order ('asc' or 'desc')"),
                    current_user: dict = Depends(user_jwt_required)):
    """
    API endpoint to retrieve all users in a paginated and sorted format.
    Roles with access:
    - User: Cannnot access it.
    - Admin: Can view all user accounts
    """
    try:
        query = {}

        if name:
            query["name__icontains"] = name
        if email:
            query["email__icontains"] = email
        if is_active is not None:
            query["is_active"] = is_active
        if is_two_factor_auth_enabled is not None:
            query["is_two_factor_auth_enabled"] = is_two_factor_auth_enabled
        if role:
            query["roles_in"] = [role]
        if user_ids:
            query["id__in"] = user_ids

        total_users = User.objects(**query).count()
        total_pages = ceil(total_users / page_size)
        skip = (page - 1) * page_size
        sort_order = "+" if order == "asc" else "-"
        sort_criteria = f"{sort_order}{sort_by}"

        users = User.objects(**query).order_by(
            sort_criteria).skip(skip).limit(page_size)
        users_data = [user.to_dict() for user in users]
        response_content = {
            "success": True,
            "message": "Users retrieved successfully.",
            "data": {
                "users": users_data,
                "total_users": total_users,
                "total_pages": total_pages,
                "current_page": page,
            },
        }
        return JSONResponse(status_code=HTTPSSTATUS.HTTP_200_OK, content=response_content)
    except Exception as e:
        logging.error(f"Error retrieving users: {e}")
        response = CommonResponseModel(
            success=False, message="Error retrieving users.")
        return JSONResponse(
            status_code=HTTPSSTATUS.HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict())

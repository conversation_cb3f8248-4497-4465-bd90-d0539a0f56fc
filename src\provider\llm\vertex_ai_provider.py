import os
import base64
import json
import requests
import base64
from google.oauth2 import service_account
from google.auth.transport.requests import Request
from src.constants.env_constant import env_var

class GoogleVertexAIProvider:
    def __init__(self, project_id: str, model_id: str):
        """
        Initializes the Vertex AI provider.
        Loads service account credentials from the environment variable 'GOOGLE_VERTEX_JSON',
        which is expected to be a base64-encoded JSON.
        
        Args:
            project_id (str): Google Cloud project ID.
            model_id (str): The Vertex AI model ID.
        """
        # Load and decode the service account JSON from env variable
        encoded_creds = env_var["GOOGLE_VERTEX_JSON"]
        if not encoded_creds:
            raise ValueError("Environment variable GOOGLE_VERTEX_JSON is not set.")
        json_creds = base64.b64decode(encoded_creds).decode("utf-8")
        creds_info = json.loads(json_creds)

        SCOPES = ["https://www.googleapis.com/auth/cloud-platform"]
        self.credentials = service_account.Credentials.from_service_account_info(creds_info, scopes=SCOPES)
        
        # Refresh immediately to get the initial access token.
        self._refresh_token()
        
        self.project_id = project_id
        self.model_id = model_id
        self.endpoint = (
            f"https://us-central1-aiplatform.googleapis.com/v1/projects/{project_id}/locations/us-central1/publishers/google/models/{model_id}:generateContent"
        )

    def _refresh_token(self):
        """Refreshes the service account token and caches it."""
        request = Request()
        self.credentials.refresh(request)
        self.auth_token = self.credentials.token
        # print("Refreshed token:", self.auth_token[:20] + "...")  # Print a snippet for debugging

    def _ensure_valid_token(self):
        """
        Checks whether the current credentials are valid.
        If not, refresh the token.
        """
        if not self.credentials.valid:
            print("Token not valid. Refreshing token...")
            self._refresh_token()

    def generate_content(self, image_path: str, prompt: str, retry_count: int = 3):
        """
        Sends a request to the Vertex AI generateContent endpoint using inline file data.
        Before each attempt, ensures that the token is valid. If a request returns a 401 error,
        it refreshes the token and retries.

        Args:
            image_path (str): Local path to the file to process.
            prompt (str): Text prompt to guide the model.
            retry_count (int): Number of times to retry if the API call fails.

        Returns:
            dict: Contains the response JSON, a success flag, and an error message (if any).
        """
        # Ensure we have a valid access token before sending the request.
        self._ensure_valid_token()

        # Determine MIME type based on file extension
        extension = os.path.splitext(image_path)[1].lower()
        if extension == ".mp4":
            mime_type = "video/mp4"
        elif extension in [".jpg", ".jpeg"]:
            mime_type = "image/jpeg"
        elif extension == ".png":
            mime_type = "image/png"
        else:
            error_msg = f"Unsupported file format: {extension}"
            print(error_msg)
            return {"response": None, "error": error_msg, "success": False}

        # Read and encode the file
        try:
            with open(image_path, "rb") as file:
                encoded_file = base64.b64encode(file.read()).decode("utf-8")
        except Exception as e:
            error_msg = f"Error reading or encoding the file: {e}"
            print(error_msg)
            return {"response": None, "error": error_msg, "success": False}

        # Prepare the payload.
        payload = {
            "contents": {
                "role": "user",
                "parts": [
                    {
                        "inline_data": {
                            "mimeType": mime_type,
                            "data": encoded_file
                        }
                    },
                    {
                        "text": prompt
                    }
                ]
            }
        }

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.auth_token}"
        }

        # Log payload size for debugging
        payload_json = json.dumps(payload)
        size_in_mb = len(payload_json.encode("utf-8")) / (1024 * 1024)
        print(f"Payload size: {size_in_mb:.2f} MB")

        # Attempt the request with retries
        for attempt in range(retry_count):
            try:
                response = requests.post(self.endpoint, headers=headers, json=payload)
                if response.status_code == 200:
                    try:

                        result_json = response.json()

                        raw_text = result_json["candidates"][0]["content"]["parts"][0]["text"]

                        cleaned_text = raw_text.replace("```json", "").replace("```", "").strip()

                        # Validate that the cleaned text is valid JSON
                        json.loads(cleaned_text)

                        return {"response": result_json, "success": True, "error": ""}
                    
                    except (json.JSONDecodeError, KeyError) as e:
                        print(f"JSON parsing error on attempt {attempt+1}: {e}")

                        if attempt == retry_count - 1:
                            # Last attempt—return success anyway with the raw result
                            return {"response": result_json, "success": True, "error": ""}
                else:
                    print(
                        f"Attempt {attempt + 1} failed with status code {response.status_code}: {response.text}"
                    )
                    # If the error indicates an authentication issue, refresh the token.
                    if response.status_code == 401:
                        print("Received 401 error. Refreshing access token and retrying...")
                        self._refresh_token()
                        headers["Authorization"] = f"Bearer {self.auth_token}"
            except requests.exceptions.RequestException as e:
                print(f"Attempt {attempt + 1} encountered an exception: {e}")
            
            if attempt < retry_count - 1:
                print("Retrying...")

        print("All retry attempts have failed.")
        return {"response": None, "error": "All retry attempts have failed.", "success": False}

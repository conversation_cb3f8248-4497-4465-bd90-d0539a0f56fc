from enum import Enum
from pydantic import BaseModel, Field
from typing import List, Optional
from src.forms.base_form import TextField, YearField, CurrencyField, CheckboxField, FormDetails, ZipCodeField, EINField, DateField

class PlanTypeEnum(str, Enum):
    hsa = "hsa"
    archer_msa = "archer_msa"
    ma_msa = "ma_msa"

class TypeOfPlan(BaseModel):
    plan_type: PlanTypeEnum = Field(...)
    
class TrusteeInformation(BaseModel):
    trustee_name: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    trustee_street_address: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    trustee_city: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    trustee_state: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    trustee_zip_code: ZipCodeField = Field(
        default_factory=lambda: ZipCodeField(type="zipcode"))
    trustee_tin_or_federal_dentification_number: EINField = Field(default_factory=lambda: EINField(type="einfield"))
    type_of_plan_or_account: TypeOfPlan = Field(default_factory=TypeOfPlan)
    
class FinancialFields(BaseModel):

    employee_or_self_employed_person_archer_msa_contributions_made: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    total_contributions_made: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    total_hsa_or_archer_msa_contributions_made: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    rollover_contributions: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    fair_market_value_of_hsa_archer_msa_or_ma_msa: CurrencyField = Field(
        default_factory=lambda: CurrencyField(type="currency"))
    

class AdditionalFields(BaseModel):
    corrected: CheckboxField = Field(
        default_factory=lambda: CheckboxField(type="checkbox"))
    omb_number: TextField = Field(
        default_factory=lambda: TextField(type="text"))
    calendar_year: YearField = Field(
        default_factory=lambda: YearField(type="year"))


# Main Pydantic model for Form 5498 SA
class Form5498SA(BaseModel):
    trustee_information: TrusteeInformation = Field(
        default_factory=TrusteeInformation)
    form_details: FormDetails = Field(default_factory=FormDetails)
    financial_fields: FinancialFields = Field(default_factory=FinancialFields)
    additional_fields: AdditionalFields = Field(
        default_factory=AdditionalFields)

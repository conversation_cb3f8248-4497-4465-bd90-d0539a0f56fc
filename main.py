import os
import re
import logging
from pathlib import Path
from typing import List, Tuple
from bson import ObjectId
import cv2
import pytesseract
import numpy as np
import img2pdf
import src.config.logger
from pdf2image import convert_from_path

from src.constants.constants import (
    NON_MASKED_FOLDER,
    OUTPUTS_FOLDER,
    ADAPTIVE_THRESH_BLOCK_SIZE,
    ADAPTIVE_THRESH_C,
    VERTICAL_KERNEL_RATIO,
    BORDER_SIZE,
    DEFAULT_OUTPUT_PATH_TEMPLATE
)
from src.config.form_config import(
    FORM_IDENTIFIERS,
    NON_IMPORTANT_FORM_TYPES,
    NOT_PRESENT_FORM_IDENTIFIERS,
    FORM_CONFIG
)
from src.models.files import File
from src.constants.enum import QueueProcessingStatus, FormType, ErrorMessage
from src.constants.entities import Cell
from src.models.pages import Page
from src.utils.utility import (
    is_sensitive_label,
    perform_inner_cell_masking,
    draw_table_borders,
    transform_file_path,
    correct_orientation
)


def sort_contours(cnts, hierarchy, method="top-to-bottom"):
    bounding_boxes = [cv2.boundingRect(c) for c in cnts]
    cnts_with_data = list(zip(cnts, bounding_boxes, hierarchy))
    cnts_with_data.sort(key=lambda b: (b[1][1], b[1][0]))
    if cnts_with_data:
        sorted_cnts, sorted_boxes, sorted_hierarchy = zip(*cnts_with_data)
    else:
        sorted_cnts, sorted_boxes, sorted_hierarchy = [], [], []
    return sorted_cnts, sorted_boxes, sorted_hierarchy


def preprocess_text(extracted_text: str) -> str:
    replacements = {
        '‘': '',
        '’': '',
        '“': '',
        '”': '',
    }
    for key, value in replacements.items():
        extracted_text = extracted_text.replace(key, value)
    extracted_text = re.sub(r'[^\x20-\x7E]', '', extracted_text)
    return extracted_text


def preprocess_image(image: np.ndarray) -> np.ndarray:
    os.makedirs(OUTPUTS_FOLDER, exist_ok=True)
    mean_bgr = cv2.mean(image)[:3]
    border_color = tuple(int(c) for c in mean_bgr)
    image_with_border = cv2.copyMakeBorder(
        image,
        top=BORDER_SIZE,
        bottom=BORDER_SIZE,
        left=BORDER_SIZE,
        right=BORDER_SIZE,
        borderType=cv2.BORDER_CONSTANT,
        value=border_color
    )
    if len(image_with_border.shape) == 3:
        gray = cv2.cvtColor(image_with_border, cv2.COLOR_BGR2GRAY)
    else:
        gray = image_with_border
    return gray

# def count_lines(image: np.ndarray, axis: str) -> int:
#     """
#     Counts only table cell border lines (horizontal or vertical) in the given image,
#     while ignoring text lines and non-border artifacts.
#     """

#     # Convert to grayscale if needed
#     if len(image.shape) == 3:
#         image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

#     # Apply adaptive thresholding to create a binary image
#     bin_img = cv2.adaptiveThreshold(
#         ~image, 255, cv2.ADAPTIVE_THRESH_MEAN_C,
#         cv2.THRESH_BINARY, 15, -2
#     )

#     # Define kernels for detecting vertical and horizontal lines
#     kernel_length = max(10, image.shape[1] // 50)  # Ensure a minimum length
#     vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, kernel_length))
#     horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (kernel_length, 1))

#     # Extract vertical and horizontal lines separately
#     if axis == 'vertical':
#         lines_img = cv2.morphologyEx(bin_img, cv2.MORPH_OPEN, vertical_kernel, iterations=2)
#     elif axis == 'horizontal':
#         lines_img = cv2.morphologyEx(bin_img, cv2.MORPH_OPEN, horizontal_kernel, iterations=2)
#     else:
#         raise ValueError("Invalid axis value. Choose either 'horizontal' or 'vertical'.")

#     # Further refine the extracted lines to remove noise
#     lines_img = cv2.dilate(lines_img, np.ones((3, 3), np.uint8), iterations=2)

#     # Use Hough Line Transform to detect strong straight lines
#     edges = cv2.Canny(lines_img, 50, 150, apertureSize=3)
#     lines = cv2.HoughLinesP(edges, 1, np.pi / 180, 100, minLineLength=kernel_length, maxLineGap=5)

#     # Count valid lines that are sufficiently long
#     if lines is not None:
#         return len(lines)
#     return 0  # No significant table border lines found



def preprocess_and_extract_cells(image_path: str) -> Tuple[List[np.ndarray], List[Cell]]:
    img = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    if img is None:
        logging.error("Error: Image not found or unable to read.")
        return [], []

    img_bin = cv2.adaptiveThreshold(
        ~img, 255, cv2.ADAPTIVE_THRESH_MEAN_C,
        cv2.THRESH_BINARY, ADAPTIVE_THRESH_BLOCK_SIZE, ADAPTIVE_THRESH_C
    )

    kernel_length = img.shape[1] // VERTICAL_KERNEL_RATIO
    vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, kernel_length))
    horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (kernel_length, 1))
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))

    # Detect vertical lines
    vertical_lines = cv2.erode(img_bin, vertical_kernel, iterations=2)
    vertical_lines = cv2.dilate(vertical_lines, vertical_kernel, iterations=2)

    # Detect horizontal lines
    horizontal_lines = cv2.erode(img_bin, horizontal_kernel, iterations=2)
    horizontal_lines = cv2.dilate(horizontal_lines, horizontal_kernel, iterations=2)

    # Combine lines
    table_structure = cv2.addWeighted(vertical_lines, 0.5, horizontal_lines, 0.5, 0.0)
    #cv2.imwrite("table_structure0.png", table_structure)
    table_structure = cv2.erode(~table_structure, kernel, iterations=2)
    _, table_structure = cv2.threshold(
        table_structure, 128, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU
    )
    # save table_structure image 
    #cv2.imwrite("table_structure.png", table_structure)

    contours, hierarchy = cv2.findContours(
        table_structure, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE
    )

    if hierarchy is None:
        logging.info("No contours found in the image.")
        return [], []

    hierarchy = hierarchy[0]
    sorted_contours, _, sorted_hierarchy = sort_contours(contours, hierarchy, method="top-to-bottom")

    cell_images = []
    cell_coords = []

    height, width = img.shape[:2]

    for idx, (contour, hier) in enumerate(zip(sorted_contours, sorted_hierarchy)):
        # Skip if contour has children
        if hier[2] != -1:
            continue
        x, y, w, h = cv2.boundingRect(contour)

        if w == width and h == height: 
            continue
        
        # Extract cell image
        cell_img = img[y:y+h, x:x+w]
        cell = Cell(index=idx, bounding_box=(x, y, w, h), image=cell_img)
        #cv2.imwrite(f"cells/f{idx}.png", cell_img)
        cell_images.append(cell.image)
        cell_coords.append(cell)

    logging.info(f"Extracted {len(cell_images)} cells from the table.")
    return cell_images, cell_coords


def any_identifiers_match(identifiers, text, not_present_list):
    for identifier in identifiers:
        if isinstance(identifier, list):
            if all(item.lower() in text for item in identifier):
                if not any(npi.lower() in text for npi in not_present_list):
                    return True
        else:
            if identifier.lower() in text:
                if not any(npi.lower() in text for npi in not_present_list):
                    return True
    return False


def identify_form_type(original_img: np.ndarray, cell_images: List[np.ndarray], cell_coords: List[Cell]) -> str:
    """
    Identify the form type by checking the entire image text first.
    Only if not found, then check cells.
    """
    form_identifiers = FORM_IDENTIFIERS
    not_present_form_identifiers = NOT_PRESENT_FORM_IDENTIFIERS

    # OCR entire image once
    text_from_original_image = pytesseract.image_to_string(original_img, config='--psm 6')
    text_from_original_image = preprocess_text(text_from_original_image).lower()

    # Try identifying form from full image text first
    for f_type, identifiers in form_identifiers.items():
        if any_identifiers_match(identifiers, text_from_original_image, not_present_form_identifiers.get(f_type, [])):
            return f_type

    # If not identified, then only we proceed to check cell contents
    for idx, cell_img in enumerate(cell_images):
        preprocessed_cell_img = preprocess_image(cell_img)
        # Use a simpler config or downscale if needed
        extracted_text_with_cell_img = pytesseract.image_to_string(cell_img, config='--psm 6')
        extracted_text_with_preprocessed_cell_img = pytesseract.image_to_string(preprocessed_cell_img, config='--psm 6')
        extracted_text = extracted_text_with_cell_img if len(extracted_text_with_cell_img) > 0 else extracted_text_with_preprocessed_cell_img
        extracted_text = preprocess_text(extracted_text).lower()

        for f_type, identifiers in form_identifiers.items():
            if any_identifiers_match(identifiers, extracted_text, not_present_form_identifiers.get(f_type, [])):
                # If 1099 composite is identified here, return immediately
                # if f_type == FormType.FORM_1099_COMPOSITE.value:
                #     return f_type
                # Otherwise, keep it as a fallback if no better match found
                return f_type

    return FormType.UNKNOWN.value


def mask_sensitive_cells(image_path: str, file_id: str, page_number: int, folder_name: str, page_form_types, output_path_template: str = DEFAULT_OUTPUT_PATH_TEMPLATE):
    """
    Mask sensitive cells in the image based on form type.

    Args:
        image_path (str): Path to the original image.
        file_id (str): Unique identifier for the file.
        page_number (int): Page number of the document.
        folder_name (str): Folder name where the masked image will be saved.
        output_path_template (str): Path template to save the masked image.
    """
    # Load the original image in color
    try:
        logging.info(f"Processing Page in main.py: {file_id} and {page_number}")
        page=Page.objects(file=ObjectId(file_id), page_index=(page_number+1)).first()
        page.update(status=QueueProcessingStatus.PROCESSING.value)

        admin_provided_non_important_form_type = False
        form_type = FormType.UNKNOWN_BUT_IMPORTANT.value

        original_img = cv2.imread(image_path)
        if original_img is None:
            # handle error
            page.update(
                status=QueueProcessingStatus.ERROR.value,
                form_type=[FormType.UNKNOWN.value], 
                message=ErrorMessage.INTERNAL_SERVER_ERROR.value
            )
            logging.error(f'Could not open image: {image_path}')
            return None, FormType.UNKNOWN.value
        elif len(page_form_types) == 1:
            if page_form_types[0] in NON_IMPORTANT_FORM_TYPES:
                admin_provided_non_important_form_type = True
                page.update(
                    status=QueueProcessingStatus.PROCESSED.value,
                    form_type= page_form_types[0] if isinstance(page_form_types[0], list) else [page_form_types[0]])
                logging.info(f"Skipping document with Page Number: {page_number}, "
                    f"Document ID: {file_id}, File Name: '{image_path}' due to non-important form type.")
                form_type = page_form_types[0]

        elif len(page_form_types) > 1:
            clean_form_type = [form for form in page_form_types if form not in NON_IMPORTANT_FORM_TYPES]

            if len(clean_form_type) != 0:
                page_form_types = clean_form_type  
        
        
        if not admin_provided_non_important_form_type:
        
            correct_orientation(image_path, image_path)
            original_img = cv2.imread(image_path)

            cell_images, cell_coords = preprocess_and_extract_cells(image_path)
            # if not cell_images:
            #     # handle no cells case
            #     if page_form_types[0] == FormType.UNKNOWN_BUT_IMPORTANT.value:
            #         page.update(
            #             status=QueueProcessingStatus.ERROR.value,
            #             form_type=[FormType.UNKNOWN.value], 
            #             message=ErrorMessage.NO_CELLS_DETECTED.value)
            #         logging.warning("No cells were detected. The entire image will not be masked.")
            #         return None, FormType.UNKNOWN.value
            
            
            if len(page_form_types) == 1 and page_form_types[0] == FormType.UNKNOWN_BUT_IMPORTANT.value:
                form_type = identify_form_type(original_img, cell_images, cell_coords)
            else:
                form_type = page_form_types[0]
            
            print("FormType----->", form_type)

            form_config = FORM_CONFIG.get(form_type, None)
            if form_config is None:
                form_type = FormType.UNKNOWN.value

            # Only draw borders if required by config
            if form_config and form_config.draw_border:
                op = transform_file_path(image_path)
                draw_table_borders(image_path, op, form_config)
                original_img = cv2.imread(op)

            # Only perform inner cell masking if required for that form
            require_inner_masking = True  # Or determine from form_config
            for idx, cell_img in enumerate(cell_images):
                cell = cell_coords[idx]
                preprocessed_cell_img = preprocess_image(cell_img)
                #cv2.imwrite(f"cells/{idx}.png",cell_img)
                text = pytesseract.image_to_string(preprocessed_cell_img, config='--psm 6')

                if require_inner_masking:
                    check_inner_cell_mask, masked_cell_image = perform_inner_cell_masking(form_type, preprocessed_cell_img, original_img)
                    if check_inner_cell_mask:
                        x, y, w, h = cell.bounding_box
                        resized_masked_image = cv2.resize(masked_cell_image, (w, h))
                        if len(resized_masked_image.shape) == 2:
                            resized_masked_image = cv2.cvtColor(resized_masked_image, cv2.COLOR_GRAY2BGR)
                        original_img[y:y+h, x:x+w] = resized_masked_image

                # Mask if sensitive
                if is_sensitive_label(form_type, text):
                    print('sensitive image ================>>>>>',idx)
                    #cv2.imwrite(f"cells/sensative_{idx}.png", preprocessed_cell_img)

                    x, y, w, h = cell.bounding_box
                    cv2.rectangle(original_img, (x, y), (x+w, y+h), (255, 255, 255), -1)

            #cv2.imwrite("masked1.png", original_img)

            # Apply post-processing functions
            post_processing_functions = form_config.post_processing
            post_processing_functions_configs = form_config.post_processing_func_config or []
            if len(post_processing_functions) != 0:
                for funcIdx, func in enumerate(post_processing_functions):
                    try:
                        if len(post_processing_functions_configs[funcIdx]) > 0:
                            original_img = func(original_img, post_processing_functions_configs[funcIdx])
                        else:
                            original_img = func(original_img)
                    except Exception as e:
                        page.update(
                            status=QueueProcessingStatus.ERROR.value,
                            form_type=form_type if isinstance(form_type, list) else [form_type], 
                            message=ErrorMessage.INTERNAL_SERVER_ERROR.value)
                        logging.error(f"Error during post-processing: {e}")

        # Save final
        sanitized_form_type = sanitize_form_type(form_type)
        sanitized_input_filename = sanitize_input_filename(image_path)
        # Construct output path using the file_id, page_number, and form_type
        output_path = output_path_template.format(
            folder_name=folder_name,
            file_id=file_id,
            page_number=page_number,
            form_type=sanitized_form_type,
            input_filename=sanitized_input_filename
        )
        os.makedirs(os.path.dirname(output_path), exist_ok=True)


        # Convert the image to grayscale before saving
        if len(original_img.shape) == 3:  # Check if image is in color
            original_img = cv2.cvtColor(original_img, cv2.COLOR_BGR2GRAY)
            
        cv2.imwrite(output_path, original_img)
        logging.info(f"Masked image saved as {output_path}")
        return output_path, form_type
    except Exception as e:
        page.update(
            status=QueueProcessingStatus.ERROR.value,
            form_type=form_type if isinstance(form_type, list) else [form_type], 
            message=ErrorMessage.FAILED_TO_SAVE_MASKED_IMAGE.value
        )
        logging.error(f"Failed to save masked image: {e}")
        return None, form_type


def sanitize_input_filename(input_path: str) -> str:
    input_filename = Path(input_path).stem
    sanitized = re.sub(r'[^\w\-]', '_', input_filename).lower()
    return sanitized


def sanitize_form_type(form_type: str) -> str:
    sanitized = re.sub(r'[^\w\-]', '_', form_type)
    return sanitized


def process(image_path: str, file_id: str, page_number: int, folder_name: str, page_form_types: list):
    # page_form_types -> ["Unknown But Important"], ["Unknown"], ["Instruction"], ["1099-B","1098", ...]
    if image_path is None:
        return None, FormType.UNKNOWN.value
    output_path, form_type = mask_sensitive_cells(image_path, file_id, page_number,folder_name, page_form_types)
    if output_path is None:
        return None, FormType.UNKNOWN.value

    # If needed: call OpenAI OCR here
    # data = send_image_to_openai(form_type, output_path)
    # if data:
    #     try:
    #         ocr_result = json.loads(data)
    #         json_output_file = output_path.replace(".png", ".txt")
    #         with open(json_output_file, "w") as output_file:
    #             output_file.write(json.dumps(ocr_result, indent=2))
    #     except json.JSONDecodeError:
    #         logging.error("Failed to decode JSON from OpenAI.")

    return output_path, form_type


def process_file(input_file_path: str, file_id: str, folder_name: str, if_fallback = False):

    file_doc = File.objects(id=file_id).first()

    try:
        non_masked_folder = os.path.join(NON_MASKED_FOLDER, folder_name)
        output_folder = OUTPUTS_FOLDER

        os.makedirs(non_masked_folder, exist_ok=True)
        os.makedirs(output_folder, exist_ok=True)

        base_file_name = os.path.basename(input_file_path)
        file_name, file_ext = os.path.splitext(base_file_name)
        input_image_paths = []

        file_doc = File.objects(id=file_id).first()
        page_specific_form_types = file_doc.form_types_per_page or {}
        #Default Type of page is ["Unknown But Important"] it can be ["Unknown"], ["Instruction"] or multi form type or single form type

        if if_fallback:
            pages_obj = Page.objects(file=ObjectId(file_id))
            # if_fallback_list = []

        # Convert PDF to images if needed
        if file_ext.lower() == '.pdf':
            pages = convert_from_path(input_file_path, dpi=300)
            for i, page in enumerate(pages):
                if if_fallback:
                    if len(pages_obj) != len(pages):
                        return None, FormType.UNKNOWN.value
                    if pages_obj[i].status != QueueProcessingStatus.QUEUED.value:
                        input_image_paths.append(None)
                        continue
                    image_path = os.path.join(non_masked_folder, f'page_{file_name}_{i}.png')
                    page.save(image_path, 'PNG')
                    input_image_paths.append(image_path)
                    #update pages_obj[i] form_type
                    pages_obj[i].update(form_type=page_specific_form_types.get(str(i + 1), FormType.UNKNOWN.value))

                        
                else:
                    image_path = os.path.join(non_masked_folder, f'page_{file_name}_{i}.png')
                    page.save(image_path, 'PNG')
                    input_image_paths.append(image_path)
                    Page.create(
                        page_index=i+1,
                        form_type=page_specific_form_types.get(str(i + 1), FormType.UNKNOWN.value),
                        message="",
                        status=QueueProcessingStatus.QUEUED.value,
                        file=ObjectId(file_id),
                    )
        else:
            if if_fallback:
                if len(pages_obj) != 1:
                    return None, FormType.UNKNOWN.value
                if pages_obj[0].status != QueueProcessingStatus.QUEUED.value:
                    input_image_paths.append(None)
                else:
                    input_image_paths.append(input_file_path)
                    pages_obj[0].update(form_type=page_specific_form_types.get("1", FormType.UNKNOWN.value))
            else:
                input_image_paths.append(input_file_path)
                Page.create(
                    page_index=1,
                    form_type=page_specific_form_types.get("1", FormType.UNKNOWN.value),
                    message="",
                    status=QueueProcessingStatus.QUEUED.value,
                    file=ObjectId(file_id),
                )

        masked_image_paths = []
        masked_image_form_types = []
        for i, image_path in enumerate(input_image_paths):
            op, form_type = process(image_path, file_id, i, folder_name, page_specific_form_types[str(i+1)])
            masked_image_paths.append(op)
            masked_image_form_types.append(form_type)

        # Combine into PDF if original was PDF
        # if file_ext.lower() == '.pdf' and all(p is not None for p in masked_image_paths):
        pdf_image_paths = []
        for i, mpath in enumerate(masked_image_paths):
            if mpath is None:
                pdf_image_paths.append(input_image_paths[i])
            else:
                pdf_image_paths.append(mpath)

        if all(p is not None for p in pdf_image_paths):
            output_pdf_folder = os.path.join(output_folder, folder_name)
            os.makedirs(output_pdf_folder, exist_ok=True)
            final_form_type = masked_image_form_types[0] if masked_image_form_types else FormType.UNKNOWN.value
            masked_pdf_path = os.path.join(output_pdf_folder, f'{file_id}_{final_form_type}.pdf')
            with open(masked_pdf_path, "wb") as f:
                f.write(img2pdf.convert([p for p in pdf_image_paths if p]))
            logging.info(f"Masked PDF saved as {masked_pdf_path}")
            return masked_image_paths, masked_image_form_types
        else:
            return (masked_image_paths[0], masked_image_form_types[0]) if masked_image_paths else (None, FormType.UNKNOWN.value)
    except Exception as e:
        file_doc.update(
            status=QueueProcessingStatus.ERROR.value,
            message=f"Error processing file: {e}"
        )
        import traceback
        traceback.print_exc()
        logging.error(f"Error processing file {input_file_path}: {e}")
        return None, FormType.UNKNOWN.value
# from src.models.config import init_db
# init_db()

# if __name__ == "__main__":
#     # Input file path (PDF or image)
#     # input_file_path = 'inputs/client sample data/George Albanis/Albanis 2023 Docs.pdf'  # Replace with your input file path
#     input_file_path = 'inputs/1098/2zk1sjf3qnx_WilliamPark 2024 tax docs W2 Mortgage TSP.pdf'  # Replace with your input file path
#     # input_file_path = '678651fa7df22c0b871185a6_0_Form_1099-SSA.png'  # Replace with your input file path
#     process_file(input_file_path,"67b57eb2c4294770a7af3205","test_ss")
